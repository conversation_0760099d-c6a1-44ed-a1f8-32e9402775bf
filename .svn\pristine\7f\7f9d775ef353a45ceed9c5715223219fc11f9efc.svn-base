<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry including="**/*.java" kind="src" output="target/classes" path="src/main/java">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="target/test-classes" path="src/test/java">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.m2e.MAVEN2_CLASSPATH_CONTAINER">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="org.eclipse.jst.component.dependency" value="/WEB-INF/lib"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/itextpdf-5.5.13.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/itext-asian-5.2.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/httpmime-4.5.13.jar"/>
	<classpathentry kind="output" path="target/classes"/>
</classpath>
