<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="sealUseApply" extends="bsp-default" namespace="/cn/com/sinosoft/os/sealuseapply">
		<!-- 查询 -->
		<action name="qrySealUseApply" class="sealUseApplyAction" method="qryParentInput">
			<param name="sessionGroup">sealUseApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapply/qrySealUseApply.jsp
			</result>
		</action>
		<action name="qrySealUseApplyList" class="commonQueryAction">
			<param name="sessionGroup">sealUseApply</param>
			<param name="queryCode">QRY_OS_SEAL_USE_APPLY</param>
			<param name="resultName">qryList</param>
		</action>
		<!-- 查看 -->
		<action name="sealUseApply_viewParent" class="sealUseApplyAction" method="viewParent">
			<param name="sessionGroup">sealUseApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapply/edtSealUseApply.jsp
			</result>
		</action>
		<!-- 添加 -->
		<action name="sealUseApply_addParentInput" class="sealUseApplyAction" method="addParentInput">
			<param name="sessionGroup">sealUseApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapply/edtSealUseApply.jsp
			</result>
		</action>
		<action name="sealUseApply_addParentSubmit" class="sealUseApplyAction" method="addParentSubmit">
			<param name="sessionGroup">sealUseApply</param>
		</action>
		<!-- 修改 -->
		<action name="sealUseApply_edtParentInput" class="sealUseApplyAction" method="edtParentInput">
			<param name="sessionGroup">sealUseApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapply/edtSealUseApply.jsp
			</result>
		</action>
		<action name="sealUseApply_edtParentSubmit" class="sealUseApplyAction" method="edtParentSubmit">
			<param name="sessionGroup">sealUseApply</param>
		</action>
		<!-- 删除 -->
		<action name="sealUseApply_delParentSubmit" class="sealUseApplyAction" method="delParentSubmit">
			<param name="sessionGroup">sealUseApply</param>
		</action>
		
		<!-- 打开审批页面 -->
		<action name="sealUseApply_auditParentInput" class="sealUseApplyAction" method="auditParentInput">
			<param name="sessionGroup">sealUseApply</param>
			<result name="success">
			     /WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapply/auditSealUseApply.jsp
			</result>
		</action>
		<!-- 打开订正页面 -->
		<action name="sealUseApply_auditeitParentInput" class="sealUseApplyAction" method="auditParentInput">
			<param name="sessionGroup">sealUseApply</param>
			<result name="success">
			    /WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapply/edtSealUseApply.jsp
			</result>
		</action>
		
		<!-- 审批 applySubmit-->
		<action name="auditSealUseApplySubmit" class="sealUseApplyAction" method="applySubmit">
			<param name="sessionGroup">sealUseApply</param>
		</action>
		
				<!-- 附件在线编辑-->
		<action name="sealUseApply_OnlineEdit" class="sealUseApplyAction" method="onlineEditInput">
			<param name="sessionGroup">sealUseApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealusemanage/viewForWebOffice.jsp
			</result>
		</action>
		
		<!-- 在线编辑保存提交-->
		<action name="sealUseApply_onlineEditSubmit" class="sealUseApplyAction" method="onlineEditSubmit">
			<param name="sessionGroup">sealUseApply</param>
		</action>
	</package>
</struts>