/*
 FusionCharts JavaScript Library
 Copyright FusionCharts Technologies LLP
 License Information at <http://www.fusioncharts.com/license>

 <AUTHOR> Technologies LLP
 @version fusioncharts/3.2.2-servicerelease1.4200
*/
(function(){var U=FusionCharts(["private","modules.renderer.highcharts-lib"]);if(U!==void 0){var y="",p="0",A=".",w=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,gb=/msie/i.test(navigator.userAgent)&&!window.opera,f=/\s+/g,t=/^#?/,b=/^rgba/i,l=/[#\s]/ig,h=/\{br\}/ig,R=Math.abs,Q=Object.prototype.toString,K=function(b,f){return!b&&b!==!1&&b!==0?f:b},ia=function(){var b,f,u;f=0;for(u=arguments.length;f<u;f+=1)if((b=arguments[f])||!(b!==!1&&
b!==0))return b;return y},E=function(){var b,f,u;f=0;for(u=arguments.length;f<u;f+=1)if((b=arguments[f])||!(b!==!1&&b!==0))return b},fa=function(){var b,f,u;f=0;for(u=arguments.length;f<u;f+=1)if((b=arguments[f])||!(b!==!1&&b!==0))if(!isNaN(b=Number(b)))return b},sa=function(b,f){b=!b&&b!==!1&&b!==0?NaN:Number(b);return isNaN(b)?null:f?R(b):b},oa=function(b){return typeof b==="string"?b.replace(h,"<br />"):y},Oa=function(b,f){var u,l;if(f instanceof Array)for(u=0;u<f.length;u+=1)typeof f[u]!=="object"?
b[u]=f[u]:(typeof b[u]!=="object"&&(b[u]=f[u]instanceof Array?[]:{}),Oa(b[u],f[u]));else for(u in f)typeof f[u]==="object"?(l=Q.call(f[u]),l==="[object Object]"?(typeof b[u]!=="object"&&(b[u]={}),Oa(b[u],f[u])):l==="[object Array]"?(b[u]instanceof Array||(b[u]=[]),Oa(b[u],f[u])):b[u]=f[u]):b[u]=f[u];return b},sb=function(b,f){if(typeof b!=="object"&&typeof f!=="object")return null;typeof f!=="object"&&(f=b,b=void 0);typeof b!=="object"&&(b=f instanceof Array?[]:{});Oa(b,f);return b},Gb=function(b,
f){b=Number(b);b=isNaN(b)?100:b;f!==void 0&&(b=b*f/100);return b%101},Hb=function(b,f,u){var b=b.split(","),l;u!==void 0&&(u=fa(u.split(",")[0]));b[0]=Gb(b[0],u);for(l=1;l<f;l+=1)b[l]=b[0]*Gb(b[l],u)/100;return b.join(",")},Nb=function(f,h,u){var t=0,Qa=0,p=0;u&&u.match(b)&&(u=u.split(","),t=u[0].slice(u[0].indexOf("(")+1),Qa=u[1],p=u[2],!h&&h!==0&&(h=parseInt(u[3].slice(0,u[3].indexOf(")"))*100,10)));if(f)if(f.match(b))u=f.split(","),t=u[0].slice(u[0].indexOf("(")+1),Qa=u[1],p=u[2];else{f=f.replace(l,
y).split(",")[0];switch(f.length){case 3:f=f[0]+f[0]+f[1]+f[1]+f[2]+f[2];break;case 6:break;default:f=(f+"FFFFFF").slice(0,6)}t=parseInt(f.slice(0,2),16);Qa=parseInt(f.slice(2,4),16);p=parseInt(f.slice(4,6),16)}!h&&h!=0&&(h=100);typeof h==="string"&&(h=h.split(",")[0]);h=parseInt(h,10)/100;return"rgba("+t+","+Qa+","+p+","+h+")"},C=function(b,f){f=f<0||f>100?100:f;f/=100;var b=b.replace(l,y),u=parseInt(b,16),h=Math.floor(u/65536),t=Math.floor((u-h*65536)/256);return("000000"+(h*f<<16|t*f<<8|(u-h*65536-
t*256)*f).toString(16)).slice(-6)},J=function(b,f){f=f<0||f>100?100:f;f/=100;var b=b.replace(l,y),u=parseInt(b,16),h=Math.floor(u/65536),t=Math.floor((u-h*65536)/256);return("000000"+(256-(256-h)*f<<16|256-(256-t)*f<<8|256-(256-(u-h*65536-t*256))*f).toString(16)).slice(-6)},M={circle:"circle",triangle:"triangle",square:"square",diamond:"diamond",poly:"poly_"},cb,jb=function(){function b(f){var X;if(f&&f.offsetWidth&&f.offsetHeight){if(f.appendChild)return f.appendChild(X=document.createElement("span")),
X.className="_SmartLabel_Container",X}else if((f=document.getElementsByTagName("body")[0])&&f.appendChild)return X=document.createElement("span"),X.className="_SmartLabel_Container",C+=1,f.appendChild(X),X}function f(b,X,u){u.innerHTML=b;var l,h,t,p=u.offsetWidth;l=X;h=Math.ceil(X/F);if(p<X)return b.length-1;if(h>b.length)l=X-p,h=b.length;for(;l>0;)if(u.innerHTML=b.substr(0,h),l=X-u.offsetWidth,t=Math.floor(l/F))h+=t;else return h;for(h+=t;l<0;)if(u.innerHTML=b.substr(0,h),l=X-u.offsetWidth,t=Math.floor(l/
F))h+=t;else break;return h}function u(f,X,u){if(!(typeof f==="undefined"||typeof f==="object"))this.id=f,typeof X==="string"&&(X=document.getElementById(X)),this.parentContainer=X,this.container=b(X,f),this.showNoEllipses=!u,this.init=!0,this.style={}}var l={font:"font",fontFamily:"font-family","font-family":"font-family",fontWeight:"font-weight","font-weight":"font-weight",fontSize:"font-size","font-size":"font-size",lineHeight:"line-height","line-height":"line-height",textDecoration:"text-decoration",
"text-decoration":"text-decoration",color:"color",whiteSpace:"white-space","white-space":"white-space",padding:"padding",margin:"margin",background:"background",backgroundColor:"background-color","background-color":"background-color",backgroundImage:"background-image","background-image":"background-image",backgroundPosition:"background-position","background-position":"background-position",backgroundPositionLeft:"background-position-left","background-position-left":"background-position-left",backgroundPositionTop:"background-position-top",
"background-position-top":"background-position-top",backgroundRepeat:"background-repeat","background-repeat":"background-repeat",border:"border",borderColor:"border-color","border-color":"border-color",borderStyle:"border-style","border-style":"border-style",borderThickness:"border-thickness","border-thickness":"border-thickness",borderTop:"border-top","border-top":"border-top",borderTopColor:"border-top-color","border-top-color":"border-top-color",borderTopStyle:"border-top-style","border-top-style":"border-top-style",
borderTopThickness:"border-top-thickness","border-top-thickness":"border-top-thickness",borderRight:"border-right","border-right":"border-right",borderRightColor:"border-right-color","border-right-color":"border-right-color",borderRightStyle:"border-right-style","border-right-style":"border-right-style",borderRightThickness:"border-right-thickness","border-right-thickness":"border-right-thickness",borderBottom:"border-bottom","border-bottom":"border-bottom",borderBottomColor:"border-bottom-color",
"border-bottom-color":"border-bottom-color",borderBottomStyle:"border-bottom-style","border-bottom-style":"border-bottom-style",borderBottomThickness:"border-bottom-thickness","border-bottom-thickness":"border-bottom-thickness",borderLeft:"border-left","border-left":"border-left",borderLeftColor:"border-left-color","border-left-color":"border-left-color",borderLeftStyle:"border-left-style","border-left-Style":"border-left-style",borderLeftThickness:"border-left-thickness","border-left-thickness":"border-left-thickness"};
cb=function(){var b=document.createElement("span"),f,X={lineHeight:!0,"line-height":!0},u=function(){return fa(parseInt(b.style.fontSize,10),10)*1.4+"px"};b.innerHTML="fy";f=window.getComputedStyle?function(){return window.getComputedStyle(b,null).lineHeight}:b.currentStyle?function(){return b.currentStyle.lineHeight}:u;return function(h){var t,p="";for(t in h)!X[t]&&l[t]&&(p+=l[t]+" : "+h[t]+";");gb&&!w?b.style.setAttribute("cssText",p):b.setAttribute("style",p);t=f();parseFloat(t)||(t=u());return h.lineHeight=
t}}();var h={position:"absolute",top:"-9999em",whiteSpace:"nowrap"},t=0,X=/\b_SmartLabel\b/,p=/\b_SmartLabelBR\b/,y=/(\<[^\<\>]+?\>)|(&(?:[a-z]+|#[0-9]+);|.)/ig,R=RegExp("<span[^>]+?_SmartLabel[^>]+?>(.*?)</span>","ig"),Aa=/<[^>][^<]*[^>]+>/i,E=0,O=0,F=0,K=0,C=0,Q,M,U;document.getElementsByClassName?(Q="getElementsByClassName",M="_SmartLabel",U=!0):(Q="getElementsByTagName",M="span",U=!1);u.prototype={dispose:function(){var b=this.container,f;if(this.init){if(b&&(f=b.parentNode))f.removeChild(b),
delete this.container;delete this.id;delete this.style;delete this.parentContainer;delete this.showNoEllipses}},useEllipsesOnOverflow:function(b){if(this.init)this.showNoEllipses=!b},getSmartText:function(u,l,h,Qa){if(!this.init)return!1;if(!this.container)this.container=b(this.parentContainer),this.setStyle();var L={text:u,maxWidth:l,maxHeight:h,width:null,height:null,oriTextWidth:null,oriTextHeight:null,oriText:u,isTruncated:!1},w=!1,F,C,J,A=-1,Fa=-1,oa=-1,za=this.container,Ca,fa=[],V=this.showNoEllipses?
"":"...",ia=function(b){for(var b=b.replace(/^\s\s*/,""),f=/\s/,c=b.length;f.test(b.charAt(c-=1)););return b.slice(0,c+1)};if(za){za.innerHTML=u;L.oriTextWidth=J=za.offsetWidth;L.oriTextHeight=w=za.offsetHeight;if(w<=h&&J<=l)return L.width=L.oriTextWidth=J,L.height=L.oriTextHeight=w,L;if(E>h)return L.text="",L.width=L.oriTextWidth=0,L.height=L.oriTextHeight=0,L;u=ia(u).replace(/(\s+)/g," ");w=Aa.test(u);i=0;J=this.showNoEllipses?l:l-t;if(w){u=u.replace(y,'$1<span class="_SmartLabel">$2</span>');u=
u.replace(/(\<br\s*\/*\>)/g,"<span class='_SmartLabel _SmartLabelBR'>$1</span>");za.innerHTML=u;w=za[Q](M);Ca=[];for(var sa=-1,ya=-1,ia=0,A=w.length;ia<A;ia+=1)if(u=w[ia],U||X.test(u.className))if(Fa=u.innerHTML,Fa!=""){if(Fa==" ")ya=Ca.length;else if(Fa=="-")sa=Ca.length;Ca.push({spaceIdx:ya,dashIdx:sa,elem:u});fa.push(Fa)}delete w;i=0;w=Ca.length;O=Ca[0].elem.offsetWidth;if(O>l)return L.text="",L.width=L.oriTextWidth=L.height=L.oriTextHeight=0,L;else O>J&&!this.showNoEllipses&&(J=l-2*K,J>O?V="..":
(J=l-K,J>O?V=".":(J=0,V="")));if(Qa)for(;i<w;i+=1)u=Ca[i].elem,ia=u.offsetLeft+u.offsetWidth,ia>J&&(C||(C=i),za.offsetWidth>l&&(F=i,i=w));else for(;i<w;i+=1)if(u=Ca[i].elem,A=u.offsetHeight+u.offsetTop,ia=u.offsetLeft+u.offsetWidth,Qa=null,ia>J){if(C||(C=i),ia>l)Fa=Ca[i].spaceIdx,A=Ca[i].dashIdx,Fa>oa?(Ca[Fa].elem.innerHTML="<br/>",oa=Fa):A>oa?(Ca[A].elem.innerHTML=A===i?"<br/>-":"-<br/>",oa=A):u.parentNode.insertBefore(Qa=document.createElement("br"),u),u.offsetHeight+u.offsetTop>h?(Qa?Qa.parentNode.removeChild(Qa):
oa===A?Ca[A].elem.innerHTML="-":Ca[Fa].elem.innerHTML=" ",F=i,i=w):C=null}else A>h&&(F=i,i=w);if(F<w){L.isTruncated=!0;C=C?C:F;for(i=w-1;i>=C;i-=1)u=Ca[i].elem,u.parentNode.removeChild(u);for(;i>=0;i-=1)u=Ca[i].elem,p.test(u.className)?u.parentNode.removeChild(u):i=0}L.text=za.innerHTML.replace(R,"$1");if(L.isTruncated)L.text+=V,L.text='<span title="'+fa.join("")+'">'+L.text+"</span>"}else{fa=u.split("");w=fa.length;F="";C=[];za.innerHTML=fa[0];O=za.offsetWidth;if(J>O)C=u.substr(0,f(u,J,za)).split(""),
i=C.length-1;else if(O>l)return L.text="",L.width=L.oriTextWidth=L.height=L.oriTextHeight=0,L;else this.showNoEllipses||(J=l-2*K,J>O?V="..":(J=l-K,J>O?V=".":(J=0,V="")));if(Qa)for(;i<w;i+=1){if(C[i]=fa[i],za.innerHTML=Ca=C.join(""),za.offsetWidth>J&&(F||(F=Ca.substr(0,Ca.length-1)),za.offsetWidth>l))return za.innerHTML=L.text='<span title="'+L.oriText+'">'+ia(F)+V+"</span>",L.width=za.offsetWidth,L.height=za.offsetHeight,L}else for(;i<w;i+=1)if(C[i]=fa[i],za.innerHTML=Ca=C.join(""),za.offsetWidth>
J&&(F||(F=Ca.substr(0,Ca.length-1)),za.offsetWidth>l))if(Fa=u.substr(0,C.length).lastIndexOf(" "),A=u.substr(0,C.length).lastIndexOf("-"),Fa>oa?(C.splice(Fa,1,"<br/>"),oa=Fa,Qa=Fa+1):A>oa?(A===C.length-1?C.splice(A,1,"<br/>-"):C.splice(A,1,"-<br/>"),oa=A,Qa=A+1):(C.splice(C.length-1,1,"<br/>"+fa[i]),Qa=i),za.innerHTML=Ca=C.join(""),za.offsetHeight>h)return L.text=za.innerHTML="<span title='"+L.oriText+"'>"+ia(F)+V+"</span>",L.width=za.offsetWidth,L.height=za.offsetHeight,L;else F=null,A=f(u.substr(Qa),
J,za),C.length<Qa+A&&(C=C.concat(u.substr(C.length,Qa+A-C.length).split("")),i=C.length-1);L.text=za.innerHTML=Ca;L.width=za.offsetWidth;L.height=za.offsetHeight;return L}L.height=za.offsetHeight;L.width=za.offsetWidth}else L.error=Error("Body Tag Missing!");return L},setStyle:function(b){if(!this.init)return!1;if(b!==this.style||this.styleNotSet){if(b)delete this.style,this.style=b;if(this.container){var b=this.container,f=this.style,u;for(u in f)b.style[u]=f[u];for(u in h)b.style[u]=h[u];this.container.innerHTML=
"WgI";F=Math.ceil(this.container.offsetWidth/3);E=this.container.offsetHeight;this.container.innerHTML="...";t=this.container.offsetWidth;this.container.innerHTML=".";K=this.container.offsetWidth;this.styleNotSet=!1}else this.styleNotSet=!0}},getTextSize:function(f,u,X){if(!this.init)return!1;if(!this.container)this.container=b(this.parentContainer),this.setStyle();var l={text:f,width:null,height:null,oriTextWidth:null,oriTextHeight:null,isTruncated:!1},h=this.container;if(h&&(h.innerHTML=f,l.oriTextWidth=
h.offsetWidth,l.oriTextHeight=h.offsetHeight,l.width=Math.min(l.oriTextWidth,u),l.height=Math.min(l.oriTextHeight,X),l.width<l.oriTextWidth||l.height<l.oriTextHeight))l.isTruncated=!0;return l},getOriSize:function(f){if(!this.init)return!1;if(!this.container)this.container=b(this.parentContainer),this.setStyle();var u={text:f,width:null,height:null},X=this.container;if(X)X.innerHTML=f,u.width=X.offsetWidth,u.height=X.offsetHeight;return u}};return u.prototype.constructor=u}(),Ta=function(){var b=
{top:{align:"center",verticalAlign:"top",textAlign:"center"},right:{align:"right",verticalAlign:"middle",textAlign:"left"},bottom:{align:"center",verticalAlign:"bottom",textAlign:"center"},left:{align:"left",verticalAlign:"middle",textAlign:"right"}},f=/([^\,^\s]+)\)$/g,u=function(b,f){var u;if(/^(bar|bar3d)$/.test(b))this.isBar=!0,this.yPos="bottom",this.yOppPos="top",this.xPos="left",this.xOppPos="right";u=parseInt(f.labelstep,10);this.labelStep=u>1?u:1;this.showLabel=fa(f.showlabels,f.shownames,
1);this.is3D=/3d$/.test(b)};u.prototype={isBar:!1,yPos:"left",yOppPos:"right",xPos:"bottom",xOppPos:"top",addAxisGridLine:function(u,l,h,X,t,p,y,w){var C=h===""?!1:!0,O=X>0||p.match(f)[1]>0?!0:!1;if(C||O){O||(p="rgba(0,0,0,0)",X=0.1);l={isGrid:!0,width:X,dashStyle:t,color:p,value:l,zIndex:y===void 0?2:y};if(C)w=u.opposite?w?this.xOppPos:this.yOppPos:w?this.xPos:this.yPos,w=b[w],l.label={text:h,style:u.labels.style,textAlign:w.textAlign,align:w.align,verticalAlign:w.verticalAlign,rotation:0,x:0,y:0};
u.plotLines.push(l)}},addAxisAltGrid:function(b,f){if(!this.is3D){var u=fa(b._lastValue,b.min),X=E(b._altGrid,!1);X&&b.plotBands.push({isGrid:!0,color:b.alternateGridColor,to:f,from:u,zIndex:1});b._lastValue=f;b._altGrid=!X}},addXaxisCat:function(f,u,l,X){l%this.labelStep===0&&(l=f.opposite?this.xOppPos:this.xPos,l=b[l],u={isGrid:!0,width:0.1,color:"rgba(0,0,0,0)",value:u,label:{text:X,style:f.labels.style,textAlign:l.textAlign,align:l.align,verticalAlign:l.verticalAlign,rotation:0,x:0,y:0}},f.plotLines.push(u))},
addVline:function(b,f,u,X){var l=X._FCconf,h=l.isBar,X=X.chart.plotBorderWidth,t=X%2,p=l.divlineStyle,w=oa(f.label),y=Boolean(fa(f.showlabelborder,l.showVLineLabelBorder,1)),xb=Boolean(fa(f.showlabelbackground,1)),C=E(f.labelhalign,h?"left":"center"),R=E(f.labelvalign,h?"middle":"bottom").toLowerCase(),K=fa(f.labelposition,0),J=fa(f.lineposition,0.5),Q=fa(f.alpha,l.vLineAlpha,80),A=E(f.color,l.vLineColor,"333333").replace(/^#?/,"#"),M=E(f.labelbgcolor,l.vLineLabelBgColor,"333333").replace(/^#?/,"#"),
U=A,lb=fa(f.thickness,l.vLineThickness,1),V=lb*0.5,ia=Boolean(Number(E(f.dashed,0))),sa=fa(f.dashlen,5),ya=fa(f.dashgap,2),ja=l.smartLabel,Oa=parseInt(p.fontSize,10)+2,Ra=0,Ta=fa(f.rotatelabel,l.rotateVLineLabels)?270:0,J=J<0||J>1?0.5:J,K=K<0||K>1?0:K;ja.setStyle(p);ja=ja.getOriSize(w);A=Nb(A,Q);if(h){switch(R){case "top":Oa-=ja.height+V+2;break;case "middle":Oa-=ja.height*0.5+1;break;default:Oa+=V}f.labelhalign||(Ra-=ja.width*K)}else{switch(R){case "top":Oa-=ja.height+2+(X||1)*(1-K)+K;break;case "middle":Oa-=
ja.height*0.5+X*(1-K*2);break;default:Oa+=(X-t)*K}switch(C){case "left":Ra+=lb;break;case "right":Ra-=lb+1}}b.plotLines.push({isVline:!0,color:A,width:lb,value:u-1+J,zIndex:!l.is3d&&f.showontop==="1"?5:3,dashStyle:ia?ub(sa,ya,lb):void 0,label:{text:w,align:h?"left":"center",offsetScale:K,rotation:Ta,y:Oa,x:Ra,textAlign:C,style:{color:U,fontSize:p.fontSize,fontFamily:p.fontFamily,lineHeight:p.lineHeight,border:y?"1px solid":void 0,borderColor:y?U:void 0,backgroundColor:xb?M:void 0,backgroundOpacity:xb?
E(f.labelbgalpha,l.vLineLabelBgAlpha)/100:0}}})}};return u.prototype.constructor=u}(),kb=function(){function b(f,u,l){var h;if(u<=0)return String(Math.round(f));if(isNaN(u))return f=f.toString(),f.length>12&&f.indexOf(A)!=-1&&(u=12-f.split(A)[0].length,h=Math.pow(10,u),f=String(Math.round(f*h)/h)),f;h=Math.pow(10,u);f=String(Math.round(f*h)/h);if(l==1){f.indexOf(A)==-1&&(f+=".0");l=f.split(A);u-=l[1].length;for(l=1;l<=u;l++)f+=p}return f}function f(b,u,l){var h=Number(b);if(isNaN(h))return y;var t=
y,p=!1,L=y,w=y,xb=L=0,L=0,xb=b.length;b.indexOf(A)!=-1&&(t=b.substring(b.indexOf(A)+1,b.length),xb=b.indexOf(A));h<0&&(p=!0,L=1);L=b.substring(L,xb);if(L.length>3){b=L.length;for(h=0;h<=b;h++)w=h>2&&(h-1)%3==0?L.charAt(b-h)+l+w:L.charAt(b-h)+w}else w=L;t!=y&&(w=w+u+t);p==!0&&(w="-"+w);return w}var u={formatnumber:"1",formatnumberscale:"1",defaultnumberscale:y,numberscaleunit:["K","M"],numberscalevalue:[1E3,1E3],numberprefix:y,numbersuffix:y,decimals:y,forcedecimals:p,yaxisvaluedecimals:"2",decimalseparator:A,
thousandseparator:",",indecimalseparator:y,inthousandseparator:y,sformatnumber:"1",sformatnumberscale:p,sdefaultnumberscale:y,snumberscaleunit:["K","M"],snumberscalevalue:[1E3,1E3],snumberprefix:y,snumbersuffix:y,sdecimals:"2",sforcedecimals:p,syaxisvaluedecimals:"2",xFormatNumber:p,xFormatNumberScale:p,xDefaultNumberScale:y,xNumberScaleUnit:["K","M"],xNumberScaleValue:[1E3,1E3],xNumberPrefix:y,xNumberSuffix:y},l={mscombidy2d:{formatnumberscale:"1"}},h=function(b,f){var h,t,p,w,y=l[f];typeof y!==
"object"&&(y=u);K(b.numberscaleunit)&&(h=b.numberscaleunit.split(","));K(b.snumberscaleunit)&&(t=b.snumberscaleunit.split(","));K(b.numberscalevalue)&&(p=b.numberscalevalue.split(","));K(b.snumberscalevalue)&&(w=b.snumberscalevalue.split(","));this.paramLabels=h={formatnumber:E(b.formatnumber,y.formatnumber,u.formatnumber),formatnumberscale:E(b.formatnumberscale,y.formatnumberscale,u.formatnumberscale),defaultnumberscale:ia(b.defaultnumberscale,y.defaultnumberscale,u.defaultnumberscale),numberscaleunit:E(h,
y.numberscaleunit,u.numberscaleunit),numberscalevalue:E(p,y.numberscalevalue,u.numberscalevalue),numberprefix:ia(b.numberprefix,y.numberprefix,u.numberprefix),numbersuffix:ia(b.numbersuffix,y.numbersuffix,u.numbersuffix),decimalprecision:parseInt(E(b.decimals,b.decimalprecision,y.decimals,u.decimals,y.decimalprecision,u.decimalprecision),10),forcedecimals:E(b.forcedecimals,y.forcedecimals,u.forcedecimals),decimalseparator:E(b.decimalseparator,y.decimalseparator,u.decimalseparator),thousandseparator:E(b.thousandseparator,
y.thousandseparator,u.thousandseparator),indecimalseparator:ia(b.indecimalseparator,y.indecimalseparator,u.indecimalseparator),inthousandseparator:ia(b.inthousandseparator,y.inthousandseparator,u.inthousandseparator)};this.param1=p={formatnumber:h.formatnumber,formatnumberscale:h.formatnumberscale,defaultnumberscale:h.defaultnumberscale,numberscaleunit:h.numberscaleunit,numberscalevalue:h.numberscalevalue,numberprefix:h.numberprefix,numbersuffix:h.numbersuffix,decimalprecision:parseInt(E(b.yaxisvaluedecimals,
h.decimalprecision)),forcedecimals:E(b.forceyaxisvaluedecimals,h.forcedecimals),decimalseparator:h.decimalseparator,thousandseparator:h.thousandseparator,indecimalseparator:h.indecimalseparator,inthousandseparator:h.inthousandseparator};this.paramX={formatnumber:E(b.xformatnumber,h.formatnumber),formatnumberscale:E(b.xformatnumberscale,h.formatnumberscale),defaultnumberscale:ia(b.xdefaultnumberscale,h.defaultnumberscale),numberscaleunit:E(b.xnumberscaleunit,h.numberscaleunit),numberscalevalue:E(b.xnumberscalevalue,
h.numberscalevalue),numberprefix:E(b.xnumberprefix,h.numberprefix),numbersuffix:E(b.xnumbersuffix,h.numbersuffix),decimalprecision:parseInt(E(b.xaxisvaluedecimals,b.xaxisvaluesdecimals,h.decimalprecision)),forcedecimals:E(b.forcexaxisvaluedecimals,0),decimalseparator:h.decimalseparator,thousandseparator:h.thousandseparator,indecimalseparator:h.indecimalseparator,inthousandseparator:h.inthousandseparator};this.param2={formatnumber:E(b.sformatnumber,u.sformatnumber),formatnumberscale:E(b.sformatnumberscale,
u.sformatnumberscale),defaultnumberscale:ia(b.sdefaultnumberscale,u.sdefaultnumberscale),numberscaleunit:E(t,u.snumberscaleunit),numberscalevalue:E(w,u.snumberscalevalue),numberprefix:ia(b.snumberprefix,u.snumberprefix),numbersuffix:ia(b.snumbersuffix,u.snumbersuffix),decimalprecision:parseInt(E(b.syaxisvaluedecimals,b.sdecimals,u.sdecimals),10),forcedecimals:E(b.forcesyaxisvaluedecimals,b.sforcedecimals,u.sforcedecimals),decimalseparator:E(b.decimalseparator,u.decimalseparator),thousandseparator:E(b.thousandseparator,
u.thousandseparator),indecimalseparator:E(b.indecimalseparator,u.indecimalseparator),inthousandseparator:E(b.inthousandseparator,u.inthousandseparator)};if(/^(bubble|scatter|selectscatter)$/.test(f))p.formatnumber=E(b.yformatnumber,p.formatnumber),p.formatnumberscale=E(b.yformatnumberscale,p.formatnumberscale),p.defaultnumberscale=ia(b.ydefaultnumberscale,p.defaultnumberscale),p.numberscaleunit=E(b.ynumberscaleunit,p.numberscaleunit),p.numberscalevalue=E(b.ynumberscalevalue,p.numberscalevalue),p.numberprefix=
E(b.ynumberprefix,p.numberprefix),p.numbersuffix=E(b.ynumbersuffix,p.numbersuffix),h.formatnumber=E(b.yformatnumber,h.formatnumber),h.formatnumberscale=E(b.yformatnumberscale,h.formatnumberscale),h.defaultnumberscale=ia(b.ydefaultnumberscale,h.defaultnumberscale),h.numberscaleunit=E(b.ynumberscaleunit,h.numberscaleunit),h.numberscalevalue=E(b.ynumberscalevalue,h.numberscalevalue),h.numberprefix=E(b.ynumberprefix,h.numberprefix),h.numbersuffix=E(b.ynumbersuffix,h.numbersuffix);if(/^(mscombidy2d|mscombidy3d)$/.test(f))this.param2.formatnumberscale=
fa(b.sformatnumberscale,"1");if(/^(pie2d|pie3d|doughnut2d|doughnut3d|marimekko|pareto2d|pareto3d)$/.test(f))h.decimalprecision=E(b.decimals,"2")};h.prototype={percentValue:function(u){return f(b(u,this.paramLabels.decimalprecision,this.paramLabels.forcedecimals),this.paramLabels.decimalseparator,this.paramLabels.thousandseparator)+"%"},getCleanValue:function(b,f){var u=b,h=this.paramLabels.indecimalseparator,l=this.paramLabels.inthousandseparator,u=h!==y?u.toString().replace(h,A):u;l&&l.toString&&
(l=l.toString().replace(/(\W)/ig,"\\$1"));u=l!==y?u.toString().replace(RegExp(l,"g"),y):u;b=!isNaN(u=parseFloat(u))&&isFinite(u)?u:NaN;return isNaN(b)?null:f?R(b):b},dataLabels:function(b,f){return t(b,f?this.param2:this.paramLabels)},yAxis:function(b){return t(b,this.param1)},xAxis:function(b){return t(b,this.paramX)},sYAxis:function(b){return t(b,this.param2)}};h.prototype.constructor=h;var t=function(u,h){if(u!==null){var u=Number(u),l=u+y,t;t=h.formatnumberscale==1?h.defaultnumberscale:y;var p;
p=(p=l.split(".")[1])?p.length:h.forcedecimals?"2":y;if(h.formatnumberscale==1){var l=u,L=h.numberscalevalue,w=h.numberscaleunit;t={};var C=h.defaultnumberscale,R=0;if(L.length===w.length)for(R=0;R<L.length;R++)if(L[R]&&Math.abs(Number(l))>=L[R])C=w[R]||y,l=Number(l)/L[R];else break;t.value=l;t.scale=C;u=l=t.value;t=t.scale}h.formatnumber==1&&(l=b(u,E(h.decimalprecision,p),h.forcedecimals),l=f(l,h.decimalseparator,h.thousandseparator));return l=(h.numberprefix||y)+l+t+(h.numbersuffix||y)}};return h}(),
V=function(){var b=function(b,h,l,t,p){b=Math.abs(h-b);h=b/(l+1);f(b/(l+1))>f(t)&&(p&&Number(h)/Number(t)<(t>1?2:0.5)&&(t/=10),h=(Math.floor(h/t)+1)*t,b=h*(l+1));return b},f=function(b){var b=Math.abs(b),b=String(b),f=0,h=b.indexOf(A);h!=-1&&(f=b.length-h-1);return f};return function(h,l,t,p,w,C,R,K){var E,h=isNaN(h)==!0||h==void 0?0.1:h,l=isNaN(l)==!0||l==void 0?0:l;h==l&&h==0&&(h=0.1);var C=typeof C===void 0?!0:C,J=Math.max(Math.floor(Math.log(Math.abs(l))/Math.LN10),Math.floor(Math.log(Math.abs(h))/
Math.LN10));E=Math.pow(10,J);Math.abs(h)/E<2&&Math.abs(l)/E<2&&(J--,E=Math.pow(10,J));J=Math.pow(10,Math.floor(Math.log(h-l)/Math.LN10));h-l>0&&E/J>=10&&(E=J);var J=(Math.floor(h/E)+1)*E,O;l<0?O=-1*(Math.floor(Math.abs(l/E))+1)*E:C?O=0:(O=Math.floor(Math.abs(l/E)-1)*E,O=O<0?0:O);(typeof w===void 0||w)&&h<=0&&(J=0);w=t==null||t==void 0||t==y?!1:!0;C=p==null||p==void 0||p==y||isNaN(Number(p))?!1:!0;h=w==!1||w==!0&&Number(t)<h?J:Number(t);l=C==!1||C==!0&&Number(p)>l?O:Number(p);p=Math.abs(h-l);if(C==
!1&&w==!1&&K==!0)if(h>0&&l<0)for(var K=!1,t=E>10?E/10:E,w=b(l,h,R,t,!1)-(R+1)*t,F,Q,A,U;K==!1;){if(w+=(R+1)*t,!(f(w/(R+1))>f(t)))if(F=w-p,C=w/(R+1),O=Math.min(Math.abs(l),h),J=O==Math.abs(l)?-1:1,R==0)K=!0;else for(U=1;U<=Math.floor((R+1)/2);U++)Q=C*U,!(Q-O>F)&&Q>O&&(A=w-Q,A/C==Math.floor(A/C)&&Q/C==Math.floor(Q/C)&&(p=w,h=J==-1?A:Q,l=J==-1?-Q:-A,K=!0))}else K=b(l,h,R,E,!0),F=K-p,p=K,h>0?h+=F:l-=F;else if(K==!0&&R>0){K=0;for(t=1;;){F=R+K*t;F=F==0?1:F;if(!(f(p/(F+1))>f(E)))break;K=t==-1||K>R?++K:K;
if(K>25){F=0;break}t=K<=R?t*-1:1}R=F}return{Max:h,Min:l,Range:p,interval:E,divGap:(h-l)/(R+1)}}}(),Cb=function(){var b=function(b,f){this.title.y=b.offsetHeight/2;if(f!==void 0)this.title.text=f};b.prototype={chart:{events:{},margin:[0,0,0,0]},credits:{href:"http://www.fusioncharts.com?BS=FCHSEvalMark",text:"FusionCharts",enabled:!0},legend:{enabled:!1},title:{text:"Chart Placeholder",style:{fontFamily:"Verdana",fontSize:"10px",color:"#666666"}},plotOptions:{series:{}},series:[],exporting:{enabled:!1}};
return b.prototype.constructor=b}(),ya={"true":{"true":"bottom","false":"top"},"false":{"true":"top","false":"bottom"}},ja=function(){var b=/^s$/i,f=function(){return{x:this.category,y:this.y,series:this.series,point:this,percentage:this.percentage,total:this.total||this.stackTotal}},h=function(b){var h;h={series:{},chart:this.chart,id:this.id,label:this.label,options:this.options,svgElm:this.svgElm,toolText:b,getLabelConfig:f};return{mouseover:function(b){var f=this.chart.plotLeft,l=this.chart.plotTop;
h.tooltipPos=[E(b.layerX,b.x)-f+20,E(b.layerY,b.y)-l-15];this.chart.tooltip.refresh(h)},mousemove:function(b){var f=this.chart.plotLeft,l=this.chart.plotTop;h.tooltipPos=[E(b.layerX,b.x)-f+20,E(b.layerY,b.y)-l-15];this.chart.tooltip.refresh(h)},mouseout:function(){this.chart.tooltip.hide()}}};return function(f,l,p,w,y,C){var R,K=p.trendStyle,J,Q,F,A,U,M,lb,va,V,ia,sa,ja,Oa=parseInt(K.fontSize,10)/2+2;R=0;for(Q=f.length;R<Q;R+=1)if(f[R].line){J=0;for(F=f[R].line.length;J<F;J+=1)if(A=f[R].line[J],sa=
fa(A.startvalue,A.value,0),ja=fa(A.endvalue,sa),V=C?l:w&&A.parentyaxis&&b.test(A.parentyaxis)?l[1]:l[0],M=V.max,U=V.min,M>=sa&&M>=ja&&U<=sa&&U<=ja){U=A.parentyaxis&&b.test(A.parentyaxis)?A.valueonleft!=="1":A.valueonright==="1";M=Boolean(fa(A.istrendzone,C?1:0));if(lb=oa(E(A.displayvalue,p.numberFormatter[C?"xAxis":"dataLabels"](sa)))){if(U={text:lb,textAlign:y?"center":U?"left":"right",align:y?M?"center":sa<ja?"right":"left":U?"right":"left",verticalAlign:y?"bottom":M?"middle":ya[sa>ja][U],rotation:0,
x:0,y:y?Oa:2,style:sb(K)},lb=E(A.color,"333333"),A.alwaysVisible=M,lb)U.style.color=lb.replace(t,"#")}else U=void 0;va=E(A.tooltext);lb=va!==void 0?h(va):void 0;ia=fa(A.thickness,1);M?V.plotBands.push({isTrend:!0,color:Nb(E(A.color,"333333"),E(A.alpha,40)),from:sa,to:ja,label:U,zIndex:!p.is3d&&A.showontop==="1"?5:3,events:lb,tooltext:va,alwaysVisible:A.alwaysVisible}):V.plotLines.push({isTrend:!0,color:Nb(E(A.color,"333333"),E(A.alpha,99)),value:sa,to:ja,width:ia,dashStyle:A.dashed=="1"?ub(fa(A.dashlen,
5),fa(A.dashgap,2),ia):void 0,label:U,zIndex:!p.is3d&&A.showontop==="1"?5:3,events:lb,tooltext:va})}}}}(),ub=function(b,f,h,l){if(l||l===void 0)return h=h?h:1,fa(b,5)/h+","+fa(f,3)/h},eb=function(){},Ra=function(b,f,h){var l,t=Ra[b];if(!t)t=function(){},t.prototype=h instanceof eb?h:new eb,t.prototype.constructor=t,t=Ra[b]=new t;if(h)t.base=h;t.name=b;for(l in f)switch(typeof f[l]){case "object":if(f[l]instanceof eb){t[l]=f[l][l];break}default:t[l]=f[l];break;case "undefined":delete t[l]}return this instanceof
Ra?(b=function(){},b.prototype=t,b.prototype.constructor=b,new b):t};U.extend(U.hcLib,{BLANKSTRINGPLACEHOLDER:"#BLANK#",BLANKSTRING:y,COLOR_BLACK:"000000",COLOR_WHITE:"FFFFFF",COLOR_TRANSPARENT:"rgba(0,0,0,0)",HASHSTRING:"#",BREAKSTRING:"<br />",STRINGSTRING:"string",OBJECTSTRING:"object",COMMASTRING:",",ZEROSTRING:p,SAMPLESTRING:"Ay0",TESTSTR:"Ag",ONESTRING:"1",DECIMALSTRING:A,STRINGUNDEFINED:"undefined",POSITION_TOP:"top",POSITION_RIGHT:"right",POSITION_BOTTOM:"bottom",POSITION_LEFT:"left",POSITION_CENTER:"center",
POSITION_MIDDLE:"middle",FC_CONFIG_STRING:"_FCconf",HUNDREDSTRING:"100",PXSTRING:"px",COMMASPACE:", ",regex:{stripWhitespace:f,dropHash:t,startsRGBA:b,cleanColorCode:l,breakPlaceholder:h,hexcode:/^#?[0-9a-f]{6}/i},extend2:sb,pluck:E,pluckNumber:fa,pluckFontSize:function(){var b,f,h;f=0;for(h=arguments.length;f<h;f+=1)if((b=arguments[f])||!(b!==!1&&b!==0))if(!isNaN(b=Number(b)))return b<1?1:b;return 1},getValidValue:K,getDefinedColor:function(b,f){return!b&&b!=0&&b!=!1?f:b},getFirstValue:ia,getFirstColor:function(b){b=
b.split(",")[0];b=b.replace(f,y);b==y&&(b="000000");return b.replace(t,"#")},getColorCodeString:function(b,f){var h="",l,t,p=0,w=f.split(",");for(t=w.length;p<t;p+=1)l=w[p].split("-"),l[0].indexOf("dark"),h+=C(b,100-parseInt(l[1],10))+",";return h.substring(0,h.length-1)},pluckColor:function(b){if(K(b))return b=b.split(",")[0],b=b.replace(f,y),b==y&&(b="000000"),b.replace(t,"#")},getFirstAlpha:function(b){b=parseInt(b,10);if(isNaN(b)||b>100||b<0)b=100;return b},parsePointValue:sa,parseUnsafeString:oa,
toPrecision:function(b,f){return b.toPrecision&&b.toPrecision(f)||b},stubFN:function(){},falseFN:function(){return!1},hasSVG:w,getLinkAction:function(b,f){return function(){var h,l,t,p,w;p=window;w=ia(this.link,y);w=E(w,this.options&&this.options.chart&&this.options.chart.link||y,this.series&&this.series.chart&&this.series.chart.options&&this.series.chart.options.chart&&this.series.chart.options.chart.link||y);if(w!==void 0)switch(w=p.decodeURIComponent(w).replace(/^\s+/,y).replace(/\s+$/,y),w.search(/^[a-z]*\s*[\-\:]\s*/i)!==
-1&&(h=w.split(/\s*[\-\:]\s*/)[0].toUpperCase()),h){case "J":w=w.replace(/^j\s*\-/i,"j-");h=w.indexOf("-",2);if(h===-1)try{eval(w.slice(2))}catch(C){}else try{p[w.substr(2,h-2).replace(/\s/g,y)](w.slice(h+1))}catch(R){}break;case "JAVASCRIPT":w=w.replace(/^JAVASCRIPT\s*\:/i,"javascript:");try{eval(w.slice(11))}catch(K){}break;case "N":w.replace(/^n\s*\-/i,"n-");p.open(w.slice(2));break;case "F":w=w.replace(/^f\s*\-/i,"f-");h=w.indexOf("-",2);h!==-1?(l=w.substr(2,h-2))&&p.frames[l]?p.frames[l].location=
w.slice(h+1):p.open(w.slice(h+1),l):p.open(w.slice(2));break;case "P":w=w.replace(/p\s*\-/i,"p-");h=w.indexOf("-",2);l=w.indexOf(",",2);h===-1&&(h=1);t=w.slice(h+1);p.open(t,w.substr(2,l-2),w.substr(l+1,h-l-1)).focus();break;case "NEWCHART":h=w.indexOf("-",9);p=w.substring(9,h).toUpperCase();if(p=="XMLURL")l=w.substring(h+1,w.length);else if(p=="JSONURL")l=w.substring(h+1,w.length);else if(p=="XML"||p=="JSON"){w=w.substring(h+1,w.length);p={chart:{}};FcJSON=b;w=w.toLowerCase();if(FcJSON.linkeddata)for(h=
0;h<FcJSON.linkeddata.length;h+=1)if(FcJSON.linkeddata[h].id.toLowerCase()===w)p=FcJSON.linkeddata[h].linkedchart;l=p;p="JSON"}U.raiseEvent("LinkedChartInvoked",{linkType:p,data:l},f);break;default:p.location.href=w}}},graphics:{parseAlpha:Hb,convertColor:Nb,getDarkColor:C,getLightColor:J,mapSymbolName:function(b){var f=M.circle,b=sa(b);if(b>=3)switch(b){case 3:f=M.triangle;break;case 4:f=M.diamond;break;default:f=M.poly+b}return f},getColumnColor:function(b,f,h,l,t,p,w,y,R){var K,A;K=b.split(",");
A=f.split(",");p=p.split(",");w=w.split(",");R?y={FCcolor:{color:K[0],alpha:A[0]}}:t?(b=K[0],A=A[0],y={FCcolor:{color:C(b,75)+","+J(b,25)+","+C(b,80)+","+J(b,65)+","+C(b,80),alpha:A+","+A+","+A+","+A+","+A,ratio:"0,10,13,57,20",angle:y?"-180":"0"}},p=[C(b,70)]):(f=Hb(f,K.length),y={FCcolor:{color:b,alpha:f,ratio:h,angle:y?180-l:l}});return[y,{FCcolor:{color:p[0],alpha:w[0]}}]},getAngle:function(b,f,h){b=Math.atan(f/b)*180/Math.PI;h==2?b=180-b:h==3?b+=180:h==4&&(b=360-b);return b},parseColor:function(b){return b.replace(l,
y).replace(t,"#")}},setImageDisplayMode:function(b,f,h,l,t,p,w,y){var C=y.width*(l/100),l=y.height*(l/100),y={},R,K=p-t*2;R=w-t*2;var A=function(b,f,h,l,p,w){var u={};switch(b){case "top":u.y=t;break;case "bottom":u.y=w-l-t;break;case "middle":u.y=(w-l)/2}switch(f){case "left":u.x=t;break;case "right":u.x=p-h-t;break;case "middle":u.x=(p-h)/2}return u};switch(b){case "center":y.width=C;y.height=l;y.y=w/2-l/2;y.x=p/2-C/2;break;case "stretch":y.width=p-t*2;y.height=w-t*2;y.y=t;y.x=t;break;case "tile":y.width=
C;y.height=l;y.tileInfo={};y.tileInfo.xCount=b=Math.ceil(K/C);y.tileInfo.yCount=R=Math.ceil(R/l);alignObj=A(f,h,C*b,l*R,p,w);y.y=alignObj.y;y.x=alignObj.x;break;case "fit":b=C/l>K/R?K/C:R/l;y.width=C*b;y.height=l*b;alignObj=A(f,h,y.width,y.height,p,w);y.y=alignObj.y;y.x=alignObj.x;break;case "fill":b=C/l>K/R?R/l:K/C;y.width=C*b;y.height=l*b;alignObj=A(f,h,y.width,y.height,p,w);y.y=alignObj.y;y.x=alignObj.x;break;default:alignObj=A(f,h,C,l,p,w),y.width=C,y.height=l,y.y=alignObj.y,y.x=alignObj.x}return y},
SmartLabelManager:jb,setLineHeight:cb,NumberFormatter:kb,getAxisLimits:V,createTrendLine:ja,getDashStyle:ub,axisLabelAdder:Ta,chartAPI:Ra,createDialog:Cb,defaultPaletteOptions:{bgColor:["CBCBCB,E9E9E9","CFD4BE,F3F5DD","C5DADD,EDFBFE","A86402,FDC16D","FF7CA0,FFD1DD"],bgAngle:[270,270,270,270,270],bgRatio:["0,100","0,100","0,100","0,100","0,100"],bgAlpha:["50,50","60,50","40,20","20,10","30,30"],canvasBgColor:["FFFFFF","FFFFFF","FFFFFF","FFFFFF","FFFFFF"],canvasBgAngle:[0,0,0,0,0],canvasBgAlpha:["100",
"100","100","100","100"],canvasBgRatio:[y,y,y,y,y],canvasBorderColor:["545454","545454","415D6F","845001","68001B"],canvasBorderAlpha:[100,100,100,90,100],showShadow:[0,1,1,1,1],divLineColor:["717170","7B7D6D","92CDD6","965B01","68001B"],divLineAlpha:[40,45,65,40,30],altHGridColor:["EEEEEE","D8DCC5","99C4CD","DEC49C","FEC1D0"],altHGridAlpha:[50,35,10,20,15],altVGridColor:["767575","D8DCC5","99C4CD","DEC49C","FEC1D0"],altVGridAlpha:[10,20,10,15,10],anchorBgColor:["FFFFFF","FFFFFF","FFFFFF","FFFFFF",
"FFFFFF"],toolTipBgColor:["FFFFFF","FFFFFF","FFFFFF","FFFFFF","FFFFFF"],toolTipBorderColor:["545454","545454","415D6F","845001","68001B"],baseFontColor:["555555","60634E","025B6A","A15E01","68001B"],borderColor:["767575","545454","415D6F","845001","68001B"],borderAlpha:[50,50,50,50,50],legendBgColor:["FFFFFF","FFFFFF","FFFFFF","FFFFFF","FFFFFF"],legendBorderColor:["545454","545454","415D6F","845001","D55979"],plotGradientColor:["FFFFFF","FFFFFF","FFFFFF","FFFFFF","FFFFFF"],plotBorderColor:["333333",
"8A8A8A","FFFFFF","FFFFFF","FFFFFF"],plotFillColor:["767575","D8DCC5","99C4CD","DEC49C","FEC1D0"],bgColor3D:["FFFFFF","FFFFFF","FFFFFF","FFFFFF","FFFFFF"],bgAlpha3D:["100","100","100","100","100"],bgAngle3D:[90,90,90,90,90],bgRatio3D:[y,y,y,y,y],canvasBgColor3D:["DDE3D5","D8D8D7","EEDFCA","CFD2D8","FEE8E0"],canvasBaseColor3D:["ACBB99","BCBCBD","C8A06C","96A4AF","FAC7BC"],divLineColor3D:["ACBB99","A4A4A4","BE9B6B","7C8995","D49B8B"],divLineAlpha3D:[100,100,100,100,100],legendBgColor3D:["F0F3ED","F3F3F3",
"F7F0E8","EEF0F2","FEF8F5"],legendBorderColor3D:["C6CFB8","C8C8C8","DFC29C","CFD5DA","FAD1C7"],toolTipbgColor3D:["FFFFFF","FFFFFF","FFFFFF","FFFFFF","FFFFFF"],toolTipBorderColor3D:["49563A","666666","49351D","576373","681C09"],baseFontColor3D:["49563A","4A4A4A","49351D","48505A","681C09"],anchorBgColor3D:["FFFFFF","FFFFFF","FFFFFF","FFFFFF","FFFFFF"]}})}})();
(function(U){U.fn.drag=function(b,f,h){var t=typeof b=="string"?b:"",p=U.isFunction(b)?b:U.isFunction(f)?f:null;t.indexOf("drag")!==0&&(t="drag"+t);h=(b==p?f:h)||{};return p?this.bind(t,h,p):this.trigger(t)};var y=U.event,p="ontouchstart"in document.documentElement,A=p?"touchstart":"mousedown",w=p?"touchmove touchend":"mousemove mouseup",gb=function(b,f){if(!f.touchXY||!b.originalEvent)return b;var h=b.originalEvent.changedTouches||b.originalEvent.touches;h&&h.length&&U.extend(b,h[0]);return b},f=
y.special,t=f.drag={defaults:{which:1,distance:0,not:":input",handle:null,relative:!1,drop:!1,click:!1,touchXY:!0},datakey:"dragdata",livekey:"livedrag",add:function(b){var f=U.data(this,t.datakey),h=b.data||{};f.related+=1;if(!f.live&&b.selector)f.live=!0,y.add(this,"draginit."+t.livekey,t.delegate);U.each(t.defaults,function(b){h[b]!==void 0&&(f[b]=h[b])})},remove:function(){U.data(this,t.datakey).related-=1},setup:function(){if(!U.data(this,t.datakey)){var b=U.extend({related:0},t.defaults);U.data(this,
t.datakey,b);y.add(this,A,t.init,b);this.attachEvent&&this.attachEvent("ondragstart",t.dontstart)}},teardown:function(){U.data(this,t.datakey).related||(U.removeData(this,t.datakey),y.remove(this,A,t.init),y.remove(this,"draginit",t.delegate),t.textselect(!0),this.detachEvent&&this.detachEvent("ondragstart",t.dontstart))},init:function(b){var l=b.data,h;if((h=b.originalEvent?b.originalEvent.changedTouches||b.originalEvent.touches:[])&&h.length){if(h.length>1)return}else if(l.which>0&&b.which!=l.which)return;
if(!U(b.target).is(l.not)&&(!l.handle||U(b.target).closest(l.handle,b.currentTarget).length))if(l.propagates=1,l.interactions=[t.interaction(this,l)],l.target=b.target,l.pageX=b.pageX,l.pageY=b.pageY,l.dragging=null,h=t.hijack(b,"draginit",l),l.propagates){if((h=t.flatten(h))&&h.length)l.interactions=[],U.each(h,function(){l.interactions.push(t.interaction(this,l))});l.propagates=l.interactions.length;l.drop!==!1&&f.drop&&f.drop.handler(b,l);t.textselect(!1);y.add(document,w,t.handler,l);if(!p)return!1}},
interaction:function(b,f){return{drag:b,callback:new t.callback,droppable:[],offset:U(b)[f.relative?"position":"offset"]()||{top:0,left:0}}},handler:function(b){var l=b.data;if(!l.dragging&&(b.type==="mousemove"||b.type==="touchmove")){if(Math.pow(b.pageX-l.pageX,2)+Math.pow(b.pageY-l.pageY,2)<Math.pow(l.distance,2))return;b.target=l.target;t.hijack(b,"dragstart",l);if(l.propagates)l.dragging=!0}switch(b.type){case "touchmove":l.dragging&&(b.preventDefault(),gb(b,l));case "mousemove":if(l.dragging){t.hijack(b,
"drag",l);if(l.propagates){l.drop!==!1&&f.drop&&f.drop.handler(b,l);break}b.type="mouseup"}case "mouseup":case "touchend":if(y.remove(document,w,t.handler),l.dragging&&(l.drop!==!1&&f.drop&&f.drop.handler(b,l),t.hijack(b,"dragend",l)),t.textselect(!0),l.click===!1&&l.dragging)jQuery.event.triggered=!0,setTimeout(function(){jQuery.event.triggered=!1},20),l.dragging=!1}},delegate:function(b){var f=[],h,p=U.data(this,"events")||{};U.each(p.live||[],function(p,w){if(w.preType.indexOf("drag")===0&&(h=
U(b.target).closest(w.selector,b.currentTarget)[0]))y.add(h,w.origType+"."+t.livekey,w.origHandler,w.data),U.inArray(h,f)<0&&f.push(h)});if(!f.length)return!1;return U(f).bind("dragend."+t.livekey,function(){y.remove(this,"."+t.livekey)})},hijack:function(b,f,h,p,w){if(h){var A={event:b.originalEvent,type:b.type},ia=f.indexOf("drop")?"drag":"drop",E,fa=p||0,sa,oa,p=!isNaN(p)?p:h.interactions.length;b.type=f;b.sourceEvent=A.event;b.originalEvent=null;h.results=[];do if((sa=h.interactions[fa])&&!(f!==
"dragend"&&sa.cancelled)){oa=t.properties(b,h,sa);sa.results=[];U(w||sa[ia]||h.droppable).each(function(p,w){E=(oa.target=w)?y.handle.call(w,b,oa):null;if(E===!1){if(ia=="drag")sa.cancelled=!0,h.propagates-=1;f=="drop"&&(sa[ia][p]=null)}else f=="dropinit"&&sa.droppable.push(t.element(E)||w);if(f=="dragstart")sa.proxy=U(t.element(E)||sa.drag)[0];sa.results.push(E);delete b.result;if(f!=="dropinit")return E});h.results[fa]=t.flatten(sa.results);if(f=="dropinit")sa.droppable=t.flatten(sa.droppable);
f=="dragstart"&&!sa.cancelled&&oa.update()}while(++fa<p);b.type=A.type;b.originalEvent=A.event;return t.flatten(h.results)}},properties:function(b,f,h){var p=h.callback;p.drag=h.drag;p.proxy=h.proxy||h.drag;p.startX=f.pageX;p.startY=f.pageY;p.deltaX=b.pageX-f.pageX;p.deltaY=b.pageY-f.pageY;p.originalX=h.offset.left;p.originalY=h.offset.top;p.offsetX=b.pageX-(f.pageX-p.originalX);p.offsetY=b.pageY-(f.pageY-p.originalY);p.drop=t.flatten((h.drop||[]).slice());p.available=t.flatten((h.droppable||[]).slice());
return p},element:function(b){if(b&&(b.jquery||b.nodeType==1))return b},flatten:function(b){return U.map(b,function(b){return b&&b.jquery?U.makeArray(b):b&&b.length?t.flatten(b):b})},textselect:function(b){U(document)[b?"unbind":"bind"]("selectstart",t.dontstart).attr("unselectable",b?"off":"on").css("MozUserSelect",b?"":"none")},dontstart:function(){return!1},callback:function(){}};t.callback.prototype={update:function(){f.drop&&this.available.length&&U.each(this.available,function(b){f.drop.locate(this,
b)})}};f.draginit=f.dragstart=f.dragend=t})(jQuery);
(function(U){function y(p){var w=p||window.event,y=[].slice.call(arguments,1),f=0,t=0,b=0,p=U.event.fix(w);p.type="wheelchange";p.wheelDelta&&(f=p.wheelDelta/120);p.detail&&(f=-p.detail/3);b=f;w.axis!==void 0&&w.axis===w.HORIZONTAL_AXIS&&(b=0,t=-1*f);w.wheelDeltaY!==void 0&&(b=w.wheelDeltaY/120);w.wheelDeltaX!==void 0&&(t=-1*w.wheelDeltaX/120);y.unshift(p,f,t,b);return U.event.handle.apply(this,y)}var p=["DOMMouseScroll","mousewheel"];U.event.special.wheelchange={setup:function(){if(this.addEventListener)for(var A=
p.length;A;)this.addEventListener(p[--A],y,!1);else this.onmousewheel=y},teardown:function(){if(this.removeEventListener)for(var A=p.length;A;)this.removeEventListener(p[--A],y,!1);else this.onmousewheel=null}};U.fn.extend({wheelchange:function(p){return p?this.bind("wheelchange",p):this.trigger("wheelchange")},unwheelchange:function(p){return this.unbind("wheelchange",p)}})})(jQuery);
(function(){var U=FusionCharts(["private","modules.renderer.highcharts-interface"]);if(U!==void 0){var y=U.hcLib,p=U.renderer.getRenderer("javascript"),A=y.loadModule,w=y.moduleCmdQueue,gb=y.executeWaitingCommands,f=y.moduleDependencies,t=y.getDependentModuleName,b=y.eventList={loaded:"FC_Loaded",dataloaded:"FC_DataLoaded",rendered:"FC_Rendered",drawcomplete:"FC_DrawComplete",resized:"FC_Resized",dataxmlinvalid:"FC_DataXMLInvalid",nodatatodisplay:"FC_NoDataToDisplay"};y.raiseEvent=function(f,h,p,
t){var w=b[f];U.raiseEvent(f,h,p);w&&typeof window[w]==="function"&&setTimeout(function(){window[w].apply(window,t)},0)};f.charts=U.extend(f.charts||{},{column2d:0,column3d:0,pie2d:0,pie3d:0,line:0,bar2d:0,area2d:0,doughnut2d:0,doughnut3d:0,pareto2d:0,pareto3d:0,mscolumn2d:0,mscolumn3d:0,msline:0,msarea:0,msbar2d:0,msbar3d:0,stackedcolumn2d:0,marimekko:0,stackedcolumn3d:0,stackedarea2d:0,stackedcolumn2dline:0,stackedcolumn3dline:0,stackedbar2d:0,stackedbar3d:0,msstackedcolumn2d:0,mscombi2d:0,mscombi3d:0,
mscolumnline3d:0,mscombidy2d:0,mscolumn3dlinedy:0,stackedcolumn3dlinedy:0,msstackedcolumn2dlinedy:0,scatter:0,bubble:0,ssgrid:0,scrollcolumn2d:0,scrollcolumn3d:0,scrollline2d:0,scrollarea2d:0,scrollstackedcolumn2d:0,scrollcombi2d:0,scrollcombidy2d:0,zoomline:0});f.powercharts=U.extend(f.powercharts||{},{spline:0,splinearea:0,msspline:0,mssplinearea:0,multiaxisline:0,multilevelpie:0,waterfall2d:0,msstepline:0,inversemsline:0,inversemscolumn2d:0,inversemsarea:0,errorbar2d:0,horizontalerrorbar2d:0,errorscatter:0,
errorline:0,logmsline:0,logmscolumn2d:0,radar:0,dragnode:0,candlestick:0,selectscatter:0,dragcolumn2d:0,dragline:0,dragarea:0,kagi:0});f.widgets=U.extend(f.widgets||{},{angulargauge:0,bulb:0,cylinder:0,drawingpad:0,funnel:0,hbullet:0,hled:0,hlineargauge:0,pyramid:0,realtimearea:0,realtimecolumn:0,realtimeline:0,realtimelinedy:0,realtimestackedarea:0,realtimestackedcolumn:0,sparkcolumn:0,sparkline:0,sparkwinloss:0,thermometer:0,vbullet:0,vled:0});U.extend(p,{render:function(b,f){var A=this.chartType(),
Q=this.jsVars,K=t(A);K.length?y.chartAPI[A]?(this.__state.lastRenderedSrc=this.src,delete Q.waitingModule,U.hcLib.createChart(this,b,A,f),y.raiseEvent("rendered",{},Q.fcObj,[Q.fcObj.id])):(w[K[K.length-1]].push({cmd:"render",obj:this,args:arguments}),Q.waitingModule||(U.hcLib.createChart(this,b,"stub",void 0,Q.msgStore.LoadingText),p.load.apply(this))):U.hcLib.createChart(this,b,"stub",void 0,Q.msgStore.ChartNotSupported)},update:function(b){var f=this.ref,p=this.jsVars;b.error===void 0?(delete p.stallLoad,
delete p.loadError,this.isActive()&&(this.src!==this.__state.lastRenderedSrc?this.render():U.hcLib.createChart(this,p.container,p.type))):(this.isActive()&&typeof f.showChartMessage==="function"&&f.showChartMessage("InvalidXMLText"),delete p.loadError)},resize:function(b){var f=this.ref,p,t=this.jsVars;if(f&&f.resize){if(t.isResizing)t.isResizing=clearTimeout(t.isResizing);t.isResizing=setTimeout(function(){p=U.normalizeCSSDimension(b.width,b.height,f);if(b.width!==void 0)f.style.width=p.width;if(b.height!==
void 0)f.style.height=p.height;f.resize();delete t.isResizing},0)}},dispose:function(){var b,f=this.jsVars,p=f.hcObj||{};f.instanceAPI.dispose&&f.instanceAPI.dispose();(b=this.ref)&&b.parentNode&&b.parentNode.removeChild(b);return p&&p.destroy&&p.destroy()},load:function(){var b=this.jsVars,f=this.chartType(),p=U.hcLib.chartAPI[f],f=t(f),w=f[f.length-1];if(p||!f||f&&f.length===0)delete b.waitingModule;else if(!b.waitingModule)b.waitingModule=!0,A(f,function(){delete b.waitingModule;gb(y.moduleCmdQueue[w]||
[])},function(b){U.raiseError(this,11171116151,"run","HC-interface~renderer.load","Unable to load required modules and resources: "+b)}),b.moduleLoadRequested=!0},config:function(b){var f,p=this.jsVars,t=p.msgStore,p=p.cfgStore;for(f in b)t[f]?t[f]=b[f]:p[f.toLowerCase()]=b[f]}})}})();
(function(){function U(a,d,k,s,b,c,x){var aa=[n],c=yb(typeof c.color==="string"?c.color:c.color.FCcolor.color),e,g,f;f=x.box.tagName&&x.box.tagName.toLowerCase()==="div";x=c.replace(V,"");e=jb(x,40);c=cb(x,60).replace(V,ja);x={FCcolor:{color:x+","+x+","+e+","+x+","+x,ratio:"0,30,30,30,10",angle:0,alpha:"100,100,100,100,100"}};switch(b){case "column":case "column3d":e=parseInt(k*25)/100;g=parseInt(e*50)/100;b=parseInt(s*30)/100;f=parseInt(s*60)/100;aa=aa.concat([a,d+s,D,a,d+b,a+e,d+b,a+e,d+s,a+e+g,
d+s,a+e+g,d,a+e+e+g,d,a+e+e+g,d+s,a+e+e+g+g,d+s,a+e+e+g+g,d+f,a+k,d+f,a+k,d+s,"Z"]);x.FCcolor.angle=270;break;case "bar":case "bar3d":e=k*0.3;g=k*0.6;b=s/4;f=b/2;aa=aa.concat([a,d,D,a+g,d,a+g,d+b,a,d+b,a,d+b+f,a+k,d+b+f,a+k,d+b+f+b,a,d+2*b+f,a,d+2*(b+f),a+e,d+2*(b+f),a+e,d+s,a,d+s,"Z"]);break;case "area":case "area3d":b=s*0.6;f=s*0.2;s*=0.8;aa=aa.concat([a,d+s,D,a,d+b,a+k*0.3,d+f,a+k*0.6,d+b,a+k,d+f,a+k,d+s,"Z"]);x.FCcolor.angle=270;break;case "pie":case "pie3d":e=k/2;g=k*0.7;b=s/2;aa=f?aa.concat([a+
e,d+b,D,a+g,d,"at",a,d,a+k,d+s,a+g,d,a,d+b,D,a+e,d+b,n,a+e,d+b,D,a,d+b,"at",a,d,a+k,d+s,a,d+b,a+g,d+s,D,a+e,d+b,n,a+e,d+b,D,a+g,d+s,"at",a,d,a+k,d+s,a+g,d+s,a+g,d,"Z"]):aa.concat([a+e,d+b,D,a+g,d,"A",e,b,0,0,0,a,d+b,D,a+e,d+b,n,a+e,d+b,D,a,d+b,"A",e,b,0,0,0,a+g,d+s,D,a+e,d+b,n,a+e,d+b,D,a+g,d+s,"A",e+1,b+1,0,0,0,a+g,d,"Z"]);break;default:aa=aa.concat([a,d,D,a+k,d,a+k,d+s,a,d+s,"Z"]),x.FCcolor.angle=270,x.FCcolor.ratio="0,70,30"}return{path:aa,color:x,strokeWidth:0.5,strokeColor:c}}function y(a,d){var k;
a||(a={});for(k in d)a[k]=d[k];return a}function p(a,d){return parseInt(a,d||10)}function A(a){return typeof a==="string"}function w(a){return typeof a==="object"}function gb(a){return typeof a==="number"}function f(a,d){for(var k=a.length;k--;)if(a[k]===d){a.splice(k,1);break}}function t(a){return a!==e&&a!==null}function b(a,d,k){var s,b;if(A(d))t(k)?a.setAttribute(d,k):a&&a.getAttribute&&(b=a.getAttribute(d));else if(t(d)&&w(d))for(s in d)a.setAttribute(s,d[s]);return b}function l(a){if(!a||a.constructor!==
Array)a=[a];return a}function h(){var a=arguments,d,k,s=a.length;for(d=0;d<s;d++)if(k=a[d],typeof k!=="undefined"&&k!==null)return k}function R(a,d){if(Lb&&d&&d.opacity!==e)d.filter="alpha(opacity="+d.opacity*100+")";y(a.style,d)}function Q(a,d,k,s,b){a=Aa.createElement(a);d&&y(a,d);b&&R(a,{padding:0,border:ca,margin:0});k&&R(a,k);s&&s.appendChild(a);return a}function K(a,d){var k=function(){};k.prototype=new a;y(k.prototype,d);return k}function ia(a,d,k,s){var b=Va.lang,c=isNaN(d=va(d))?2:d,d=k===
void 0?b.decimalPoint:k,s=s===void 0?b.thousandsSep:s,b=a<0?"-":"",k=String(p(a=va(+a||0).toFixed(c))),e=k.length>3?k.length%3:0;return b+(e?k.substr(0,e)+s:"")+k.substr(e).replace(/(\d{3})(?=\d)/g,"$1"+s)+(c?d+va(a-k).toFixed(c).slice(2):"")}function E(a){for(var d={left:a.offsetLeft,top:a.offsetTop},a=a.offsetParent;a;)d.left+=a.offsetLeft,d.top+=a.offsetTop,a!==Aa.body&&a!==Aa.documentElement&&(d.left-=a.scrollLeft,d.top-=a.scrollTop),a=a.offsetParent;return d}function fa(){this.symbol=this.color=
0}function sa(a,d){g=h(a,d.animation)}function oa(){var a=Va.global.useUTC;Na=a?Date.UTC:function(a,k,s,b,c,e){return(new Date(a,k,h(s,1),h(b,0),h(c,0),h(e,0))).getTime()};Da=a?"getUTCMinutes":"getMinutes";G=a?"getUTCHours":"getHours";Ka=a?"getUTCDay":"getDay";oc=a?"getUTCDate":"getDate";ua=a?"getUTCMonth":"getMonth";T=a?"getUTCFullYear":"getFullYear";ba=a?"setUTCMinutes":"setMinutes";Qb=a?"setUTCHours":"setHours";vc=a?"setUTCDate":"setDate";Bc=a?"setUTCMonth":"setMonth";Cc=a?"setUTCFullYear":"setFullYear"}
function Oa(a){Ib||(Ib=Q(j));a&&Ib.appendChild(a);Ib.innerHTML=""}function sb(){}function Gb(a,d){function k(a,d){function k(a,d){this.pos=a;this.minor=d;this.isNew=!0;d||this.addLabel()}function s(a){if(a)this.options=a,this.id=a.id;return this}function b(a,d,k){this.isNegative=d;this.options=a;this.x=k;this.alignOptions={align:a.align||(Pa?d?"left":"right":"center"),verticalAlign:a.verticalAlign||(Pa?"middle":d?"bottom":"top"),y:h(a.y,Pa?4:d?14:-6),x:h(a.x,Pa?d?-6:6:0)};this.textAlign=a.textAlign||
(Pa?d?"right":"left":"center")}function I(){var a=[],k=[],s;A=Ia=null;K=[];B(ab,function(I){s=!1;B(["xAxis","yAxis"],function(a){if(I.isCartesian&&(a==="xAxis"&&qa||a==="yAxis"&&!qa)&&(I.options[a]===d.index||I.options[a]===e&&d.index===0))I[a]=z,K.push(I),s=!0});!I.visible&&ta.ignoreHiddenSeries&&(s=!1);if(s){var c,aa,x,ra,g;if(!qa){c=I.options.stacking;pa=c==="percent";if(c)ra=I.type+h(I.options.stack,""),g="-"+ra,I.stackKey=ra,aa=a[ra]||[],a[ra]=aa,x=k[g]||[],k[g]=x;pa&&(A=0,Ia=99)}I.isCartesian&&
(B(I.data,function(a){var k=a.x,s=a.y,I=s<0,e=I?x:aa,f=I?g:ra;A===null&&(A=Ia=a[Tb]);qa?k>Ia?Ia=k:k<A&&(A=k):t(s)&&(c&&(e[k]=t(e[k])?e[k]+s:s),s=e?e[k]:s,a=h(a.low,s),pa||(s>Ia?Ia=s:a<A&&(A=a)),c&&(m[f]||(m[f]={}),m[f][k]||(m[f][k]=new b(d.stackLabels,I,k)),m[f][k].setTotal(s)))}),/(area|column|bar)/.test(I.type)&&!qa&&(A>=0?(A=0,U=!0):Ia<0&&(Ia=0,R=!0)))}})}function aa(a,k){var s,b;L=k?1:O.pow(10,mb(O.log(a)/O.LN10));s=a/L;if(!k&&(k=[1,2,2.5,5,10],d.allowDecimals===!1||w))L===1?k=[1,2,5,10]:L<=0.1&&
(k=[1/L]);for(b=0;b<k.length;b++)if(a=k[b],s<=(k[b]+(k[b+1]||k[b]))/2)break;a*=L;return a}function x(a){var d;d=a;L=h(L,O.pow(10,mb(O.log(ea)/O.LN10)));L<1&&(d=F(1/L)*10,d=F(a*d)/d);return d}function ra(){var k,s,b,I,c=d.tickInterval,e=d.tickPixelInterval;k=d.maxZoom||(qa&&!t(d.min)&&!t(d.max)?nb(a.smallestInterval*5,Ia-A):null);u=ga?Xa:Ua;Z?(b=a[qa?"xAxis":"yAxis"][d.linkedTo],I=b.getExtremes(),Ha=h(I.min,I.dataMin),E=h(I.max,I.dataMax)):(Ha=h(W,d.min,A),E=h(N,d.max,Ia));w&&(Ha=O.log(Ha)/O.LN10,
E=O.log(E)/O.LN10);E-Ha<k&&(I=(k-E+Ha)/2,Ha=na(Ha-I,h(d.min,Ha-I),A),E=nb(Ha+k,h(d.max,Ha+k),Ia));if(!ja&&!pa&&!Z&&t(Ha)&&t(E)){k=E-Ha||1;if(!t(d.min)&&!t(W)&&da&&(A<0||!U))Ha-=k*da;if(!t(d.max)&&!t(N)&&bc&&(Ia>0||!R))E+=k*bc}ea=Ha===E?1:Z&&!c&&e===b.options.tickPixelInterval?b.tickInterval:h(c,ja?1:(E-Ha)*e/u);!P&&!t(d.tickInterval)&&(ea=aa(ea));z.tickInterval=ea;Q=d.minorTickInterval==="auto"&&ea?ea/5:d.minorTickInterval;if(P){$=[];var c=Va.global.useUTC,g=1E3/tb,f=6E4/tb,ma=36E5/tb,e=864E5/tb;
k=6048E5/tb;I=2592E6/tb;var o=31556952E3/tb,j=[["second",g,[1,2,5,10,15,30]],["minute",f,[1,2,5,10,15,30]],["hour",ma,[1,2,3,4,6,8,12]],["day",e,[1,2]],["week",k,[1,2]],["month",I,[1,2,3,4,6]],["year",o,null]],Ga=j[6],n=Ga[1],m=Ga[2];for(b=0;b<j.length;b++)if(Ga=j[b],n=Ga[1],m=Ga[2],j[b+1]&&ea<=(n*m[m.length-1]+j[b+1][1])/2)break;n===o&&ea<5*n&&(m=[1,2,5]);j=aa(ea/n,m);m=new Date(Ha*tb);m.setMilliseconds(0);n>=g&&m.setSeconds(n>=f?0:j*mb(m.getSeconds()/j));if(n>=f)m[ba](n>=ma?0:j*mb(m[Da]()/j));if(n>=
ma)m[Qb](n>=e?0:j*mb(m[G]()/j));if(n>=e)m[vc](n>=I?1:j*mb(m[oc]()/j));n>=I&&(m[Bc](n>=o?0:j*mb(m[ua]()/j)),s=m[T]());n>=o&&(s-=s%j,m[Cc](s));if(n===k)m[vc](m[oc]()-m[Ka]()+d.startOfWeek);b=1;s=m[T]();g=m.getTime()/tb;f=m[ua]();for(ma=m[oc]();g<E&&b<Xa;)$.push(g),n===o?g=Na(s+b*j,0)/tb:n===I?g=Na(s,f+b*j)/tb:!c&&(n===e||n===k)?g=Na(s,f,ma+b*j*(n===e?1:7)):g+=n*j,b++;$.push(g);Sb=d.dateTimeLabelFormats[Ga[0]]}else{b=x(mb(Ha/ea)*ea);s=x(Fa(E/ea)*ea);$=[];for(b=x(b);b<=s;)$.push(b),b=x(b+ea)}if(!Z){if(ja||
qa&&a.hasColumn){s=(ja?1:ea)*0.5;if(ja||!t(h(d.min,W)))Ha-=s;if(ja||!t(h(d.max,N)))E+=s}s=$[0];b=$[$.length-1];d.startOnTick?Ha=s:Ha>s&&$.shift();d.endOnTick?E=b:E<b&&$.pop();Rb||(Rb={x:0,y:0});if(!P&&$.length>Rb[Tb])Rb[Tb]=$.length}}function g(){var a,d;Fb=Ha;ac=E;I();ra();Y=ca;ca=u/(E-Ha||1);if(!qa)for(a in m)for(d in m[a])m[a][d].cum=0;if(!z.isDirty)z.isDirty=Ha!==Fb||E!==ac}function ma(a){a=(new s(a)).render();M.push(a);return a}function o(){var b=d.title,I=d.stackLabels,c=d.alternateGridColor,
aa=d.lineWidth,x,ra,g=a.hasRendered,f=g&&t(Fb)&&!isNaN(Fb);x=J(d.showAlways,K.length&&t(Ha)&&t(E));u=ga?Xa:Ua;ca=u/(E-Ha||1);Eb=ga?Ea:V;if(x||Z){if(Q&&!ja)for(x=Ha+($[0]-Ha)%Q;x<=E;x+=Q)ic[x]||(ic[x]=new k(x,!0)),f&&ic[x].isNew&&ic[x].render(null,!0),ic[x].isActive=!0,ic[x].render();B($,function(a,d){if(!Z||a>=Ha&&a<=E)f&&X[a].isNew&&X[a].render(d,!0),X[a].isActive=!0,X[a].render(d)});c&&B($,function(a,d){if(d%2===0&&a<E)kc[a]||(kc[a]=new s),kc[a].options={from:a,to:$[d+1]!==e?$[d+1]:E,color:c},kc[a].render(),
kc[a].isActive=!0});g||B((d.plotLines||[]).concat(d.plotBands||[]),function(a){M.push((new s(a)).render())})}B([X,ic,kc],function(a){for(var d in a)a[d].isActive?a[d].isActive=!1:(a[d].destroy(),delete a[d])});aa&&(x=Ea+(Ga?Xa:0)+rb,ra=Sa-V-(Ga?Ua:0)+rb,x=wa.crispLine([n,ga?Ea:x,ga?ra:La,D,ga?bb-Mb:x,ga?ra:Sa-V],aa),Ob?Ob.animate({d:x}):Ob=wa.path(x).attr({stroke:d.lineColor,"stroke-width":aa,zIndex:7}).add());z.axisTitle&&(x=ga?Ea:La,aa=p(b.style.fontSize||12),x={low:x+(ga?0:u),middle:x+u/2,high:x+
(ga?u:0)}[b.align],aa=(ga?La+Ua:Ea)+(ga?1:-1)*(Ga?-1:1)*db+(r===2?aa:0),z.axisTitle[g?"animate":"attr"]({x:ga?x:aa+(Ga?Xa:0)+rb+(b.x||0),y:ga?aa-(Ga?Ua:0)+rb:x+(b.y||0)}));if(I&&I.enabled){var ma,j,I=z.stackTotalGroup;if(!I)z.stackTotalGroup=I=wa.g("stack-labels").attr({visibility:q,zIndex:6}).translate(Ea,La).add();for(ma in m)for(j in b=m[ma],b)b[j].render(I)}ma=d.scroll;j=z.scroller;if(ma&&ma.enabled&&(ga?(b=Ea+rb-2,I=Sa-V-(!Ga?0:Ua)+rb+ma.padding-1):(b=Ea+(Ga?Xa:0),I=Sa-V),!j))j=z.scroller=wa.scroller(b,
I,u+4,ma.height,ga,{size:ma.buttonWidth,padding:ma.buttonPadding},!1).attr({fill:ma.color}).setScrollRatio(ma.scrollRatio).setScrollPosition(ma.startPercent).callback(function(){z.scroll.apply(z,arguments)}).add(wb);z.isDirty=!1}function j(a){for(var d=M.length;d--;)M[d].id===a&&M[d].destroy()}var qa=d.isX,Ga=d.opposite,ga=Pa?!qa:qa,r=ga?Ga?0:2:Ga?1:3,m={},d=ka(qa?Ba:wc,[Fc,Gc,Dc,Hc][r],d),z=this,l=d.type,P=l==="datetime",w=l==="logarithmic",rb=d.offset||0,Tb=qa?"x":"y",u,ca,Y,Eb=ga?Ea:V,xa,C,wb,
hc,Ob,A,Ia,K,W,N,E=null,Ha=null,Fb,ac,da=d.minPadding,bc=d.maxPadding,Z=t(d.linkedTo),U,R,pa,l=d.events,la,M=[],ea,Q,L,$,X={},ic={},kc={},Wa,mc,db,Sb,ja=d.categories,lc=d.labels.formatter||function(){var a=this.value;return Sb?c(Sb,a):ea%1E6===0?a/1E6+"M":ea%1E3===0?a/1E3+"k":!ja&&a>=1E3?ia(a,0):a},vb=ga&&d.labels.staggerLines,fb=d.reversed,jc=ja&&d.tickmarkPlacement==="between"?0.5:0;k.prototype={addLabel:function(){var a=this.pos,k=d.labels,s=!(a===Ha&&!h(d.showFirstLabel,1)||a===E&&!h(d.showLastLabel,
0)),b=ja&&ga&&ja.length&&!k.step&&!k.staggerLines&&!k.rotation&&Xa/ja.length||!ga&&Xa/2,I=this.label,a=lc.call({isFirst:a===$[0],isLast:a===$[$.length-1],dateTimeLabelFormat:Sb,value:ja&&ja[a]?ja[a]:a}),b=b&&{width:na(1,F(b-2*(k.padding||10)))+v},b=y(b,k.style);I===e?this.label=t(a)&&s&&k.enabled?wa.text(a,0,0).attr({align:k.align,rotation:k.rotation}).css(b).add(wb):null:I&&I.attr({text:a}).css(b)},getLabelSize:function(){var a=this.label;return a?(this.labelBBox=a.getBBox())[ga?"height":"width"]:
0},render:function(a,k){var s=!this.minor,b=this.label,I=this.pos,c=d.labels,x=this.gridLine,aa=s?d.gridLineWidth:d.minorGridLineWidth,ra=s?d.gridLineColor:d.minorGridLineColor,g=s?d.gridLineDashStyle:d.minorGridLineDashStyle,f=this.mark,ma=s?d.tickLength:d.minorTickLength,j=s?d.tickWidth:d.minorTickWidth||0,o=s?d.tickColor:d.minorTickColor,qa=s?d.tickPosition:d.minorTickPosition,s=c.step,h=k&&Ma||Sa,m;m=ga?xa(I+jc,null,null,k)+Eb:Ea+rb+(Ga?(k&&ob||bb)-Mb-Ea:0);h=ga?h-V+rb-(Ga?Ua:0):h-xa(I+jc,null,
null,k)-Eb;if(aa){I=C(I+jc,aa,k);if(x===e){x={stroke:ra,"stroke-width":aa};if(g)x.dashstyle=g;this.gridLine=x=aa?wa.path(I).attr(x).add(hc):null}x&&I&&x.animate({d:I})}if(j)qa==="inside"&&(ma=-ma),Ga&&(ma=-ma),aa=wa.crispLine([n,m,h,D,m+(ga?0:-ma),h+(ga?ma:0)],j),f?f.animate({d:aa}):this.mark=wa.path(aa).attr({stroke:o,"stroke-width":j}).add(wb);if(b&&!isNaN(m)){m=m+c.x-(jc&&ga?jc*ca*(fb?-1:1):0);h=h+c.y-(jc&&!ga?jc*ca*(fb?1:-1):0);t(c.y)||(h+=p(b.styles.lineHeight)*0.9-b.getBBox().height/2);vb&&
(h+=a/(s||1)%vb*16);if(s)b[a%s?"hide":"show"]();b[this.isNew?"attr":"animate"]({x:m,y:h})}this.isNew=!1},destroy:function(){for(var a in this)this[a]&&this[a].destroy&&this[a].destroy()}};s.prototype={render:function(){var d=this,k=d.options,s=k.label,b=d.label,I=k.width,c=k.to,x,aa=k.from,e=k.dashStyle,ra=d.svgElem,g=[],f,ma,j=k.color;ma=k.zIndex;var o=k.events;d.chart=a;I===0&&s&&t(s.text)&&(I=1,j=Qa);if(I){if(g=C([k.value,J(k.to,k.value)],I),k={stroke:j,"stroke-width":I},e)k.dashstyle=e}else if(t(aa)&&
t(c))aa=na(aa,Ha),c=nb(c,E),x=C(c),(g=C(aa))&&x?(this.options.alwaysVisible&&(ga?x[1]=x[4]+=g[1]===x[1]&&g[4]===x[4]:x[2]=x[5]+=g[2]===x[2]&&g[5]===x[5]),g.push(x[4],x[5],x[1],x[2])):g=null,k={fill:j};else return;if(t(ma))k.zIndex=ma;if(ra)g?ra.animate({d:g},null,ra.onGetPath):(ra.hide(),ra.onGetPath=function(){ra.show()});else if(g&&g.length&&(d.svgElem=ra=wa.path(g).attr(k).add(),o))for(f in e=function(a){ra.on(a,function(k){o[a].apply(d,[k])})},o)e(f);if(s&&t(s.text)&&g&&g.length&&Xa>0&&Ua>0){s=
ka({align:ga&&x&&"center",x:ga?!x&&4:10,verticalAlign:!ga&&x&&"middle",y:ga?x?16:10:x?6:-4,rotation:ga&&!x&&90},s);if(!b)d.label=b=wa.text(s.text,0,0).attr({align:s.textAlign||s.align,rotation:s.rotation,zIndex:ma}).css(s.style).add();f=[g[1],g[4],h(g[6],g[1])];ma=[g[2],g[5],h(g[7],g[2])];g=nb.apply(O,f);x=nb.apply(O,ma);f=na.apply(O,f)-g;ma=na.apply(O,ma)-x;s.offsetScale!==void 0&&(s.offsetScaleIndex!==void 0?(e=(qa?a.yAxis:a.xAxis)[s.offsetScaleIndex],ga?x+=e.translate(s.offsetScale,0,1):g=e.translate(s.offsetScale,
0,1)+g):ga?x+=Ua*s.offsetScale:g+=Xa*s.offsetScale);b.align(s,!1,{x:g,y:x,width:f,height:ma});b.textBound();b.show()}else b&&b.hide();return d},destroy:function(){for(var a in this)this[a]&&this[a].destroy&&this[a].destroy(),delete this[a];f(M,this)}};b.prototype={setTotal:function(a){this.cum=this.total=a},render:function(d){var k=this.options.formatter.call(this);this.label?this.label.attr({text:k,visibility:S}):this.label=a.renderer.text(k,0,0).css(this.options.style).attr({align:this.textAlign,
rotation:this.options.rotation,visibility:S}).add(d)},setOffset:function(d,k){var s=this.isNegative,b=z.translate(this.total),I=z.translate(0),I=va(b-I),c=a.xAxis[0].translate(this.x)+d,x=a.plotHeight,s={x:Pa?s?b:b-I:c,y:Pa?x-c-k:s?x-b-I:x-b,width:Pa?I:k,height:Pa?k:I};this.label&&this.label.align(this.alignOptions,null,s).attr({visibility:q})}};xa=function(a,d,k,s,b,I){var c=1,x=0,e=s?Y:ca,s=s?Fb:Ha;e||(e=ca);k&&(c*=-1,x=u);fb&&(c*=-1,x-=c*u);I?d?(a/=e,w&&b&&(a=O.pow(10,a))):(w&&b&&(a=O.log(a)/O.LN10),
a*=e):d?(fb&&(a=u-a),a=a/e+s,w&&b&&(a=O.pow(10,a))):(w&&b&&(a=O.log(a)/O.LN10),a=c*(a-s)*e+x);return a};C=function(a,d,k){var s,b,I,c,x=k&&Ma||Sa,e=k&&ob||bb,aa;a instanceof Array||(a=[a,a]);I=xa(a[0],null,null,k);c=xa(a[1],null,null,k);a=F(I+Eb);s=F(c+Eb);k=F(x-I-Eb);b=F(x-c-Eb);if(isNaN(I)||isNaN(c))aa=!0;else if(ga){if(k=La,b=x-V,a<Ea||a>Ea+Xa)aa=!0}else if(a=Ea,s=e-Mb,k<La||k>La+Ua)aa=!0;return aa?null:wa.crispLine([n,a,k,D,s,b],d||0)};Pa&&qa&&fb===e&&(fb=!0);y(z,{addPlotBand:ma,addPlotLine:ma,
adjustTickAmount:function(){if(Rb&&!P&&!ja&&!Z){var a=Wa,d=$.length;Wa=Rb[Tb];if(d<Wa){for(;$.length<Wa;)$.push(x($[$.length-1]+ea));ca*=(d-1)/(Wa-1);E=$[$.length-1]}if(t(a)&&Wa!==a)z.isDirty=!0}},categories:ja,getExtremes:function(){return{min:Ha,max:E,dataMin:A,dataMax:Ia,userMin:W,userMax:N}},getPlotLinePath:C,getThreshold:function(a){Ha>a?a=Ha:E<a&&(a=E);return xa(a,0,1)},isXAxis:qa,options:d,plotLinesAndBands:M,getOffset:function(){var a=J(d.showAlways,K.length&&t(Ha)&&t(E)),s=0,b=0,I=d.title,
c=d.labels,x=[-1,1,1,-1][r],e;wb||(wb=wa.g("axis").attr({zIndex:7}).add(),hc=wa.g("grid").attr({zIndex:1}).add());mc=0;if(a||Z)B($,function(a){X[a]?X[a].addLabel():X[a]=new k(a);if(r===0||r===2||{1:"left",3:"right"}[r]===c.align)mc=na(X[a].getLabelSize(),mc)}),vb&&(mc+=(vb-1)*16);else for(e in X)X[e].destroy(),delete X[e];if(I&&I.text){if(!z.axisTitle)z.axisTitle=wa.text(I.text,0,0).attr({zIndex:7,rotation:I.rotation||0,align:I.textAlign||{low:"left",middle:"center",high:"right"}[I.align]}).css(I.style).add();
s=z.axisTitle.getBBox()[ga?"height":"width"];b=h(I.margin,ga?5:10)}rb=x*(d.offset||oa[r]);db=mc+(r!==2&&mc&&x*d.labels[ga?"y":"x"])+b;oa[r]=na(oa[r],db+s+x*rb)},render:o,setCategories:function(d,k){z.categories=ja=d;B(K,function(a){a.translate();a.setTooltipPoints(!0)});z.isDirty=!0;h(k,!0)&&a.redraw()},setExtremes:function(d,k,s,b){s=h(s,!0);Ya(z,"setExtremes",{min:d,max:k},function(){W=d;N=k;s&&a.redraw(b)})},setScale:g,setTickPositions:ra,translate:xa,redraw:function(){ib.resetTracker&&ib.resetTracker();
o();B(M,function(a){a.render()});B(K,function(a){a.isDirty=!0})},removePlotBand:j,removePlotLine:j,reversed:fb,stacks:m,scroll:function(d,k,s){var b=this.options,I=b.scroll,b=b.min,c=I.vxLength,x=I.viewPortMin,d=(I.viewPortMax-x-c)*d+x,I=d+c,e;s===void 0&&(s=!0);ib.resetTracker&&(s?ib.resetTracker():a.tooltip&&a.tooltip.hide());W=d;N=I;sa(h(k,!1),a);Bb&&(Jb||(Rb=null,this.setScale()),B(M,function(a){a.render()}),e=ca*(d-b),B(K,function(a){a.scroll(e,0,s)}));Ya(a,"scroll")}});for(la in l)ha(z,la,l[la]);
g()}function s(){var d={};return{add:function(k,s,b,I){d[k]||(s=wa.text(s,0,0).css(a.toolbar.itemStyle).align({align:"right",x:-Mb-20,y:La+30}).on("click",I).attr({align:"right",zIndex:20}).add(),d[k]=s)},remove:function(a){Oa(d[a].element);d[a]=null}}}function I(a){function d(){var a=this.points||l(this),k=a[0].series.xAxis,s=this.x,k=k&&k.options.type==="datetime",b=A(s)||k,I;I=b?['<span style="font-size: 10px">'+(k?c("%A, %b %e, %Y",s):s)+"</span>"]:[];B(a,function(a){I.push(a.point.tooltipFormatter(b))});
return I.join("<br/>")}function k(a,d){ga=ma?a:(2*ga+a)/3;qa=ma?d:(qa+d)/2;h.translate(ga,qa);qc=va(a-ga)>1||va(d-qa)>1?function(){k(a,d)}:null}function s(){if(!ma){var a=W.hoverPoints;h.hide();B(e,function(a){a&&a.hide()});a&&B(a,function(a){a.setState()});W.hoverPoints=null;ma=!0}}var b,I=a.borderWidth,x=a.crosshairs,e=[],aa=a.style,ra=a.shared,g=p(aa.padding),f=I+g,ma=!0,j,o,ga=0,qa=0,Ga=!1;aa.padding=0;var h=wa.g("tooltip").attr({zIndex:8}).add(),n=wa.rect(f,f,0,0,a.borderRadius,I).attr({fill:a.backgroundColor,
"stroke-width":I}).add(h).shadow(a.shadow,void 0,a.shadow),m=wa.text("",g+f,p(aa.fontSize)+g+f).attr({zIndex:1}).css(aa).add(h);h.hide();return{shared:ra,refresh:function(I){var c,aa,ga,qa=0,z={},r=[];ga=I.tooltipPos;c=a.formatter||d;z=W.hoverPoints;if(Ga)s();else if(ra?(z&&B(z,function(a){a.setState()}),W.hoverPoints=I,B(I,function(a){a.setState(Y);qa+=a.plotY;r.push(a.getLabelConfig())}),aa=I[0].plotX,qa=F(qa)/I.length,z={x:I[0].category},z.points=r,I=I[0]):z=I.getLabelConfig(),z=c.call(z),b=I.series,
aa=ra?aa:I.plotX,qa=ra?qa:I.plotY,c=F(ga?ga[0]:Pa?Xa-qa:aa),aa=F(ga?ga[1]:Pa?Ua-aa:qa),ga=ra||!I.series.isCartesian||cc(c,aa),z===!1||!ga?s():(ma&&(h.show(),ma=!1),m.attr({text:z}),ga=m.getBBox(),j=ga.width+2*g,o=ga.height+2*g,n.attr({width:j,height:o,stroke:a.borderColor||I.color||b.color||"#606060"}),ga=c-j+Ea-25,aa=aa-o+La+10,ga<7&&(ga=Ea+c+15),aa<5?aa=5:aa+o>Sa&&(aa=Sa-o-5),k(F(ga-f),F(aa-f))),x){x=l(x);for(c=x.length;c--;)if(aa=I.series[c?"yAxis":"xAxis"],x[c]&&aa)if(aa=aa.getPlotLinePath(I[c?
"y":"x"],1),e[c])e[c].attr({d:aa,visibility:q});else{ga={"stroke-width":x[c].width||1,stroke:x[c].color||"#C0C0C0",zIndex:2};if(x[c].dashStyle)ga.dashstyle=x[c].dashStyle;e[c]=wa.path(aa).attr(ga).add()}}},hide:s,move:k,block:function(a){(Ga=Boolean(a))&&s()}}}function ra(a,d){function k(a){var d,s=Zb&&Aa.width/Aa.documentElement.clientWidth-1,b,I,c,a=a||hb.event;if(!a.target)a.target=a.srcElement;d=a.touches?a.touches.item(0):a;if(a.type!=="mousemove"||hb.opera||s)Ib=E($),b=Ib.left,I=Ib.top;Lb?(c=
a.x,d=a.y):d.layerX===e?(c=d.pageX-b,d=d.pageY-I):(c=a.layerX,d=a.layerY);s&&(c+=F((s+1)*b-b),d+=F((s+1)*I-I));return y(a,{chartX:c,chartY:d})}function s(a){var d={xAxis:[],yAxis:[]};B(qb,function(k){var s=k.translate,b=k.isXAxis;d[b?"xAxis":"yAxis"].push({axis:k,value:s((Pa?!b:b)?a.chartX-Ea:Ua-a.chartY+La,!0)})});return d}function c(){var d=a.hoverSeries,k=a.hoverPoint;if(k)k.onMouseOut();if(d)d.onMouseOut();Pb&&Pb.hide();dc=null}function x(){if(f){var d={xAxis:[],yAxis:[]},k=f.getBBox(),s=k.x-
Ea,b=k.y-La;d.selectionLeft=s;d.selectionTop=b;d.selectionWidth=k.width;d.selectionHeight=k.height;g&&(B(qb,function(a){var I=a.translate,c=a.isXAxis,x=Pa?!c:c,aa=I(x?s:Ua-b-k.height,!0,0,0,1),I=I(x?s+k.width:Ua-b,!0,0,0,1);d[c?"xAxis":"yAxis"].push({axis:a,min:nb(aa,I),max:na(aa,I)})}),Ya(a,"selection",d,xc));f=f.destroy()}a.mouseIsDown=sb=g=!1;vb(Aa,$a?"touchend":"mouseup",x)}var aa,ra,g,f,ma=ta.zoomType,j=/x/.test(ma),o=/y/.test(ma),ga=j&&!Pa||o&&Pa,qa=o&&!Pa||j&&Pa;Ab=function(){zb?(zb.translate(Ea,
La),Pa&&zb.attr({width:a.plotWidth,height:a.plotHeight}).invert()):a.trackerGroup=zb=wa.g("tracker").attr({zIndex:9}).add()};Ab();if(d.enabled)a.tooltip=Pb=I(d);(function(){var I=!0;$.onmousedown=function(d){d=k(d);!$a&&d.preventDefault&&d.preventDefault();a.mouseIsDown=sb=!0;aa=d.chartX;ra=d.chartY;ha(Aa,$a?"touchend":"mouseup",x)};var e=function(s){if(!s||!(s.touches&&s.touches.length>1)){s=k(s);if(!$a)s.returnValue=!1;var x=s.chartX,e=s.chartY,ma=!cc(x-Ea,e-La);$a&&s.type==="touchstart"&&(b(s.target,
"isTracker")?a.runTrackerClick||s.preventDefault():!tc&&!ma&&s.preventDefault());ma&&(I||c(),x<Ea?x=Ea:x>Ea+Xa&&(x=Ea+Xa),e<La?e=La:e>La+Ua&&(e=La+Ua));if(sb&&s.type!=="touchstart"){if(g=Math.sqrt(Math.pow(aa-x,2)+Math.pow(ra-e,2)),g>10){if(Bb&&(j||o)&&cc(aa-Ea,ra-La))f||(f=wa.rect(Ea,La,ga?1:Xa,qa?1:Ua,0).attr({fill:"rgba(69,114,167,0.25)",zIndex:7}).add());f&&ga&&(x-=aa,f.attr({width:va(x),x:(x>0?0:x)+aa}));f&&qa&&(e-=ra,f.attr({height:va(e),y:(e>0?0:e)+ra}))}}else if(!ma){var h,e=a.hoverPoint,
x=a.hoverSeries,Ga,n,m=bb,z=Pa?s.chartY:s.chartX-Ea;if(Pb&&d.shared){h=[];Ga=ab.length;for(n=0;n<Ga;n++)if(ab[n].visible&&ab[n].tooltipPoints.length)s=ab[n].tooltipPoints[z],s._dist=va(z-s.plotX),m=nb(m,s._dist),h.push(s);for(Ga=h.length;Ga--;)h[Ga]._dist>m&&h.splice(Ga,1);if(h.length&&h[0].plotX!==dc)Pb.refresh(h),dc=h[0].plotX}if(x&&x.tracker&&(s=x.tooltipPoints[z+((Pa?x.yShift:x.xShift)||0)])&&s!==e)s.onMouseOver()}return(I=ma)||!Bb}};$.onmousemove=e;ha($,"mouseleave",c);$.ontouchstart=function(a){if(j||
o)$.onmousedown(a);e(a)};$.ontouchmove=e;$.ontouchend=function(){g&&c()};$.onclick=function(d){var I=a.hoverPoint,d=k(d);d.cancelBubble=!0;if(!g)if(I&&b(d.target,"isTracker")){var c=I.plotX,x=I.plotY;y(I,{pageX:Ib.left+Ea+(Pa?Xa-x:c),pageY:Ib.top+La+(Pa?Ua-c:x)});Ya(I.series,"click",y(d,{point:I}));I.firePointEvent("click",d)}else y(d,s(d)),Ya(a,"click",d);g=!1}})();Yb=setInterval(function(){qc&&qc()},32);y(this,{zoomX:j,zoomY:o,resetTracker:c})}function x(a){var d=a.type||ta.type||ta.defaultSeriesType,
k=Z[d],s=W.hasRendered;if(s)if(Pa&&d==="column")k=Z.bar;else if(!Pa&&d==="bar")k=Z.column;d=new k;d.init(W,a);!s&&d.inverted&&(Pa=!0);if(d.isCartesian)Bb=d.isCartesian;ab.push(d);return d}function aa(){ta.alignTicks!==!1&&B(qb,function(a){a.adjustTickAmount()});Rb=null}function ma(a){var d=W.isDirtyLegend,k,s=W.isDirtyBox,b=ab.length,I=b,c=W.clipRect;for(sa(a,W);I--;)if(a=ab[I],a.isDirty&&a.options.stacking){k=!0;break}if(k)for(I=b;I--;)if(a=ab[I],a.options.stacking)a.isDirty=!0;B(ab,function(a){a.isDirty&&
(a.cleanData(),a.getSegments(),a.options.legendType==="point"&&(d=!0))});if(d&&Hb.renderLegend)Hb.renderLegend(),W.isDirtyLegend=!1;Bb&&(Jb||(Rb=null,B(qb,function(a){a.setScale()})),aa(),pc(),B(qb,function(a){if(a.isDirty||s)a.redraw(),s=!0}));s&&(ec(),Ab(),c&&(ea(c),c.animate({width:W.plotSizeX,height:W.plotSizeY})));B(ab,function(a){a.isDirty&&a.visible&&(!a.isCartesian||a.xAxis)&&a.redraw()});ib&&ib.resetTracker&&ib.resetTracker();Ya(W,"redraw")}function o(){var d=a.xAxis||{},s=a.yAxis||{},b,
d=l(d);B(d,function(a,d){a.index=d;a.isX=!0});s=l(s);B(s,function(a,d){a.index=d});qb=d.concat(s);W.xAxis=[];W.yAxis=[];qb=gc(qb,function(a){b=new k(W,a);W[b.isXAxis?"xAxis":"yAxis"].push(b);return b});aa()}function qa(d,k){db=ka(a.title,d);Sb=ka(a.subtitle,k);B([["title",d,db],["subtitle",k,Sb]],function(a){var d=a[0],k=W[d],s=a[1],a=a[2];k&&s&&(k.destroy(),k=null);a&&a.text&&!k&&(W[d]=wa.text(a.text,0,0).attr({align:a.align,"class":"highcharts-"+d,zIndex:1}).css(a.style).add().align(a,!1,Wa))})}
function Ga(){fb=ta.renderTo;Ja=m+Wb++;A(fb)&&(fb=Aa.getElementById(fb));fb.innerHTML="";fb.offsetWidth||(ja=fb.cloneNode(0),R(ja,{position:z,top:"-9999px",display:""}),Aa.body.appendChild(ja));lc=(ja||fb).offsetWidth;ya=(ja||fb).offsetHeight;W.chartWidth=bb=ta.width||lc||600;W.chartHeight=Sa=ta.height||ya||400;W.container=$=Q(j,{className:"highcharts-container"+(ta.className?" "+ta.className:""),id:Ja},y({position:P,overflow:S,width:bb+v,height:Sa+v,textAlign:"left"},ta.style),ja||fb);W.renderer=
wa=ta.forExport?new rc($,bb,Sa,!0):new $b($,bb,Sa);wa.lighting3D=W.options.chart.use3DLighting;wa.smartLabel=nc.smartLabel;var a,d;za&&$.getBoundingClientRect&&(a=function(){R($,{left:0,top:0});d=$.getBoundingClientRect();R($,{left:-(d.left-p(d.left))+v,top:-(d.top-p(d.top))+v})},a(),ha(hb,"resize",a),ha(W,"destroy",function(){vb(hb,"resize",a)}))}function rb(){function a(){var k=ta.width||fb.offsetWidth,s=ta.height||fb.offsetHeight;if(k&&s){if(k!==lc||s!==ya)clearTimeout(d),d=setTimeout(function(){yc(k,
s,!1)},100);lc=k;ya=s}}var d;ha(hb,"resize",a);ha(W,"destroy",function(){vb(hb,"resize",a)})}function Tb(){var a=this.options.chart.canvasBaseColor3D,d=this.options.chart.canvasBaseDepth,k=this.options.chart.canvasBgDepth,s=this.options.chart.canvasBgColor,b=this.renderer,I=this.options.chart.defaultSeriesType,c=this.xDepth,x=this.yDepth;if(this.options.chart.showCanvasBase)this.base3D=I==="bar3d"?b.rect3d(this.plotLeft-c-d,this.plotTop+x,d,this.plotHeight,c,x,0,"canvasBase3D"):b.rect3d(this.plotLeft-
c,this.plotTop+this.plotHeight+x,this.plotWidth,d,c,x,0,"canvasBase3D"),this.base3D.attr({fill:a,lighting3D:this.options.chart.use3DLighting}).add();if(this.options.chart.showCanvasBg)Za.depth3D=I==="bar3d"?b.path(["M",this.plotLeft,this.plotTop,"L",this.plotLeft+k*1.2,this.plotTop-k,this.plotLeft+this.plotWidth-k,this.plotTop-k,this.plotLeft+this.plotWidth,this.plotTop,"Z"]).attr({fill:s}).add():b.path(["M",this.plotLeft+this.plotWidth,this.plotTop,"L",this.plotLeft+this.plotWidth+k,this.plotTop+
k*1.2,this.plotLeft+this.plotWidth+k,this.plotTop+this.plotHeight-k,this.plotLeft+this.plotWidth,this.plotTop+this.plotHeight,"Z"]).attr({fill:s}).add()}function Eb(){var d=a.labels,k=a.credits,b,I={},c;qa();Hb=W.legend=new Ac(W);pc();B(qb,function(a){a.setTickPositions(!0)});aa();pc();ec();Bb&&B(qb,function(a){a.render()});if(!W.seriesGroup)W.seriesGroup=wa.g("series-group").attr({zIndex:3}).add();B(ab,function(d){d.translate();d.setTooltipPoints();a.chart.is3D?(c=d.type,I[c]||(I[c]=[]),I[c].push(d)):
d.render()});if(a.chart.is3D)W.xDepth=M(a.chart.xDepth,10),W.yDepth=M(a.chart.yDepth,10),Tb.call(W),uc(W,I);d.items&&B(d.items,function(){var a=y(d.style,this.style),k=p(a.left)+Ea,s=p(a.top)+La;delete a.left;delete a.top;a=wa.text(this.html,k,s).attr({zIndex:M(this.zIndex,2),align:J(this.textAlign,"left")}).css(a).add();this.vAlign==="bottom"&&a.attr({y:s-a.getBBox().height});this.vAlign==="middle"&&a.attr({y:s-a.getBBox().height/2})});if(!W.toolbar)W.toolbar=s(W);if(k.enabled&&!W.credits)b=k.href,
wa.text(k.text,0,0).on("click",function(){if(b)location.href=b}).attr({align:k.position.align,zIndex:8}).css(k.style).add().align(k.position);Ab();if(a.subCharts&&a.subCharts.length&&!wa.forExport)W.subCharts=[],B(a.subCharts,function(d){var k=d.chart;k.renderTo=Q(j,null,{position:"relative",background:"transparent",left:k.left+v,top:(Lb&&!Ca?k.top:k.top-Sa)+v,width:k.width+v,height:k.height+v},$);y(d,{instanceAPI:a.instanceAPI});W.subCharts.push(new Gb(d))});W.hasRendered=!0;ja&&(fb.appendChild($),
Oa(ja))}function C(){var a=ab.length,d=$&&$.parentNode;W.subCharts&&W.subCharts.length&&B(W.subCharts,function(a){a.destroy&&a.destroy()});Ya(W,"destroy");vb(hb,"unload",C);vb(W);for(B(qb,function(a){vb(a)});a--;)ab[a].destroy();if($)$.innerHTML="",vb($),d&&d.removeChild($),$=null;if(wa)wa.alignedObjects=null;clearInterval(Yb);for(a in W)delete W[a]}function Ob(){!Ca&&hb==hb.top&&Aa.readyState!=="complete"?Aa.attachEvent("onreadystatechange",function(){Aa.detachEvent("onreadystatechange",Ob);Aa.readyState===
"complete"&&Ob()}):(nc.hcInstance=W,Ga(),fc(),zc(),B(a.series||[],function(a){x(a)}),W.inverted=Pa=h(Pa,a.chart.inverted),o(),W.render=Eb,W.tracker=ib=new ra(W,a.tooltip),Eb(),Nb.raiseEvent("internal.hc.rendered",nc,nc.chartInstance),Ya(W,"load"),d&&d.apply(W,[W]),B(W.callbacks,function(a){a.apply(W,[W])}))}Ba=ka(Ba,Va.xAxis);wc=ka(wc,Va.yAxis);Va.xAxis=Va.yAxis=null;var a=ka(Va,a),ta=a.chart,Ia=ta.margin,Ia=w(Ia)?Ia:[Ia,Ia,Ia,Ia],wb=h(ta.marginTop,Ia[0]),K=h(ta.marginRight,Ia[1]),Fb=h(ta.marginBottom,
Ia[2]),ac=h(ta.marginLeft,Ia[3]),bc=ta.spacingTop,da=ta.spacingRight,pa=ta.spacingBottom,X=ta.spacingLeft,Wa,db,Sb,La,Mb,V,Ea,oa,fb,ja,$,Ja,lc,ya,bb,Sa,ob,Ma,sc,Za,Ta,nc=a.instanceAPI,Db,Xb,jb,gb,cb,Ub,W=this,tc=(Ia=ta.events)&&!!Ia.click,pb,cc,Pb,sb,kb,Cb,yb,Ua,Xa,ib,zb,Ab,Hb,Vb,Kb,Ib,Bb=ta.showAxes,Jb=0,qb=[],Rb,ab=[],Pa,wa,qc,Yb,dc,ec,pc,fc,zc,yc,xc,Ec,Ac=function(a){function d(a){var k=a.pageX+S.dragOffsetX,a=a.pageY+S.dragOffsetY;if(k<S.movementBoundaryOrigin)k=S.movementBoundaryOrigin;else if(k>
S.movementBoundaryX)k=S.movementBoundaryX;if(a<S.movementBoundaryOrigin)a=S.movementBoundaryOrigin;else if(a>S.movementBoundaryY)a=S.movementBoundaryY;S.translate(k,a)}function k(a,d){var s=a.legendItem,b=a.legendLine,I=a.legendSymbol,c=o.color,aa=d?x.itemStyle.color:c,ra=d?a.color:c,g=a.customSymbol,c=d?a.pointAttr[xa]:{stroke:c,fill:c};s&&s.css({fill:aa});b&&b.attr({stroke:ra,"stroke-width":2,"stroke-opacity":1});I&&(I.attr(c),I.attr({r:Fa(e/(b?4:2)),"stroke-width":1,"stroke-opacity":1,"fill-opacity":1}));
g&&g.attr(d?g.symbolAttr:c)}function s(a,d,k){var b=a.legendItem,I=a.legendLine,x=a.legendSymbol,c=a.checkbox,e=a.customSymbol,aa=a.legendLabelHeight;b&&b.align({x:d,y:k,height:aa,verticalAlign:"middle"},!0,{height:a.legendItemHeight});I&&I.translate(d,k);x&&x.attr({x:d+w,y:k+u});e&&a.customSymbol.translate(d,k);if(c)c.x=d,c.y=k}function b(){B(ra,function(a){var d=a.checkbox,k=Ka.alignAttr;d&&R(d,{left:k.translateX+a.legendItemWidth+d.x-40+v,top:k.translateY+d.y-11+v})})}function I(a){var d,b,c,ra=
a.legendItem,f=a.series||a;d=f.options;if(!ra){c=/^(bar|pie|area|column|column3d|bar3d|floatedcolumn|radar)$/.test(f.type);a.legendItem=ra=wa.text(x.labelFormatter.call(a),0,0).css(a.visible?ma:o).on("mouseover",function(){a.setState(Y);ra.css(j)}).on("mouseout",function(){ra.css(a.visible?ma:o);a.setState()}).on("click",function(){var d=function(){a.setVisible(void 0,!1)};if(Ka.dragActive)return delete Ka.dragActive,!1;a.firePointEvent?a.firePointEvent("legendItemClick",null,d):Ya(a,"legendItemClick",
null,d)}).attr({zIndex:2}).add(Ka);if(!c&&d&&d.lineWidth){var qa={"stroke-width":d.lineWidth,zIndex:2};if(d.dashStyle)qa.dashstyle=d.dashStyle;a.legendLine=wa.path([n,t,u,D,t+e,u]).attr(qa).add(Ka)}c?(c=U(t,rb,e,e,f.type,a,wa),a.customSymbol=wa.path(c.path).attr({"stroke-width":c.strokeWidth,zIndex:3}).add(Ka),a.customSymbol.symbolAttr={stroke:c.strokeColor,fill:c.color}):d&&d.marker&&d.marker.enabled&&(b=wa.symbol(a.symbol,w,u,e/2).attr({zIndex:3}).add(Ka));a.legendSymbol=b;k(a,a.visible);if(b=b||
a.customSymbol)b.css(x.symbolStyle).on("click",function(){Ya(ra.element,"click")});if(d&&d.showCheckbox)a.checkbox=Q("input",{type:"checkbox",checked:a.selected,defaultChecked:a.selected},x.itemCheckboxStyle,$),ha(a.checkbox,"click",function(d){Ya(a,"checkboxClick",{checked:d.target.checked},function(){a.select()})})}d=ra.getBBox();b=a.legendItemWidth=x.itemWidth||e+g+d.width+h;N=a.legendItemHeight=na(d.height,wb);a.legendLabelHeight=d.height;a.symbolSize=e;a.lineHeight=wb;if(aa)if(wb&&G){for(;Eb[ta]===
!0;)ta+=1;hc=mb(ta/G);E=ta%G;hc>C&&(K+=wb*(hc-C),C=hc);Da=q+E*b;Eb[ta]=!0;Ha=N/wb;Ob=ta;for(A=0;A<Ha;A+=1,Ob+=G)Eb[Ob]=!0}else if(Da-q+b>(ua||bb-2*ga-q))Da=q,K+=N;W=K;s(a,Da,K);aa?Da+=b:K+=N;Fb=ua||na(aa?Da-q:b,Fb)}function c(){var k=T*0.5;Da=q;K=m;W=Fb=0;if(!Ka){var s,e,g;l&&l.enabled?(S=wa.g("legend-box").attr({zIndex:10}).add(),s=qa+k,e=Z=ua-l.scrollBarWidth-l.scrollBarPadding,g=Ia-qa-T,s=wa.clipRect(0,s,e,g),Tb=wa.g("legend-container").attr({zIndex:1}).clip(s).add(S),Ka=wa.g("legend").add(Tb),
wa.scroller(e+qa-k,k,10,g+1,!1,!1,!1,S).attr({zIndex:3,fill:x.legendScrollBgColor}).setScrollRatio((g+ga)/x.totalHeight).callback(function(a){Ka.translate(0,(g-x.totalHeight)*a)}).add(S)):Tb=S=Ka=wa.g("legend").attr({zIndex:10}).add();x.legendAllowDrag&&(S.css({cursor:"move"}),ha(S.element,"dragstart dragend",function(a){S.movementBoundaryOrigin=T||0;S.movementBoundaryX=bb-ua-S.movementBoundaryOrigin;S.movementBoundaryY=Sa-Ia-S.movementBoundaryOrigin;S.dragOffsetX=S.attr("translateX")-a.pageX;S.dragOffsetY=
S.attr("translateY")-a.pageY;S.dragActive=!0;Pb&&Pb.block(a.type==="dragstart")}),ha(S.element,"drag",d))}x.title&&x.title.text!==ub&&(P=wa.text(x.title.text,0,0).css(x.title.style).attr({zIndex:2,align:"center"}).add(Ka),k=P.getBBox(),P.align({align:"center",verticalAlign:"top",y:K},!1,{x:0,y:0,width:Z,height:k.height}),K+=(k.height||0)+z);ra=[];B(ba,function(a){var d=a.options;d.showInLegend&&(d.legendType==="point"?B(a.data,function(a){a.showInLegend&&(ra=ra.concat(a))}):ra=ra.concat(a))});ra.sort(function(a,
d){return(a.options.legendIndex||0)-(d.options.legendIndex||0)});ac&&ra.reverse();B(ra,I);Vb=ua||Fb;Kb=W-m+N;if(T||J)Na?Vb>0&&Kb>0&&Na.animate(Na.crisp(null,null,null,Vb,Ia)):Na=wa.rect(0,0,Vb,Ia,x.borderRadius,T||0).attr({stroke:x.borderColor,"stroke-width":T||0,fill:J||ca}).add(S).shadow(x.shadow,void 0,x.shadow),Na[ra.length?"show":"hide"]();k=["left","right","top","bottom"];for(s=4;s--;)e=k[s],f[e]&&f[e]!=="auto"&&(x[s<2?"align":"verticalAlign"]=e,x[s<2?"x":"y"]=p(f[e])*(s%2?-1:1));if(!aa)x.y=
(a.options.chart.marginTop-a.options.chart.marginBottom)/2;S.align(y(x,{width:Vb,height:Ia}),!0,Wa);Jb||b()}var x=a.options.legend;if(x.enabled){var aa=x.layout==="horizontal",e=x.symbolWidth,g=x.symbolPadding,ra,f=x.style,ma=x.itemStyle,j=x.itemHoverStyle,o=x.itemHiddenStyle,ga=x.padding||4,qa=ga*0.5,h=20,Ga=p(ma.fontSize)||10,m=Ga+ga,z=x.title.padding,r=x.textPadding,q=r+ga+e+2*g+x.initialItemX,l=x.scroll,P,t=-r-g-e,rb=-Ga+g,w=-r-g-e/2,u=-Ga+g+e/2,Tb,S,Eb=[],ta=0,C=0,wb=x.rowHeight,G=x.legendNumColumns,
hc,E,Ha,Ob,A,Ia=x.height,Da,K,W,N=0,Na,T=x.borderWidth,J=x.backgroundColor,Ka,Fb,ua=x.width,Z=ua,ba=a.series,ac=x.reversed;c();ha(a,"endResize",b);return{colorizeItem:k,destroyItem:function(a){var d=a.checkbox;B(["legendItem","legendLine","legendSymbol"],function(d){a[d]&&a[d].destroy()});d&&Oa(a.checkbox)},renderLegend:c}}};cc=function(a,d){return a>=0&&a<=Xa&&d>=0&&d<=Ua};Ec=function(){W.stepZoom&&cb?cb(void 0,void 0,0):Ya(W,"selection",{resetSelection:!0},xc);W.toolbar.remove("zoom")};cb=function(a,
d,k){var Ha;var s=W.stepZoom,b=s.zoomHistory,I=s.currentZoomLevelIndex,c,x=s.pixelsPerPoint,e=W.series,aa=e.length,ra=s.maxIndex,g,f,ma,j;if(k<I)I=s.currentZoomLevelIndex=k||0,b.splice(I+1,b.length),b=b[I],x=b.stepping,k=b.seriesStart,d=b.perPointPixelDistance,f=b.scrollRatio,a=s.scrollPosition=b.scrollPosition,s.scrollablePXLength=b.scrollablePXLength;else{a>d&&(k=a,a=d,d=k);c=W.plotWidth;k=d-a;d=c/k;x=d<x?Math.ceil(x/d):1;d=c/(k-k%x);k=a%x;g=ra-ra%x;ma=(g-k)*d;f=c/ma;j=ma-c;a=j>0?(a-k)*d/(ma-c):
0;s.scrollPosition=a;s.scrollablePXLength=j;if((c=b[I])&&c.stepping===x&&c.seriesStart===k&&c.seriesEnd===g&&c.seriesEnd===d)return;b[s.currentZoomLevelIndex=I+=1]={seriesStart:k,seriesEnd:g,stepping:x,perPointPixelDistance:d,visiblePointDistance:x*d,seriesConf:[],scrollRatio:f,scrollPosition:a,scrollablePXLength:j}}c=[];for(var o,ga,qa,I=0;I<=ra;I+=1){g=I-k;b=g*d;ma=b+s.xDisplacement;g=(j=g<0||g%x!==0)?"hide":"show";if(qa=s.catLabelArr[I])qa[g](),qa.attr({x:ma});for(ma=0;ma<aa;ma+=1)if(o=e[ma],qa=
o.data[I],qa.plotX=b,Ha=c[ma]||(c[ma]={path:[],addMove:!0,addLine:!1,lastMovePoint:[],connectNullData:o.options.connectNullData}),o=Ha,ga=o.path,qa.graphic&&(qa.graphic.attr({x:b}),qa.graphic[g]()),qa.dataLabel&&qa.dataLabel.attr&&(qa.dataLabel.attr({x:b}),qa.dataLabel[g]()),qa.tracker&&qa.tracker.attr&&(qa.tracker.attr({x:b}),qa.tracker[g]()),!j)if(qa&&qa.plotY){if(o.addLine)ga.push(n,o.lastMovePoint[0],o.lastMovePoint[1],D),o.addLine=!1;o.addMove?(o.addLine=!0,o.addMove=!1,o.lastMovePoint[0]=b,
o.lastMovePoint[1]=qa.plotY):ga.push(b,qa.plotY)}else if(!o.connectNullData)o.addMove=!0}for(ma=0;ma<aa;ma+=1)o=c[ma]||(c[ma]={path:[n],isLastMoveComand:!0}),ga=o.path,e[ma].graphLine.attr({d:ga});s.scroller.setScrollRatio(f);s.scroller.setScrollPosition(a)};var Ic=function(a){var d=W.stepZoom,k=d.zoomHistory[d.currentZoomLevelIndex],s=k.perPointPixelDistance,b=a.selectionWidth,d=d.maxIndex,a=k.scrollablePXLength*k.scrollPosition+a.selectionLeft,b=a+b,a=Math.floor(a/s),b=Math.ceil(b/s);b>d&&(b=d);
b>a&&cb(a,b)};xc=function(a){var d=Va.lang,k=W.pointCount<100;W.toolbar.add("zoom",d.resetZoom,d.resetZoomTitle,Ec);W.stepZoom&&cb?Ic(a):(!a||a.resetSelection?B(qb,function(a){a.setExtremes(null,null,!1,k)}):B(a.xAxis.concat(a.yAxis),function(a){var d=a.axis;W.tracker[d.isXAxis?"zoomX":"zoomY"]&&d.setExtremes(a.min,a.max,!1,k)}),ma())};pc=function(){var d=a.legend,k=h(d.margin,10),s=d.x,b=d.y,I=d.align,c=d.verticalAlign,x;fc();if((W.title||W.subtitle)&&!t(wb))(x=na(W.title&&!db.floating&&!db.verticalAlign&&
db.y||0,W.subtitle&&!Sb.floating&&!Sb.verticalAlign&&Sb.y||0))&&(La=na(La,x+h(db.margin,15)+bc));d.enabled&&!d.floating&&(I==="right"?t(K)||(Mb=na(Mb,Vb-s+k+da)):I==="left"?t(ac)||(Ea=na(Ea,Vb+s+k+X)):c==="top"?t(wb)||(La=na(La,Kb+b+k+bc)):c==="bottom"&&(t(Fb)||(V=na(V,Kb-b+k+pa))));Bb&&B(qb,function(a){a.getOffset()});t(ac)||(Ea+=oa[3]);t(wb)||(La+=oa[0]);t(Fb)||(V+=oa[2]);t(K)||(Mb+=oa[1]);zc()};yc=function(a,d,k){var s=W.title,b=W.subtitle;Jb+=1;sa(k,W);Ma=Sa;ob=bb;W.chartWidth=bb=F(a);W.chartHeight=
Sa=F(d);R($,{width:bb+v,height:Sa+v});wa.setSize(bb,Sa,k);Xa=bb-Ea-Mb;Ua=Sa-La-V;Rb=null;B(qb,function(a){a.isDirty=!0;a.setScale()});B(ab,function(a){a.isDirty=!0});W.isDirtyLegend=!0;W.isDirtyBox=!0;pc();s&&s.align(null,null,Wa);b&&b.align(null,null,Wa);ma(k);Ma=null;Ya(W,"resize");setTimeout(function(){Ya(W,"endResize",null,function(){Jb-=1})},g&&g.duration||500)};zc=function(){W.plotLeft=Ea=F(Ea);W.plotTop=La=F(La);W.plotWidth=Xa=F(bb-Ea-Mb);W.plotHeight=Ua=F(Sa-La-V);W.plotSizeX=Pa?Ua:Xa;W.plotSizeY=
Pa?Xa:Ua;Wa={x:X,y:bc,width:bb-X-da,height:Sa-bc-pa}};fc=function(){La=h(wb,bc);Mb=h(K,da);V=h(Fb,pa);Ea=h(ac,X);oa=[0,0,0,0]};ec=function(){var a=ta.borderWidth||0,d=ta.backgroundColor,k=ta.plotBackgroundColor,s=ta.plotBackgroundImage,b=ta.bgSWF,I=ta.bgSWFAlpha/100,c=ta.bgImageDisplayMode,x=ta.bgImageVAlign,e=ta.bgImageHAlign,aa=ta.bgImageScale,g=ta.logoURL,ra=ta.logoAlpha/100,f=ta.logoPosition,ma=ta.logoLink,o=ta.logoScale,j=ta.logoLeftMargin,qa=ta.logoTopMargin,ga,h={x:Ea,y:La,width:Xa,height:Ua};
ga=a+(ta.shadow?8:0);if(a||d)sc?sc.animate(sc.crisp(null,null,null,bb-ga,Sa-ga)):sc=wa.rect(ga/2,ga/2,bb-ga,Sa-ga,ta.borderRadius,a).attr({stroke:ta.borderColor,"stroke-width":a,fill:d||ca}).add().shadow(ta.shadow);d=wa.clipRect(a,a,bb-a*2,Sa-a*2);if(b&&!Db){var Ga=new Image,n,m=1,z=1,l,P;Xb||(Xb=wa.g("group").attr({visibility:S}).clip(d).add());Ga.onload=function(){n=eb(c,x,e,aa,a,bb,Sa,Ga);Db=[];if(n.tileInfo){m=n.tileInfo.xCount;z=l=n.tileInfo.yCount;P=n.y;for(delete n.tileInfo;m;l-=1){if(l==0)l=
z,m-=1,n.x+=n.width,n.y=P;Db[void 0]=wa.image(b).attr(n).css({opacity:I}).add(Xb.attr({visibility:q}));n.y+=n.height}}else Db[0]=wa.image(b).attr(n).css({opacity:I}).add(Xb.attr({visibility:q}))};Ga.src=b}k&&(Za?Za.animate(h):Za=wa.rect(Ea,La,Xa,Ua,M(ta.plotBorderRadius,0)).attr({fill:k}).add().shadow(ta.plotShadow,void 0,ta.plotShadow));s&&(Ta?Ta.animate(h):Ta=wa.image(s,Ea,La,Xa,Ua).add());if(ta.plotBorderWidth)k=ta.plotBorderWidth,s=k*0.5,s={x:Ea-s,y:La-s,width:Xa+k,height:Ua+k,r:M(ta.plotBorderRadius,
0)},Ub?Ub.animate(Ub.crisp(null,s.x,s.y,s.width,s.height)):Ub=wa.rect(s.x,s.y,s.width,s.height,s.r,k).attr({stroke:ta.plotBorderColor,"stroke-width":k,"stroke-linejoin":"round",zIndex:2}).add();if(g&&!jb){var p=new Image,v,t,D,rb;switch(f){case "tr":t=Ra;D=xb;break;case "bl":t=lb;D=u;break;case "br":t=lb;D=xb;break;case "cc":D=t=L;break;default:t=Ra,D=u}gb||(gb=wa.g("group").attr({visibility:S,zIndex:7}).clip(d).add());ma&&(rb=wa.rect(0,0,1,1,0).attr({isTracker:!0,stroke:r,fill:r,"stroke-width":0,
visibility:q,zIndex:10}).css({cursor:"pointer",_cursor:"hand"}).on("mouseover",function(){W.tooltip&&W.tooltip.hide()}).on("click",function(){ta.events.click.call({link:ma})}).add());p.onload=function(){v=eb("none",t,D,o,a,bb,Sa,p);jb=wa.image(g).attr(v).translate(j,qa).css({opacity:ra}).add(gb.attr({visibility:q}));if(ma)v.r=0,rb.attr(v)};p.src=g}W.isDirtyBox=!1};ha(hb,"unload",C);ta.reflow!==!1&&ha(W,"load",rb);if(Ia)for(pb in Ia)ha(W,pb,Ia[pb]);W.options=a;W.series=ab;W.addSeries=function(a,d,
k){var s;a&&(sa(k,W),d=h(d,!0),Ya(W,"addSeries",{options:a},function(){s=x(a);s.isDirty=!0;W.isDirtyLegend=!0;d&&W.redraw()}));return s};W.animation=h(ta.animation,!0);W.destroy=C;W.get=function(a){var d,k,s;for(d=0;d<qb.length;d++)if(qb[d].options.id===a)return qb[d];for(d=0;d<ab.length;d++)if(ab[d].options.id===a)return ab[d];for(d=0;d<ab.length;d++){s=ab[d].data;for(k=0;k<s.length;k++)if(s[k].id===a)return s[k]}return null};W.getSelectedPoints=function(){var a=[];B(ab,function(d){a=a.concat(N(d.data,
function(a){return a.selected}))});return a};W.getSelectedSeries=function(){return N(ab,function(a){return a.selected})};W.hideLoading=function(){la(kb,{opacity:0},{duration:a.loading.hideDuration,complete:function(){R(kb,{display:ca})}});yb=!1};W.isInsidePlot=cc;W.redraw=ma;W.setSize=yc;W.setTitle=qa;W.showLoading=function(d){var k=a.loading,s=k.labelStyle;kb||(kb=Q(j,{className:"highcharts-loading"},y(k.style,{left:0,top:0,width:bb+v,height:Sa+v,zIndex:22,display:ca}),$),y(s,{top:Sa/2-3+v}),Cb=
Q("span",null,s,kb));Cb.innerHTML=d||a.lang.loading;yb||(R(kb,{opacity:0,display:""}),la(kb,{opacity:k.style.opacity},{duration:k.showDuration}),yb=!0)};W.pointCount=0;W.counters=new fa;Ob()}function Hb(a,d,k,s,b,c){var x=O.atan((d-s)/(a-k)),e=[];x<0&&(x=2*O.PI+x);if(s>d){if(k>=a&&x>O.PI||k<a&&x>O.PI)x-=O.PI}else if(k>=a&&x<O.PI&&x!=0||k<a&&x<O.PI)x+=O.PI;typeof c=="undefined"?(k=a+b*Ja(x),b=d+b*Ma(x)):(b=va(b)/2,c=va(c)/2,k=a+(b=a<k?b:-b),b=d+b*O.tan(x),va(d-b)>va(c)&&(b=d+(c=d<s?c:-c),k=a+c/O.tan(x)));
e.push(n);e.push(k+10*Ja(x+0.79));e.push(b+10*Ma(x+0.79));e.push(D);e.push(k);e.push(b);e.push(k+10*Ja(x-0.79));e.push(b+10*Ma(x-0.79));return e}var Nb=FusionCharts(["private","modules.renderer.highcharts-src"]);if(Nb!==void 0){var C=Nb.hcLib,J=C.pluck,M=C.pluckNumber,cb=C.graphics.getDarkColor,jb=C.graphics.getLightColor,Ta=C.graphics.convertColor,kb=C.graphics.getAngle,V=C.regex.dropHash,Cb=C.regex.startsRGBA,ya=C.regex.hexcode,ja=C.HASHSTRING,ub=C.BLANKSTRING,eb=C.setImageDisplayMode,Ra=C.POSITION_TOP,
xb=C.POSITION_RIGHT,lb=C.POSITION_BOTTOM,u=C.POSITION_LEFT,L=C.POSITION_MIDDLE,Qa=C.COLOR_TRANSPARENT,yb=C.getFirstColor,X=C.COMMASTRING,Jb={column3d:function(a,d){var k,s,b=0,c,x,e,g=[];k=0;for(s=a.length;k<s;k+=1)b=na(a[k].data.length,b);if(b>0){x=a[0];d=x.chart;x.initGroup();e=d.column3DStacked=J(x.options.stacking,d.options.plotOptions.column3d&&d.options.plotOptions.column3d.stacking,d.options.plotOptions.series.stacking)!==void 0?!0:!1;for(k=0;k<b;k+=1){c=0;for(s=a.length;c<s;c+=1)x=a[c],(x=
x.data[k])&&x.y!==null&&(x.y<=0&&e?g.push(c):a[c].drawNthPoint(k));if(e)for(s=g.length-1;s>=0;s-=1)a[g.pop()].drawNthPoint(k)}c=0;for(s=a.length;c<s;c+=1)a[c].render()}},bar3d:function(a,d){var k,s,b=0,c,x,e=[];k=0;for(s=a.length;k<s;k+=1)b=na(a[k].data.length,b);if(b>0){c=a[0];d=c.chart;c.initGroup();x=d.column3DStacked=J(c.options.stacking,d.options.plotOptions.column3d&&d.options.plotOptions.column3d.stacking,d.options.plotOptions.series.stacking)!==void 0?!0:!1;for(k=b-1;k>=0;k-=1){b=0;for(s=
a.length;b<s;b+=1)c=a[b],(c=c.data[k])&&c.y!==null&&(c.y<=0&&x?e.push(b):a[b].drawNthPoint(k));if(x)for(s=e.length-1;s>=0;s-=1)a[e.pop()].drawNthPoint(k)}b=0;for(s=a.length;b<s;b+=1)a[b].render()}},common:function(){}},fc={column3d:!0,bar3d:!0,area3d:!0,line3d:!0},uc=function(a,d,k){var s=0,b=0,c=0,x=0,e=0,g,f=a.xDepth,o=a.yDepth,j=a.options.chart.series2D3Dshift,h=0,n=0;if(d.column3d)s=d.column3d.length;if(d.bar3d)x=d.bar3d.length;if(d.area3d)b=d.area3d.length;if(d.line3d)c=d.line3d.length;e=k?(s?
1:0)+(x?1:0)+(b?1:0)+(c?1:0):(s?1:0)+(x?1:0)+b+c;a.num3dSeriesType=e;n=h=0;s=o/e;f/=e;for(g in d)if(fc[g]){b=d[g];c=g==="column3d"||g==="bar3d"||k?!1:!0;h+=f;n+=s;e=0;for(x=b.length;e<x;e+=1)o=b[e],o.depthXDisplacement=-h,o.depthYDisplacement=n,c?(h+=o.xDepth=f,n+=o.yDepth=s):(o.xDepth=f,o.yDepth=s);e=Jb[g]?Jb[g]:Jb.common;e(b,a);delete d[g]}for(g in d){s=a.plotLeft-h;f=j?a.plotTop+n:a.plotTop;e=0;for(k=d[g].length;e<k;e+=1)d[g][e].options.zIndex=4,d[g][e].render(),d[g][e].group.translate(s,f)}},
Aa=document,hb=window,O=Math,F=O.round,mb=O.floor,Fa=O.ceil,na=O.max,nb=O.min,va=O.abs,Ja=O.cos,Ma=O.sin,pb=O.PI,zb=pb/180,dc=O.atan2,ec=pb/2,Yb=pb*1.5,Kb=pb*2,ib=navigator.userAgent,Lb=/msie/i.test(ib)&&!hb.opera,Ab=Aa.documentMode===8,Zb=/AppleWebKit/.test(ib),za=/Firefox/.test(ib),Ca=!!Aa.createElementNS&&!!Aa.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,$b,$a=Aa.documentElement.ontouchstart!==void 0,Bb={},Wb=0,tb=1,Ib,Va,c,g,o,e,j="div",z="absolute",P="relative",S="hidden",
m="highcharts-",q=Lb&&!Ca?"visible":"",v="px",ca="none",n="M",D="L",r="rgba(192,192,192,"+(Ca?1.0E-6:0.0020)+")",xa="",Y="hover",Na,Da,G,Ka,oc,ua,T,ba,Qb,vc,Bc,Cc,da=hb.HighchartsAdapter,db=da||{},B=db.each,N=db.grep,gc=db.map,ka=db.merge,ha=db.addEvent,vb=db.removeEvent,Ya=db.fireEvent,la=db.animate,ea=db.stop,Z=C.seriesTypes={};c=function(a,d,k){function s(a){return a.toString().replace(/^([0-9])$/,"0$1")}if(!t(d)||isNaN(d))return"Invalid date";var a=h(a,"%Y-%m-%d %H:%M:%S"),d=new Date(d*tb),b,
c=d[G](),x=d[Ka](),e=d[oc](),g=d[ua](),f=d[T](),o=Va.lang,j=o.weekdays,o=o.months,d={a:j[x].substr(0,3),A:j[x],d:s(e),e:e,b:o[g].substr(0,3),B:o[g],m:s(g+1),y:f.toString().substr(2,2),Y:f,H:s(c),I:s(c%12||12),l:c%12||12,M:s(d[Da]()),p:c<12?"AM":"PM",P:c<12?"am":"pm",S:s(d.getSeconds())};for(b in d)a=a.replace("%"+b,d[b]);return k?a.substr(0,1).toUpperCase()+a.substr(1):a};fa.prototype={wrapColor:function(a){if(this.color>=a)this.color=0},wrapSymbol:function(a){if(this.symbol>=a)this.symbol=0}};da&&
da.init&&da.init();if(!da&&hb.jQuery){var Wa=jQuery,B=C.each=function(a,d){for(var k=0,s=a.length;k<s;k++)if(d.call(a[k],a[k],k,a)===!1)return k},N=Wa.grep,gc=function(a,d){for(var k=[],s=0,b=a.length;s<b;s++)k[s]=d.call(a[s],a[s],s,a);return k},ka=C.merge=function(){var a=arguments;return Wa.extend(!0,null,a[0],a[1],a[2],a[3])},ha=function(a,d,k,s){Wa(a).bind(d,s,k)},vb=function(a,d,k){var s=Aa.removeEventListener?"removeEventListener":"detachEvent";Aa[s]&&!a[s]&&(a[s]=function(){});Wa(a).unbind(d,
k)},Ya=function(a,d,k,s){var b=Wa.Event(d),c="detached"+d;y(b,k);a[d]&&(a[c]=a[d],a[d]=null);Wa(a).trigger(b);a[c]&&(a[d]=a[c],a[c]=null);s&&!b.isDefaultPrevented()&&s(b)},la=function(a,d,k){var s=Wa(a);if(d.d)a.toD=d.d,d.d=1;s.stop();s.animate(d,k)},ea=function(a){Wa(a).stop()};Wa.extend(Wa.easing,{easeOutQuad:function(a,d,k,s,b){return-s*(d/=b)*(d-2)+k}});var pa=jQuery.fx.step._default,Mb=jQuery.fx.prototype.cur;Wa.fx.step._default=function(a){var d=a.elem;d.attr?d.attr(a.prop,a.now):pa.apply(this,
arguments)};Wa.fx.step.d=function(a){var d=a.elem;if(!a.started){var k=o.init(d,d.d,d.toD);a.start=k[0];a.end=k[1];a.started=!0}d.attr("d",o.step(a.start,a.end,a.pos,d.toD))};Wa.fx.prototype.cur=function(){var a=this.elem;return a.attr?a.attr(this.prop):Mb.apply(this,arguments)}}o={init:function(a,d,k){var d=d||"",s=a.shift,b=d.indexOf("C")>-1,c=b?7:3,x,d=d.split(" "),k=[].concat(k),e,g,f=function(a){for(x=a.length;x--;)a[x]===n&&a.splice(x+1,0,a[x+1],a[x+2],a[x+1],a[x+2])};b&&(f(d),f(k));a.isArea&&
(e=d.splice(d.length-6,6),g=k.splice(k.length-6,6));if(s)k=[].concat(k).splice(0,c).concat(k),a.shift=!1;if(d.length)for(a=k.length;d.length<a;)s=[].concat(d).splice(d.length-c,c),b&&(s[c-6]=s[c-2],s[c-5]=s[c-1]),d=d.concat(s);e&&(d=d.concat(e),k=k.concat(g));return[d,k]},step:function(a,d,k,s){var b=[],c=a.length;if(k===1)b=s;else if(c===d.length&&k<1)for(;c--;)s=parseFloat(a[c]),b[c]=isNaN(s)?a[c]:k*parseFloat(d[c]-s)+s;else b=d;return b}};da={enabled:!0,align:"center",x:0,y:15,style:{color:"#666",
fontSize:"11px",lineHeight:"14px"}};Va={colors:["#4572A7","#AA4643","#89A54E","#80699B","#3D96AE","#DB843D","#92A8CD","#A47D7C","#B5CA92"],symbols:["circle","diamond","square","triangle","triangle-down"],lang:{loading:"Loading...",months:["January","February","March","April","May","June","July","August","September","October","November","December"],weekdays:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],decimalPoint:".",resetZoom:"Reset zoom",resetZoomTitle:"Reset zoom level 1:1",
thousandsSep:","},global:{useUTC:!0},chart:{borderColor:"#4572A7",borderRadius:5,defaultSeriesType:"line",ignoreHiddenSeries:!0,spacingTop:10,spacingRight:10,spacingBottom:15,spacingLeft:10,style:{fontFamily:'"Lucida Grande", "Lucida Sans Unicode", Verdana, Arial, Helvetica, sans-serif',fontSize:"12px"},backgroundColor:"#FFFFFF",plotBorderColor:"#C0C0C0"},title:{text:"Chart title",align:"center",y:15,style:{color:"#3E576F",fontSize:"16px"}},subtitle:{text:"",align:"center",y:30,style:{color:"#6D869F"}},
plotOptions:{line:{allowPointSelect:!1,showCheckbox:!1,animation:{duration:1E3},events:{},lineWidth:2,shadow:!0,marker:{enabled:!0,lineWidth:0,radius:4,lineColor:"#FFFFFF",states:{hover:{},select:{fillColor:"#FFFFFF",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:ka(da,{enabled:!1,y:-6,formatter:function(){return this.y}}),showInLegend:!0,states:{hover:{marker:{}},select:{marker:{}}},stickyTracking:!0}},labels:{style:{position:z,color:"#3E576F"}},legend:{enabled:!0,align:"center",
layout:"horizontal",labelFormatter:function(){return this.name},borderWidth:1,borderColor:"#909090",borderRadius:5,shadow:!1,style:{padding:"5px"},itemStyle:{cursor:"pointer",color:"#3E576F"},itemHoverStyle:{cursor:"pointer"},itemHiddenStyle:{color:"#C0C0C0"},itemCheckboxStyle:{position:z,width:"13px",height:"13px"},symbolWidth:16,symbolPadding:5,verticalAlign:"bottom",x:0,y:0},loading:{hideDuration:100,labelStyle:{position:P,fontFamily:"Verdana",fontSize:"10px",color:"#ffffff"},showDuration:100,
style:{position:z,backgroundColor:"black",opacity:0.2,textAlign:"center"}},tooltip:{enabled:!0,backgroundColor:"rgba(255, 255, 255, .85)",borderWidth:2,borderRadius:5,shadow:!0,snap:$a?25:10,style:{color:"#333333",fontSize:"12px",padding:"5px",whiteSpace:"nowrap"}},toolbar:{itemStyle:{color:"#4572A7",cursor:"pointer"}},credits:{enabled:!0,text:"Highcharts.com",href:"http://www.highcharts.com",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#909090",fontSize:"10px"}}};
var Ba={dateTimeLabelFormats:{second:"%H:%M:%S",minute:"%H:%M",hour:"%H:%M",day:"%e. %b",week:"%e. %b",month:"%b '%y",year:"%Y"},endOnTick:!1,gridLineColor:"#C0C0C0",labels:da,lineColor:"#C0D0E0",lineWidth:1,max:null,min:null,minPadding:0.01,maxPadding:0.01,minorGridLineColor:"#E0E0E0",minorGridLineWidth:1,minorTickColor:"#A0A0A0",minorTickLength:2,minorTickPosition:"outside",startOfWeek:1,startOnTick:!1,tickColor:"#C0D0E0",tickLength:5,tickmarkPlacement:"between",tickPixelInterval:100,tickPosition:"outside",
tickWidth:1,title:{align:"middle",style:{color:"#6D869F",fontWeight:"bold"}},type:"linear"},wc=ka(Ba,{endOnTick:!0,gridLineWidth:1,tickPixelInterval:72,showLastLabel:!0,labels:{align:"right",x:-8,y:3},lineWidth:0,maxPadding:0.05,minPadding:0.05,startOnTick:!0,tickWidth:0,title:{rotation:270,text:"Y-values"},stackLabels:{enabled:!1,formatter:function(){return this.total},style:da.style}}),Hc={labels:{align:"right",x:-8,y:null},title:{rotation:270}},Gc={labels:{align:"left",x:8,y:null},title:{rotation:90}},
Dc={labels:{align:"center",x:0,y:14},title:{rotation:0}},Fc=ka(Dc,{labels:{y:-5}}),$=C.defaultPlotOptions=Va.plotOptions,da=$.line;$.spline=ka(da);$.scatter=ka(da,{lineWidth:0,states:{hover:{lineWidth:0}}});$.area=ka(da,{});$.areaspline=ka($.area);$.column=ka(da,{borderColor:"#FFFFFF",borderWidth:1,borderRadius:0,groupPadding:0.2,marker:null,pointPadding:0.1,minPointLength:0,states:{hover:{brightness:0.1,shadow:!1},select:{color:"#C0C0C0",borderColor:"#000000",shadow:!1}},dataLabels:{y:null,verticalAlign:null}});
$.bar=ka($.column,{dataLabels:{align:"left",x:5,y:0}});$.pie=ka(da,{borderColor:"#FFFFFF",borderWidth:1,center:["50%","50%"],colorByPoint:!0,dataLabels:{distance:30,enabled:!0,formatter:function(){return this.point.name},y:5},legendType:"point",marker:null,size:"75%",showInLegend:!1,slicedOffset:10,states:{hover:{brightness:0.1,shadow:!1}}});oa();var Db=function(a){var d=[],k;(function(a){(k=/rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]?(?:\.[0-9]+)?)\s*\)/.exec(a))?d=
[p(k[1]),p(k[2]),p(k[3]),parseFloat(k[4],10)]:(k=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(a))&&(d=[p(k[1],16),p(k[2],16),p(k[3],16),1])})(a);return{get:function(k){return d&&!isNaN(d[0])?k==="rgb"?"rgb("+d[0]+","+d[1]+","+d[2]+")":k==="hex"?"#"+("000000"+(d[0]<<16|d[1]<<8|d[2]).toString(16)).slice(-6):k==="a"?d[3]:"rgba("+d.join(",")+")":a},brighten:function(a){if(gb(a)&&a!==0){var k;for(k=0;k<3;k++)d[k]+=p(a*255),d[k]<0&&(d[k]=0),d[k]>255&&(d[k]=255)}return this},setOpacity:function(a){d[3]=
a;return this}}};sb.prototype={init:function(a,d){this.element=Aa.createElementNS("http://www.w3.org/2000/svg",d);this.renderer=a},animate:function(a,d,k){if(d=h(d,g,!0)){d=ka(d);if(k)d.complete=k;la(this,a,d)}else this.attr(a),k&&k()},attr:function(a,d){var k,s,c,e,x,g=this.element,f=g.nodeName,o=this.renderer,j,h=this.shadows,n,m=this;A(a)&&t(d)&&(k=a,a={},a[k]=d);if(A(a))k=a,f==="circle"?k={x:"cx",y:"cy"}[k]||k:k==="strokeWidth"&&(k="stroke-width"),m=b(g,k)||this[k]||0,k!=="d"&&k!=="visibility"&&
(m=parseFloat(m));else for(k in a){j=!1;s=a[k];if(k==="d")s&&s.join&&(s=s.join(" ")),/(NaN| {2}|^$)/.test(s)&&(s="M 0 0"),this.d=s;else if(k==="x"&&f==="text"){for(c=0;c<g.childNodes.length;c++)e=g.childNodes[c],b(e,"x")===b(g,"x")&&b(e,"x",s);this.rotation&&b(g,"transform","rotate("+this.rotation+" "+s+" "+p(a.y||b(g,"y"))+")")}else if(k==="fill")s=o.color(s,g,k);else if(f==="circle"&&(k==="x"||k==="y"))k={x:"cx",y:"cy"}[k]||k;else if(k==="translateX"||k==="translateY"||k==="rotation"||k==="verticalAlign")this[k]=
s,this.updateTransform(),j=!0;else if(k==="stroke")s=o.color(s,g,k);else if(k==="dashstyle")if(k="stroke-dasharray",s=s&&s.toLowerCase(),s==="solid")s=ca;else{if(s){s=s.replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(",");for(c=s.length;c--;)s[c]=parseFloat(s[c])*a["stroke-width"];s=s.join(",")}}else k==="isTracker"?this[k]=
s:k==="width"?s=p(s):k==="align"&&(k="text-anchor",s={left:"start",center:"middle",right:"end"}[s]);k==="strokeWidth"&&(k="stroke-width");Zb&&k==="stroke-width"&&s===0&&(s=1.0E-6);this.symbolName&&/^(x|y|r|start|end|innerR)/.test(k)&&(n||(this.symbolAttr(a),n=!0),j=!0);if(h&&(k==="opacity"||k==="stroke-opacity")){x=0.06*s;c=0;for(e=h.length;c<e;)b(h[c],k,(c+=1)*x)}if(h&&/^(width|height|visibility|x|y|d)$/.test(k))for(c=h.length;c--;)b(h[c],k,s);if((k==="width"||k==="height")&&f==="rect"&&s<0)s=0;
k==="text"?(this.textStr=s,this.added&&o.buildText(this)):j||b(g,k,s)}return m},symbolAttr:function(a){var d=this;B(["x","y","r","start","end","width","height","innerR"],function(k){d[k]=h(a[k],d[k])});d.attr({d:d.renderer.symbols[d.symbolName](F(d.x*2)/2,F(d.y*2)/2,d.r,{start:d.start,end:d.end,width:d.width,height:d.height,innerR:d.innerR})})},clip:function(a){return this.attr("clip-path","url('"+this.renderer.url+"#"+a.id+"')")},crisp:function(a,d,k,b,c){var e,x={},g={},f,a=a||this.strokeWidth||
0;f=a%2/2;g.x=mb(d||this.x||0)+f;g.y=mb(k||this.y||0)+f;g.width=mb((b||this.width||0)-2*f);g.height=mb((c||this.height||0)-2*f);g.strokeWidth=a;if(g.width===0&&b!==0)g.width=1;for(e in g)this[e]!==g[e]&&(this[e]=x[e]=g[e]);return x},css:function(a){var d=this.element,d=a&&a.width&&d.nodeName==="text",k,b="",c=function(a,d){return"-"+d.toLowerCase()};if(a&&a.color)a.fill=a.color;this.styles=a=y(this.styles,a);if(Lb&&!Ca)d&&delete a.width,R(this.element,a);else{for(k in a)b+=k.replace(/([A-Z])/g,c)+
":"+a[k]+";";this.attr({style:b})}d&&this.added&&this.renderer.buildText(this);return this},on:function(a,d){var k=d;$a&&a==="click"&&(a="touchstart",k=function(a){a.preventDefault();d()});this.element["on"+a]=k;return this},translate:function(a,d){return this.attr({translateX:a,translateY:d})},invert:function(){this.inverted=!0;this.updateTransform();return this},updateTransform:function(){var a=this.translateX||0,d=this.translateY||0,k=this.inverted,s=this.rotation,c=[];k&&(a+=this.attr("width"),
d+=this.attr("height"));(a||d)&&c.push("translate("+a+","+d+")");k?c.push("rotate(90) scale(-1,1)"):s&&c.push("rotate("+s+" "+this.x+" "+this.y+")");c.length&&b(this.element,"transform",c.join(" "))},rotate:function(a,d,k){this.x=d;this.y=k;return this.attr({translateX:d,translateY:k,rotation:a})},toFront:function(){var a=this.element;a.parentNode.appendChild(a);return this},align:function(a,d,k){a?(this.alignOptions=a,this.alignByTranslate=d,k||this.renderer.alignedObjects.push(this)):(a=this.alignOptions,
d=this.alignByTranslate);var k=h(k,this.renderer),b=a.align,c=a.verticalAlign,e=(k.x||0)+(a.x||0),x=(k.y||0)+(a.y||0),g={};/^(right|center)$/.test(b)&&(e+=(k.width-(a.width||0))/{right:1,center:2}[b]);g[d?"translateX":"x"]=F(e);/^(bottom|middle)$/.test(c)&&(x+=(k.height-(a.height||0))/({bottom:1,middle:2}[c]||1));g[d?"translateY":"y"]=F(x);this[this.placed?"animate":"attr"](g);this.placed=!0;this.alignAttr=g;return this},getBBox:function(){var a,d,k,b=this.rotation,c=b*zb;try{a=y({},this.element.getBBox())}catch(e){a=
{width:0,height:0}}d=a.width;k=a.height;if(b)a.width=va(k*Ma(c))+va(d*Ja(c)),a.height=va(k*Ja(c))+va(d*Ma(c));return a},show:function(){return this.attr({visibility:q})},hide:function(){return this.attr({visibility:S})},add:function(a){var d=this.renderer,k=a||d,s=k.element||d.box,c=s.childNodes,e=this.element,x=b(e,"zIndex");this.parentInverted=a&&a.inverted;this.textStr!==void 0&&d.buildText(this);if(x)k.handleZ=!0,x=p(x);if(k.handleZ)for(k=0;k<c.length;k++)if(a=c[k],d=b(a,"zIndex"),a!==e&&(p(d)>
x||!t(x)&&t(d)))return s.insertBefore(e,a),this;s.appendChild(e);this.added=!0;return this},destroy:function(){var a=this.element||{},d=this.shadows,k=a.parentNode,b;a.onclick=a.onmouseout=a.onmouseover=a.onmousemove=null;ea(this);k&&k.removeChild(a);d&&B(d,function(a){(k=a.parentNode)&&k.removeChild(a)});this.renderer.alignedObjects&&f(this.renderer.alignedObjects,this);for(b in this)delete this[b];return null},empty:function(){for(var a=this.element,d=a.childNodes,k=d.length;k--;)a.removeChild(d[k])},
shadow:function(a,d,k){k||(k={});var s=[],c,e=this.element,x=k.color||"rgb(0,0,0)",g="translate"+(!this.parentInverted!=!k.inverted?"(-1,-1)":"(1,1)"),f=p(this.attr("stroke-width")||1,10)+6,k=J(k.opacity,1)*0.06;if(a){for(a=1;a<=3;a++)c=e.cloneNode(0),b(c,{isShadow:"true",stroke:x,"stroke-opacity":k*a,"stroke-width":f-2*a,transform:g,fill:ca}),d?d.element.appendChild(c):e.parentNode.insertBefore(c,e),s.push(c);this.shadows=s}return this},textBound:function(){var a=this.renderer,d=this.textBoundWrapper,
k=this.styles,b,c,e,x;if(this.element.nodeName!=="text"||!k||!k.backgroundColor&&!k.borderColor||!this.textStr||this.rotation%90!==0)return this.textBoundWrapper&&this.textBoundWrapper.destroy(),this;b=this.getBBox();e=b.width+4;x=b.height+4;c=b.x;b=b.y;this.rotation===270&&(c=this.attr("x")-e/2-2,b=this.attr("y")-x+4);if(!d)d=this.textBoundWrapper=a.rect(0,0,0,0,0,1),this.element.parentNode.insertBefore(d.element,this.element);d.attr({fill:k.backgroundColor||ca,stroke:k.borderColor||ub,"fill-opacity":J(k.backgroundOpacity,
1),"stroke-opacity":J(k.borderOpacity,1)});d.attr(d.crisp(1,c-1.5,b-1.5,e,x))}};var rc=function(){this.init.apply(this,arguments)};rc.prototype={Element:sb,type:"SVG",init:function(a,d,k,b){var c=location,e;e=this.createElement("svg").attr({xmlns:"http://www.w3.org/2000/svg",version:"1.1"});a.appendChild(e.element);this.box=e.element;this.boxWrapper=e;this.alignedObjects=[];this.url=Lb?"":c.href.replace(/#.*?$/,"");this.defs=this.createElement("defs").add();this.forExport=b;this.setSize(d,k,!1)},
createElement:function(a){var d=new this.Element;d.init(this,a);return d},buildText:function(a){for(var d=a.element,k=h(a.textStr,"").toString().replace(/<(b|strong)>/g,'<span style="font-weight:bold">').replace(/<(i|em)>/g,'<span style="font-style:italic">').replace(/<a/g,"<span").replace(/<\/(b|strong|i|em|a)>/g,"</span>").split(/<br.*?>/g),s=d.childNodes,c=/style="([^"]+)"/,e=/href="([^"]+)"/,x=b(d,"x"),g=a.styles,f=za&&g&&g.HcDirection==="rtl"&&!this.forExport&&p(ib.split("Firefox/")[1])<4,o,
j=g&&p(g.width),n=g&&g.lineHeight,m,z=s.length;z--;)d.removeChild(s[z]);j&&!a.added&&this.box.appendChild(d);s=/title="([^"]+)"/;s.test(k[0])&&b(d,"title",k[0].match(s)[1]);B(k,function(k,s){var g,aa=0,h,k=k.replace(/<span/g,"|||<span").replace(/<\/span>/g,"</span>|||");g=k.split("|||");B(g,function(k){if(k!==""||g.length===1){var r={},q=Aa.createElementNS("http://www.w3.org/2000/svg","tspan");c.test(k)&&b(q,"style",k.match(c)[1].replace(/(;| |^)color([ :])/,"$1fill$2"));e.test(k)&&(b(q,"onclick",
'location.href="'+k.match(e)[1]+'"'),R(q,{cursor:"pointer"}));k=(k.replace(/<(.|\n)*?>/g,"")||" ").replace(/&lt;/g,"<").replace(/&gt;/g,">");if(f){o=[];for(z=k.length;z--;)o.push(k.charAt(z));k=o.join("")}var l=Aa.createTextNode(k);q.appendChild(l);aa?r.dx=3:r.x=x;if(!aa){if(s){!Ca&&a.renderer.forExport&&R(q,{display:"block"});h=hb.getComputedStyle&&p(hb.getComputedStyle(m,null).getPropertyValue("line-height"));if(!h||isNaN(h))h=n||m.offsetHeight||18;b(q,"dy",h)}m=q}b(q,r);d.appendChild(q);aa++;if(j)for(var k=
k.replace(/-/g,"- ").split(" "),P=[];k.length||P.length;)l=d.getBBox().width,r=l>j,!r||k.length===1?(k=P,P=[],k.length&&(q=Aa.createElementNS("http://www.w3.org/2000/svg","tspan"),b(q,{dy:n||16,x:x}),d.appendChild(q),l>j&&(j=l))):(q.removeChild(q.firstChild),P.unshift(k.pop())),k.length&&q.appendChild(Aa.createTextNode(k.join(" ").replace(/- /g,"-")))}})})},crispLine:function(a,d){a[1]===a[4]&&(a[1]=a[4]=F(a[1])+d%2/2);a[2]===a[5]&&(a[2]=a[5]=F(a[2])+d%2/2);return a},path:function(a){return this.createElement("path").attr({d:a,
fill:ca})},circle:function(a,d,k){a=w(a)?a:{x:a,y:d,r:k};return this.createElement("circle").attr(a)},arc:function(a,d,k,b,c,e){if(w(a))d=a.y,k=a.r,b=a.innerR,c=a.start,e=a.end,a=a.x;return this.symbol("arc",a||0,d||0,k||0,{innerR:b||0,start:c||0,end:e||0})},rect:function(a,d,k,b,c,e){if(w(a))d=a.y,k=a.width,b=a.height,c=a.r,e=a.strokeWidth,a=a.x;c=this.createElement("rect").attr({rx:c,ry:c,fill:ca});return c.attr(c.crisp(e,a,d,na(k,0),na(b,0)))},rect3d:function(){var a={x:{drawTop:!0,drawRight:!0,
drawFront:!0,drawShadow:!0},y:{drawTop:!0,drawRight:!0,drawFront:!0,drawShadow:!0},width:{drawTop:!0,drawRight:!0,drawFront:!0,drawShadow:!0},height:{drawRight:!0,drawFront:!0,drawShadow:!0},strokeWidth:!0,"stroke-width":!0,stroke:!0,x3D:{drawTop:!0,drawRight:!0,drawShadow:!0},y3D:{drawTop:!0,drawRight:!0,drawShadow:!0},fill:!0},d=function(d,k){var b,c,e,g=this,f,o,j=this._3dAttr;A(d)&&t(k)&&(b=d,d={},d[b]=k);if(A(d))g=a[d]?this._3dAttr[d]:this._attr(d);else for(b in d)if(c=d[b],a[b]){e=a[b];j[b]=
c;if(b==="stroke"||b==="stroke-width"||b==="strokeWidth")this.frontRect.attr(b,c),this.topRect.attr(b,c),this.rightRect.attr(b,c);if(b==="fill")c&&c.linearGradient&&c.stops&&c.stops[0]&&(c=c.stops[0][1]),Cb.test(c)?(o=Db(c),f=o.get("hex"),o=o.get("a")*100):c&&c.FCcolor?(f=c.FCcolor.color.split(X)[0],o=c.FCcolor.alpha.split(X)[0]):ya.test(c)&&(f=c.replace(V,ja),o=100),j.color=f,j.alpha=o,j.lighting3D===!1?(j.frontColor=Ta(j.color,j.alpha),j.rightColor=Ta(cb(j.color,60),j.alpha),j.topColor=Ta(cb(j.color,
75),j.alpha)):(j.frontColor={FCcolor:{color:cb(j.color,65)+X+jb(j.color,55),alpha:o+X+o,angle:270}},j.rightColor={FCcolor:{color:cb(j.color,35)+X+cb(j.color,75),alpha:o+X+o,angle:kb(j.x3D,j.height+j.y3D,3)}},j.topColor={FCcolor:{color:cb(j.color,85)+X+jb(j.color,35),alpha:o+X+o,angle:kb(j.width+j.x3D,j.y3D,4)}}),this.topRect.attr({fill:j.topColor}),this.rightRect.attr({fill:j.rightColor}),this.frontRect.attr({fill:j.frontColor});if(e.drawTop){if(j.lighting3D!==!1&&j.topColor.FCcolor)j.topColor.FCcolor.angle=
kb(j.width+j.x3D,j.y3D,4);this.topRect.attr({d:["M",j.x,j.y,"L",j.x+j.width,j.y,j.x+j.width+j.x3D,j.y-j.y3D,j.x+j.x3D,j.y-j.y3D,"Z"],fill:j.topColor})}if(e.drawRight){if(j.lighting3D!==!1&&j.rightColor.FCcolor)j.rightColor.FCcolor.angle=kb(j.x3D,j.height+j.y3D,3);this.rightRect.attr({d:["M",j.x+j.width,j.y,"L",j.x+j.width+j.x3D,j.y-j.y3D,j.x+j.width+j.x3D,j.y-j.y3D+j.height,j.x+j.width,j.y+j.height,"Z"],fill:j.rightRect})}e.drawFront&&this.frontRect.attr(b,c);e.drawShadow&&this.shadowElement.attr({d:["M",
j.x+j.x3D,j.y-j.y3D,"L",j.x+j.x3D+j.width,j.y-j.y3D,j.x+j.x3D+j.width,j.y-j.y3D+j.height]})}else this._attr(b,c);return g},k=function(){var a=this.shadowElement;k&&a.shadow.apply(a,arguments)};return function(a,b,c,e,g,j,f,o){if(w(a))b=a.y,c=a.width,e=a.height,f=a.strokeWidth,g=a.x3D,j=a.y3D,a=a.x;var g=M(g,10),j=M(j,10),h={x:a,y:b,width:c,height:e,r:0},n=["M",a,b,"L",a+c,b,a+c+g,b-j,a+g,b-j,"Z"],m=["M",a+c,b,"L",a+c+g,b-j,a+c+g,b-j+e,a+c,b+e,"Z"],z=["M",a+g,b-j,"L",a+g+c,b-j,a+g+c,b-j+e],o=this.g(o);
o.shadowElement=this.path(z).add(o);o.frontRect=this.rect(h).attr({"stroke-width":f}).add(o);o.topRect=this.path(n).attr({"stroke-width":f}).add(o);o.rightRect=this.path(m).attr({"stroke-width":f}).add(o);o._attr=o.attr;o.attr=d;o.shadow=k;o._3dAttr={y:b,width:c,height:e,x:a,strokeWidth:f,x3D:g,y3D:j,lighting3D:this.lighting3D};return o}}(),getArcPath:function(a,d,k,b,c,e,x,g,j,f){return["A",x,g,0,f,j,c,e]},scroller:function(){var a=$a?"touchstart":"mousedown",d=function(a){var d=a.type==="dragend",
k=a.data;k.dragActive=!d;if(d){if(k.onChange)k.onChange(k.scrollPosition,!1,!0);if(k.onEnd)k.onEnd(k.scrollPosition,!1,!0)}else k.dragInitPos=k.scrollPosition,k.dragStartX=a.pageX,k.dragStartY=a.pageY;$a&&k.anchor.attr("height",k.anchor.attr("height")*(d?0.625:1.6))},k=function(a){var d=a.data;d.group.setScrollPosition(d.dragInitPos+(d.horiz?a.pageX-d.dragStartX:a.pageY-d.dragStartY)/d.zoneLength)},b=function(a,d){if(!arguments.length)return this;y(this.config,{onChange:a,onEnd:d});return this},c=
function(a,d){var k=this.config;k.zoneLength=k.trackLength-k.trackLength*(k.scrollRatio=a);this.elements.anchor.attr({width:this.config.trackLength*a});k.scrollDelta=a>=1?1:0.1;return this.setScrollPosition(void 0,void 0,d)},e=function(a,d,k){var b=this.config,s=b.horiz,c=b.anchor,e=Lb&&!Ca&&!s;t(a)?(a<=0?a=1.0E-8:a>1&&(a=1),b.scrollPosition=a):a=b.scrollPosition;s=(s?a:1-a)*b.zoneLength;s===0&&(s=1.0E-8);e?c.translate(void 0,-s):c.translate(s);if(!k&&b.onChange)b.onChange(a,d,!0);return this},x=
function(a){var d=this.renderer,k=this.config,b,s,c;if(w(a)&&"fill"in a){b=a.fill;s=k.flat;c=!k.horiz&&Lb&&!Ca?180:90;var e={base:b,light:ja+jb(b,15),dark:ja+cb(b,s?50:30)};e.darkFill=s?e.dark:{FCcolor:{color:e.dark+X+b,alpha:"100",angle:c}};e.lightFill=s?e.base:{FCcolor:{color:b+X+e.light,alpha:"100",angle:c}};b=e;s={fill:b.dark};c={fill:b.lightFill};k.buttons&&(this.elements.start.attr(c),this.elements.end.attr(c),this.elements.startArrow.attr(s),this.elements.endArrow.attr(s));this.elements.track.attr(c);
this.elements.anchor.attr({fill:b.darkFill,stroke:b.dark,"stroke-width":0.2});delete a.fill}return d.Element.prototype.attr.apply(this,arguments)};return function(g,j,f,o,h,m,z,q){h||(f+=o,o=f-o,f-=o);var r=this.g("scroller").attr({translateX:g,translateY:j,width:f,height:o}),g=r.config={},j=r.elements={},l,P=!m?0:m.size||o,p=P*0.25,v=o*0.25,t=P+(m&&m.padding||0),S=f-2*t;j.track=this.rect(t,0,S,o,0);j.anchor=this.rect(t,z?0:v*0.5,S,z?o:o-v,z?0:nb(p,v));if(m)j.start=this.rect(0,0,P,o,0),j.startArrow=
this.path([n,p,0,D,0,v,p,v*2,p,0]).translate(P*0.4,v),j.end=this.rect(f-P,0,P,o,0),j.endArrow=this.path([n,0,0,D,p,v,0,v*2,0,0]).translate(f-P*0.6,v);h||r.attr({width:o,height:f}).invert();for(l in j)j[l].add(r);y(g,{group:r,anchor:j.anchor,width:f,horiz:h,flat:z,buttons:!!m,scrollRatio:1,trackOffset:t,trackLength:S,zoneLength:S,scrollPosition:0,scrollDelta:0.1});y(r,{attr:x,setScrollRatio:c,setScrollPosition:e,callback:b});ha(j.anchor.element,"dragstart dragend",d,g);ha(j.anchor.element,"drag",k,
g);m&&(ha([r.elements.start.element,r.elements.startArrow.element],a,function(){r.setScrollPosition(r.config.scrollPosition-r.config.scrollDelta)},g),ha([r.elements.end.element,r.elements.endArrow.element],a,function(){r.setScrollPosition(r.config.scrollPosition+r.config.scrollDelta)},g));q&&ha(q.element,"wheelchange",function(a,d){r.setScrollPosition(r.config.scrollPosition-d*0.5);a.preventDefault()});return r}}(),setSize:function(a,d,k){var b=this.alignedObjects,c=b.length;this.width=a;this.height=
d;for(this.boxWrapper[h(k,!0)?"animate":"attr"]({width:a,height:d});c--;)b[c].align()},g:function(a){return this.createElement("g").attr(t(a)&&{"class":m+a})},image:function(a,d,k,b,c){var e={preserveAspectRatio:ca};arguments.length>1&&y(e,{x:d,y:k,width:b,height:c});e=this.createElement("image").attr(e);e.element.setAttributeNS?e.element.setAttributeNS("http://www.w3.org/1999/xlink","href",a):e.element.setAttribute("hc-svg-href",a);return e},symbol:function(a,d,k,b,c){if(w(a))d=a.x,k=a.y,b=a.radius,
c=a.options,a=a.symbol;var e;if(/^poly\_\d+$/.test(a))e=M(parseInt(a.split("_")[1],10),3),a="poly",typeof c!=="object"&&(c={}),c.innerR=e;var x;e=(e=this.symbols[a])&&e(F(d),F(k),b,c);var g=/^url\((.*?)\)$/,j;if(e)x=this.path(e),y(x,{symbolName:a,x:d,y:k,r:b}),c&&y(x,c);else if(g.test(a)){var f=function(a,d){a.attr({width:d[0],height:d[1]}).translate(-F(d[0]/2),-F(d[1]/2))};j=a.match(g)[1];a=Bb[j];x=this.image(j).attr({x:d,y:k});a?f(x,a):(x.attr({width:0,height:0}),Q("img",{onload:function(){f(x,
Bb[j]=[this.width,this.height])},src:j}))}else x=this.circle(d,k,b);return x},symbols:{poly:function(a,d,k,b){var c=b.innerR,e=2*O.PI/c,x,g=typeof b.startAngle!=="undefined"?b.startAngle:O.PI/2;if(c>2){b=[n,a+k*Ja(-g),d+k*Ma(-g),D];for(x=1;x<c;x++)b.push(a+k*Ja(-(g+x*e))),b.push(d+k*Ma(-(g+x*e)));b.push("Z")}else b=[n,a,d,"Z"];return b},square:function(a,d,k){k*=0.707;return[n,a-k,d-k,D,a+k,d-k,a+k,d+k,a-k,d+k,"Z"]},triangle:function(a,d,k){return[n,a,d-1.33*k,D,a+k,d+0.67*k,a-k,d+0.67*k,"Z"]},"triangle-down":function(a,
d,k){return[n,a,d+1.33*k,D,a-k,d-0.67*k,a+k,d-0.67*k,"Z"]},diamond:function(a,d,k){return[n,a,d-k,D,a+k,d,a,d+k,a-k,d,"Z"]},arc:function(a,d,k,b){var c=b.start,e=b.end-1.0E-6,x=b.innerR,g=Ja(c),j=Ma(c),f=Ja(e),e=Ma(e),b=b.end-c<pb?0:1;return[n,a+k*g,d+k*j,"A",k,k,0,b,1,a+k*f,d+k*e,D,a+x*f,d+x*e,"A",x,x,0,b,0,a+x*g,d+x*j,"Z"]}},clipRect:function(a,d,k,b){var c=m+Wb++,e=this.createElement("clipPath").attr({id:c}).add(this.defs),a=this.rect(a,d,k,b,0).add(e);a.id=c;return a},color:function(a,d,k){var s,
c=/^rgba/;if(a&&a.linearGradient){var e=this,k="linearGradient",x=a[k],d=m+Wb++,g,j,f;g=e.createElement(k).attr({id:d,gradientUnits:"userSpaceOnUse",x1:x[0],y1:x[1],x2:x[2],y2:x[3]}).add(e.defs);B(a.stops,function(a){c.test(a[1])?(s=Db(a[1]),j=s.get("rgb"),f=s.get("a")):(j=a[1],f=1);e.createElement("stop").attr({offset:a[0],"stop-color":j,"stop-opacity":f}).add(g)});return"url('"+this.url+"#"+d+"')"}else if(a&&a.FCcolor){var o=a.FCcolor.element,e=this;if(o&&o.parentNode&&!e.forExport)return a.FCcolor.url;
var o=J(a.FCcolor.color,"000000"),h=J(a.FCcolor.alpha,"100"),n,z,r=0,q=100,l=J(a.FCcolor.ratio,"0");n=M(a.FCcolor.angle,"0");o=o.replace(/\s+/ig,"").replace(/\,+$/ig,"").split(",");h=h.replace(/\s+/ig,"").replace(/\,+$/ig,"").split(",");l=l.replace(/\s+/ig,"").replace(/\,+$/ig,"").split(",");n%=360;n<0&&(n=360+n);if(a.FCcolor.radialGradient){d=m+Wb++;g=e.createElement("radialGradient").attr({id:d,gradientUnits:J(a.FCcolor.gradientUnits,"userSpaceOnUse"),cx:a.FCcolor.cx,cy:a.FCcolor.cy,r:a.FCcolor.r}).add(e.defs);
for(n=0;n<o.length&&r<100;n+=1)k=o[n].replace(/^#?/,"#"),q=x=M(h[n],q),z=M(l[n],n!==0?(100-r)/(o.length-n):0),r+=z,r>100&&(r=100),e.createElement("stop").attr({offset:r+"%","stop-color":k,"stop-opacity":x/100}).add(g);a.FCcolor.element=g.element;a.FCcolor.url="url('"+this.url+"#"+d+"')";return a.FCcolor.url}else if(o.length===1)return b(d,k+"-opacity",M(h[0],100)/100),o[0].replace(/^#?/,"#");else{var P,p,v,r=0,k="linearGradient",x=a[k],d=m+Wb++;z=Math.tan(n*Math.PI/180);x=Math.round(50-50*z);z=Math.round(50-
50/z);z=z<0?0:z;z=z>100?100:z;x=x<0?0:x;x=x>100?100:x;P=100-z;p=100-x;n>90&&n<=270&&(v=x,x=p,p=v);n>180&&n<=360&&(v=z,z=P,P=v);g=e.createElement(k).attr({id:d,gradientUnits:J(a.FCcolor.gradientUnits,"objectBoundingBox"),x1:z+"%",y1:x+"%",x2:P+"%",y2:p+"%"}).add(e.defs);for(n=0;n<o.length&&r<100;n+=1)k=o[n].replace(/^#?/,"#"),q=x=M(h[n],q),z=M(l[n],n!==0?(100-r)/(o.length-n):0),r+=z,r>100&&(r=100),e.createElement("stop").attr({offset:r+"%","stop-color":k,"stop-opacity":x/100}).add(g);a.FCcolor.element=
g.element;a.FCcolor.url="url('"+this.url+"#"+d+"')";return a.FCcolor.url}}else return c.test(a)?(s=Db(a),b(d,k+"-opacity",s.get("a")),s.get("rgb")):a},text:function(a,d,k){var b=Va.chart.style,d=F(h(d,0)),k=F(h(k,0)),a=this.createElement("text").attr({x:d,y:k,text:a}).css({fontFamily:b.fontFamily,fontSize:b.fontSize});a.x=d;a.y=k;return a}};$b=rc;if(!Ca)db=K(sb,{type:"VML",init:function(a,d){var k=["<",d,' filled="f" stroked="f"'],b=["position: ",z,";"];(d==="shape"||d===j)&&b.push("left:0;top:0;width:10px;height:10px;");
Ab&&b.push("visibility: ",d===j?S:q);k.push(' style="',b.join(""),'"/>');if(d)k=d===j||d==="span"||d==="img"?k.join(""):a.prepVML(k),this.element=Q(k);this.renderer=a},add:function(a){var d=this.renderer,k=this.element,b=d.box,b=a?a.element||a:b;a&&a.inverted&&d.invertChild(k,b);Ab&&b.gVis===S&&R(k,{visibility:S});b.appendChild(k);this.added=!0;this.alignOnAdd&&this.updateTransform();return this},attr:function(a,d){var k,s,c,e,x,g=this.element||{},j=g.style,f=g.nodeName,o=this.renderer,n=this.symbolName,
h,m=this.shadows,z,r=this;A(a)&&t(d)&&(k=a,a={},a[k]=d);if(A(a))k=a,r=k==="strokeWidth"||k==="stroke-width"?this.strokeweight:this[k];else for(k in a){s=a[k];z=!1;if(n&&/^(x|y|r|start|end|width|height|innerR)/.test(k))h||(this.symbolAttr(a),h=!0),z=!0;else if(k==="d"){s=s||[];this.d=s.join(" ");c=s.length;for(e=[];c--;)e[c]=gb(s[c])?F(s[c]*10)-5:s[c]==="Z"?"x":s[c];s=e.join(" ")||"x";g.path=s;if(m)for(c=m.length;c--;)m[c].path=s;z=!0}else if(k==="zIndex"||k==="visibility"){if(Ab&&k==="visibility"&&
f==="DIV"){g.gVis=s;e=g.childNodes;for(c=e.length;c--;)R(e[c],{visibility:s});s===q&&(s=null)}s&&(j[k]=s);z=!0}else if(/^(width|height)$/.test(k))this.updateClipping?(this[k]=s,this.updateClipping()):j[k]=s,z=!0;else if(/^(x|y)$/.test(k))this[k]=s,this.updateClipping?(this[{x:"left",y:"top"}[k]]=s,this.updateClipping()):g.tagName==="SPAN"?this.updateTransform():j[{x:"left",y:"top"}[k]]=s,z=!0;else if(k==="class")g.className=s;else if(k==="stroke")s=o.color(s,g,k),k="strokecolor";else if(k==="stroke-width"||
k==="strokeWidth")g.stroked=s?!0:!1,k="strokeweight",this[k]=s,gb(s)&&(s+=v);else if(k==="stroke-linecap"||k==="strokeLinecap")this[k]=s,k="endcap",s=s==="butt"?"flat":s||"flat",c=g.getElementsByTagName("stroke")[0]||Q(o.prepVML(["<stroke/>"]),null,null,g),c[k]=s,z=!0;else if(k==="dashstyle")c=g.getElementsByTagName("stroke")[0]||Q(o.prepVML(["<stroke/>"]),null,null,g),c[k]=s||"solid",this.dashstyle=s,z=!0;else if(k==="fill")f==="SPAN"?j.color=s:(g.filled=s!==ca?!0:!1,s=o.color(s,g,k),k="fillcolor");
else if(k==="translateX"||k==="translateY"||k==="rotation"||k==="align")k==="align"&&(k="textAlign"),this[k]=s,this.updateTransform(),z=!0;else if(k==="text")this.bBox=null,g.innerHTML=s,z=!0;if(m&&k==="visibility")for(c=m.length;c--;)m[c].style[k]=s;if(m&&(k==="opacity"||k==="stroke-opacity")){x=6*s;c=0;for(e=m.length;c<e;)b(m[c],k,(c+=1)*x)}z||(Ab?g[k]=s:b(g,k,s))}return r},clip:function(a){var d=this,k=a.members;k.push(d);d.destroyClip=function(){f(k,d)};return d.css(a.getCSS(d.inverted))},css:function(a){var d=
this.element;if(d=a&&d.tagName==="SPAN"&&a.width)delete a.width,this.textWidth=d,this.updateTransform();this.styles=y(this.styles,a);R(this.element,a);return this},destroy:function(){this.destroyClip&&this.destroyClip();sb.prototype.destroy.apply(this)},empty:function(){for(var a=this.element.childNodes,d=a.length,k;d--;)k=a[d],k.parentNode.removeChild(k)},getBBox:function(){var a=this.element,d=this.bBox;if(!d){if(a.nodeName==="text")a.style.position=z;d=this.bBox={x:a.offsetLeft,y:a.offsetTop,width:a.offsetWidth,
height:a.offsetHeight}}return d},on:function(a,d){this.element["on"+a]=function(){var a=hb.event;a.target=a.srcElement;d(a)};return this},updateTransform:function(){if(this.added){var a=this,d=a.element,k=a.translateX||0,b=a.translateY||0,c=a.x||0,e=a.y||0,g=a.textAlign||"left",j={left:0,center:0.5,right:1}[g],f=g&&g!=="left";(k||b)&&a.css({marginLeft:k,marginTop:b});a.inverted&&B(d.childNodes,function(k){a.renderer.invertChild(k,d)});if(d.tagName==="SPAN"){var o,n,k=a.rotation,h;o=0;var b=1,m=0,
z;h=p(a.textWidth);var r=a.xCorr||0,q=a.yCorr||0,l=[k,g,d.innerHTML,a.textWidth].join(",");if(l!==a.cTT)t(k)&&(o=k*zb,b=Ja(o),m=Ma(o),R(d,{filter:k?["progid:DXImageTransform.Microsoft.Matrix(M11=",b,", M12=",-m,", M21=",m,", M22=",b,", sizingMethod='auto expand')"].join(""):ca})),o=d.offsetWidth,n=d.offsetHeight,o>h&&(R(d,{width:h+v,display:"block",whiteSpace:"normal"}),o=h),h=F((p(d.style.fontSize)||12)*1.2),r=b<0&&-o,q=m<0&&-n,z=b*m<0,r+=m*h*(z?1-j:j),q-=b*h*(k?z?j:1-j:1),f&&(r-=o*j*(b<0?-1:1),
k&&(q-=n*j*(m<0?-1:1)),R(d,{textAlign:g})),a.xCorr=r,a.yCorr=q;R(d,{left:c+r,top:e+q});a.cTT=l}}else this.alignOnAdd=!0},rotate:function(a){var d=this.element,k=0;if(t(a))a=Number(a),k=a*zb,Ja(k),Ma(k),Ab?d.CoordOrigin="0 0":b(d,"CoordOrigin","0 0"),R(d,{rotation:a,left:0,top:0})},shadow:function(a,d,k){k||(k={});var b=[],c=this.element,e=this.renderer,g,j=c.style,f,o=c.path,n=k.color||"black",h=p(this.attr("stroke-width")||1,10)+6,m=J(k.opacity,1)*0.06,k=!this.parentInverted!=!k.inverted?-1:1;o&&
typeof o.value!=="string"&&(o="x");if(a){for(a=1;a<=3;a++)f=['<shape isShadow="true" strokeweight="',h-2*a,'" filled="false" path="',o,'" coordsize="100,100" style="',j.cssText,'" />'],g=Q(e.prepVML(f),null,{left:p(j.left)+k,top:p(j.top)+k}),f=['<stroke endcap="round" color="',n,'" opacity="',m*a,'"/>'],Q(e.prepVML(f),null,null,g),d?d.element.appendChild(g):c.parentNode.insertBefore(g,c),b.push(g);this.shadows=b}return this},textBound:function(){var a=this.styles;if(this.element.nodeName.toLowerCase()===
"span"&&a&&(a.backgroundColor||a.borderColor)&&this.element.innerHTML=="")return R(this.element,{backgroundColor:ub,borderColor:ub,border:ub}),this;a&&a.backgroundOpacity&&this.attr({opacity:a.backgroundOpacity});return this}}),da=function(){this.init.apply(this,arguments)},da.prototype=ka(rc.prototype,{Element:db,isIE8:ib.indexOf("MSIE 8.0")>-1,init:function(a,d,k){var b;this.alignedObjects=[];b=this.createElement(j);a.appendChild(b.element);this.box=b.element;this.boxWrapper=b;this.setSize(d,k,
!1);if(!Aa.namespaces.hcv)Aa.namespaces.add("hcv","urn:schemas-microsoft-com:vml"),Aa.createStyleSheet().cssText="hcv\\:fill, hcv\\:path, hcv\\:shape, hcv\\:stroke{ behavior:url(#default#VML); display: inline-block; } "},clipRect:function(a,d,k,b){var c=this.createElement();return y(c,{members:[],left:a,top:d,width:k,height:b,getCSS:function(a){var d=this.top,k=this.left,b=k+this.width,c=d+this.height,d={clip:"rect("+F(a?k:d)+"px,"+F(a?c:b)+"px,"+F(a?b:c)+"px,"+F(a?d:k)+"px)"};!a&&Ab&&y(d,{width:(b<
0?0:b)+v,height:(c<0?0:c)+v});return d},updateClipping:function(){B(c.members,function(a){a.css(c.getCSS(a.inverted))})}})},getArcPath:function(a,d,k,b,c,e,g,j,f){return[f?"WA":"AT",a-g,d-j,a+g,d+j,k,b,c,e]},color:function(a,d,k){var b,c=/^rgba/;if(a&&a.linearGradient){var e,g,j=a.linearGradient,f,o,n,h;B(a.stops,function(a,d){c.test(a[1])?(b=Db(a[1]),e=b.get("rgb"),g=b.get("a")):(e=a[1],g=1);d?(n=e,h=g):(f=e,o=g)});a=90-O.atan((j[3]-j[1])/(j[2]-j[0]))*180/pb;k=["<",k,' colors="0% ',f,",100% ",n,
'" angle="',a,'" opacity="',h,'" o:opacity2="',o,'" type="gradient" focus="100%" />'];Q(this.prepVML(k),null,null,d)}else if(a&&a.FCcolor){var j=J(a.FCcolor.color,"000000"),m=J(a.FCcolor.alpha,"100"),z=J(a.FCcolor.ratio,"0"),r=M(a.FCcolor.angle,"0"),q=100,j=j.replace(/\s+/ig,"").replace(/\,+$/ig,"").split(","),m=m.replace(/\s+/ig,"").replace(/\,+$/ig,"").split(","),z=z.replace(/\s+/ig,"").replace(/\,+$/ig,"").split(",");r-=90;r*=-1;r%=360;r<0&&(r=360+r);if(j.length===1)return k=["<",k,' opacity="',
M(m[0],100)/100,'"/>'],Q(this.prepVML(k),null,null,d),j[0].replace(/^#?/,"#");else{var l,P,p,v,t=0,D="",S=!0;f=j[0].replace(/^#?/,"#");o=M(m[0],100)/100;for(l=0;l<j.length&&t<100;l+=1)P=j[l].replace(/^#?/,"#"),q=p=M(m[l],q),v=M(z[l],l!==0?(100-t)/(j.length-l):0),t+=v,t>100&&(t=100),S&&(t>0&&(D+="0% "+P+","),S=!1),D+=t+"% "+P+",";t<100&&(D+="100% "+P+",");n=P;h=p/100;if(a.FCcolor.radialGradient)if(a.FCcolor.gradientUnits!=="objectBoundingBox")return k=["<",k,' opacity="',M(a.FCcolor.defaultAlpha,m[0],
100)/100,'"/>'],Q(this.prepVML(k),null,null,d),J(a.FCcolor.defaultColor,j[0],"000000").replace(/^#?/,"#");else k=["<",k,' colors="',D,'" color="',f,'" color2="',n,'" focusposition="',a.FCcolor.cx+","+a.FCcolor.cy,'" opacity="',h,'" o:opacity2="',o,'" type="gradientTitle" focus="100%" />'];else k=["<",k,' colors="',D,'" angle="',r,'" color="',f,'" color2="',n,'" opacity="',h,'" o:opacity2="',o,'" type="gradient" focus="100%" />'];Q(this.prepVML(k),null,null,d)}}else return c.test(a)&&d.tagName!=="IMG"?
(b=Db(a),k=["<",k,' opacity="',b.get("a"),'"/>'],Q(this.prepVML(k),null,null,d),b.get("rgb")):a},prepVML:function(a){var d=this.isIE8,a=a.join("");d?(a=a.replace("/>",' xmlns="urn:schemas-microsoft-com:vml" />'),a=a.indexOf('style="')===-1?a.replace("/>",' style="display:inline-block;behavior:url(#default#VML);" />'):a.replace('style="','style="display:inline-block;behavior:url(#default#VML);')):a=a.replace("<","<hcv:");return a},text:function(a,d,k){var b=Va.chart.style;return this.createElement("span").attr({text:a,
x:F(d),y:F(k)}).css({whiteSpace:"nowrap",fontFamily:b.fontFamily,fontSize:b.fontSize})},path:function(a){return this.createElement("shape").attr({coordsize:"100 100",d:a})},circle:function(a,d,k){return this.symbol("circle").attr({x:a,y:d,r:k})},g:function(a){var d;a&&(d={className:m+a,"class":m+a});return this.createElement(j).attr(d)},image:function(a,d,k,b,c){var e=this.createElement("img").attr({src:a});arguments.length>1&&e.css({left:d,top:k,width:b,height:c});return e},rect:function(a,d,k,b,
c,e){if(w(a))d=a.y,k=a.width,b=a.height,c=a.r,e=a.strokeWidth,a=a.x;var g=this.symbol("rect");g.r=c;return g.attr(g.crisp(e,a,d,na(k,0),na(b,0)))},invertChild:function(a,d){var k=d.style;R(a,{flip:"x",left:p(k.width)-10,top:p(k.height)-10,rotation:-90})},symbols:{arc:function(a,d,k,b){var c=b.start,e=b.end,g=Ja(c),j=Ma(c),f=Ja(e),o=Ma(e),b=b.innerR,n=0.07/k,h=b&&0.1/b||0;if(e-c===0)return["x"];else 2*pb-e+c<n?f=-n:e-c<h&&(f=Ja(c+h));return["wa",a-k,d-k,a+k,d+k,a+k*g,d+k*j,a+k*f,d+k*o,"at",a-b,d-b,
a+b,d+b,a+b*f,d+b*o,a+b*g,d+b*j,"x","e"]},circle:function(a,d,k){return["wa",a-k,d-k,a+k,d+k,a+k,d,a+k,d,"e"]},rect:function(a,d,k,b){if(!t(b))return[];var c=b.width,b=b.height,e=a+c,g=d+b,k=nb(k,c,b);return[n,a+k,d,D,e-k,d,"wa",e-2*k,d,e,d+2*k,e-k,d,e,d+k,D,e,g-k,"wa",e-2*k,g-2*k,e,g,e,g-k,e-k,g,D,a+k,g,"wa",a,g-2*k,a+2*k,g,a+k,g,a,g-k,D,a,d+k,"wa",a,d,a+2*k,d+2*k,a,d+k,a+k,d,"x","e"]}}}),$b=da;Gb.prototype.callbacks=[];var Ub=function(){};Ub.prototype={init:function(a,d){var b=a.chart.counters,
c;this.series=a;this.applyOptions(d);this.pointAttr={};if(a.options.colorByPoint){c=a.chart.options.colors;if(!this.options)this.options={};this.color=this.options.color=this.color||c[b.color++];b.wrapColor(c.length)}a.chart.pointCount++;return this},applyOptions:function(a){var d=this.series;this.config=a;if(gb(a)||a===null)this.y=a;else if(w(a)&&!gb(a.length))y(this,a),this.options=a;else if(A(a[0]))this.name=a[0],this.y=a[1];else if(gb(a[0]))this.x=a[0],this.y=a[1];if(this.x===e)this.x=d.autoIncrement()},
destroy:function(){var a=this,d=a.series,b;d.chart.pointCount--;if(a===d.chart.hoverPoint)a.onMouseOut();d.chart.hoverPoints=null;vb(a);B(["graphic","tracker","group","dataLabel","connector"],function(d){a[d]&&a[d].destroy()});a.legendItem&&a.series.chart.legend.destroyItem(a);for(b in a)a[b]=null},getLabelConfig:function(){return{x:this.category,y:this.y,series:this.series,point:this,percentage:this.percentage,total:this.total||this.stackTotal}},select:function(a,d){var b=this,c=b.series.chart;b.selected=
a=h(a,!b.selected);b.firePointEvent(a?"select":"unselect");b.setState(a&&"select");d||B(c.getSelectedPoints(),function(a){if(a.selected&&a!==b)a.selected=!1,a.setState(xa),a.firePointEvent("unselect")})},onMouseOver:function(){var a=this.series.chart,d=a.tooltip,b=a.hoverPoint;if(b&&b!==this)b.onMouseOut();this.firePointEvent("mouseOver");d&&!d.shared&&d.refresh(this);this.link!==void 0&&this.series.tracker&&this.series.tracker.css({cursor:"pointer"});this.setState(Y);a.hoverPoint=this},onMouseOut:function(){this.firePointEvent("mouseOut");
this.setState();this.series.chart.hoverPoint=null;this.link!==void 0&&this.series.tracker&&this.series.tracker.css({cursor:"auto"})},tooltipFormatter:function(a){var d=this.series;return['<span style="color:'+d.color+'">',this.name||d.name,"</span>: ",!a?"<b>x = "+(this.name||this.x)+",</b> ":"","<b>",!a?"y = ":"",this.y,"</b>"].join("")},update:function(a,d,b){var c=this,e=c.series,g=c.graphic,x=e.chart,d=h(d,!0);c.firePointEvent("update",{options:a},function(){c.applyOptions(a);w(a)&&(e.getAttribs(),
g&&g.attr(c.pointAttr[e.state]));e.isDirty=!0;d&&x.redraw(b)})},remove:function(a,d){var b=this,c=b.series,e=c.chart,g=c.data;sa(d,e);a=h(a,!0);b.firePointEvent("remove",null,function(){f(g,b);b.destroy();c.isDirty=!0;a&&e.redraw()})},firePointEvent:function(a,d,b){var c=this,e=this.series.options;(e.point.events[a]||c.options&&c.options.events&&c.options.events[a])&&this.importEvents();a==="click"&&e.allowPointSelect&&(b=function(a){c.select(null,a.ctrlKey||a.metaKey||a.shiftKey)});Ya(this,a,d,b)},
importEvents:function(){if(!this.hasImportedEvents){var a=ka(this.series.options.point,this.options).events,d;this.events=a;for(d in a)ha(this,d,a[d]);this.hasImportedEvents=!0}},setState:function(a){var d=this.series,b=d.options.states,c=$[d.type].marker&&d.options.marker,e=c&&!c.enabled,g=(c=c&&c.states[a])&&c.enabled===!1,x=d.stateMarkerGraphic,j=d.chart,f=this.pointAttr,a=a||xa;if(!(a===this.state||this.selected&&a!=="select"||b[a]&&b[a].enabled===!1||a&&(g||e&&!c.enabled))){if(this.graphic)this.graphic.attr(f[a]);
else{if(a){if(!x)d.stateMarkerGraphic=x=j.renderer.circle(0,0,f[a].r).attr(f[a]).add(d.group);x.translate(this.plotX,this.plotY)}if(x)x[a?"show":"hide"]()}this.state=a}}};var ob=function(){};ob.prototype={isCartesian:!0,type:"line",pointClass:Ub,pointAttrToOptions:{stroke:"lineColor","stroke-width":"lineWidth",fill:"fillColor",r:"radius"},init:function(a,d){var b,c;c=a.series.length;this.chart=a;d=this.setOptions(d);y(this,{index:c,options:d,name:J(d.name)===void 0?"":d.name,state:xa,pointAttr:{},
visible:d.visible!==!1,selected:d.selected===!0});c=d.events;for(b in c)ha(this,b,c[b]);if(c&&c.click||d.point&&d.point.events&&d.point.events.click||d.allowPointSelect)a.runTrackerClick=!0;this.getColor();this.getSymbol();this.setData(d.data,!1)},autoIncrement:function(){var a=this.options,d=this.xIncrement,d=h(d,a.pointStart,0);this.pointInterval=h(this.pointInterval,a.pointInterval,1);this.xIncrement=d+this.pointInterval;return d},cleanData:function(){var a=this.chart,d=this.data,b,c,g=a.smallestInterval,
j,x;d.sort(function(a,d){return a.x-d.x});if(this.options.connectNulls)for(x=d.length-1;x>=0;x--)d[x].y===null&&d[x-1]&&d[x+1]&&d.splice(x,1);for(x=d.length-1;x>=0;x--)if(d[x-1]&&(j=d[x].x-d[x-1].x,j>0&&(c===e||j<c)))c=j,b=x;if(g===e||c<g)a.smallestInterval=c;this.closestPoints=b},getSegments:function(){var a=-1,d=[],b=this.data;this.options.connectNullData?(B(b,function(a,c){typeof a.y=="number"&&d.push(b[c])}),d=[d]):B(b,function(c,e){typeof c.y!="number"?(e>a+1&&d.push(b.slice(a+1,e)),a=e):e==
b.length-1&&d.push(b.slice(a+1,e+1))});this.segments=d},setOptions:function(a){var d=this.chart.options.plotOptions;return ka(d[this.type],d.series,a)},getColor:function(){var a=this.chart.options.colors,d=this.chart.counters;this.color=this.options.color||a[d.color++]||"#0000ff";d.wrapColor(a.length)},getSymbol:function(){var a=this.chart.options.symbols,d=this.chart.counters;this.symbol=this.options.marker.symbol||a[d.symbol++];d.wrapSymbol(a.length)},addPoint:function(a,d,b,c){var e=this.data,
g=this.graph,x=this.area,j=this.chart,a=(new this.pointClass).init(this,a);sa(c,j);if(g&&b)g.shift=b;if(x)x.shift=b,x.isArea=!0;d=h(d,!0);e.push(a);b&&e[0].remove(!1);this.getAttribs();this.isDirty=!0;d&&j.redraw()},setData:function(a,d){var b=this,c=b.data,e=b.initialColor,g=b.chart,x=c&&c.length||0;b.xIncrement=null;if(t(e))g.counters.color=e;for(a=gc(l(a||[]),function(a){return(new b.pointClass).init(b,a)});x--;)c[x].destroy();b.data=a;b.cleanData();b.getSegments();b.getAttribs();b.isDirty=!0;
g.isDirtyBox=!0;h(d,!0)&&g.redraw(!1)},remove:function(a,d){var b=this,c=b.chart,a=h(a,!0);if(!b.isRemoving)b.isRemoving=!0,Ya(b,"remove",null,function(){b.destroy();c.isDirtyLegend=c.isDirtyBox=!0;a&&c.redraw(d)});b.isRemoving=!1},translate:function(){for(var a=this.chart,d=this.options.stacking,b=this.xAxis.categories,c=this.yAxis,g=this.data,j=g.length;j--;){var x=g[j],f=x.x,o=x.y,n=x.low,h=c.stacks[(o<0?"-":"")+this.stackKey];x.plotX=this.xAxis.translate(f);if(d&&h&&h[f]){var f=h[f],h=f.total,
m=c.options.min,n=f.cum;f.cum+=o;o=na(m,n+o);n=na(m,n);d==="percent"&&(n=h?n*100/h:0,o=h?o*100/h:0);x.percentage=h?x.y*100/h:0;x.stackTotal=h}if(t(n))x.yBottom=c.translate(n,0,1,0,1);if(o!==null)x.plotY=c.translate(o,0,1,0,1);x.clientX=a.inverted?a.plotHeight-x.plotX:x.plotX;x.category=b&&b[x.x]!==e?b[x.x]:x.x}},setTooltipPoints:function(a){var d=this.chart,b=d.inverted,c=[],e=F((b?d.plotTop:d.plotLeft)+d.plotSizeX),g,x,j=[];if(a)this.tooltipPoints=null;B(this.segments,function(a){c=c.concat(a)});
this.xAxis&&this.xAxis.reversed&&(c=c.reverse());B(c,function(a,d){g=c[d-1]?c[d-1]._high+1:0;for(x=a._high=c[d+1]?mb((a.plotX+(c[d+1]?c[d+1].plotX:e))/2):mb(a.plotX+30);g<=x;)j[b?e-g++:g++]=a});this.tooltipPoints=j},onMouseOver:function(){var a=this.chart,d=a.hoverSeries;if($a||!a.mouseIsDown){if(d&&d!==this)d.onMouseOut();this.options.events.mouseOver&&Ya(this,"mouseOver");this.tracker&&this.tracker.toFront();this.setState(Y);a.hoverSeries=this}},onMouseOut:function(){var a=this.options,d=this.chart,
b=d.tooltip,c=d.hoverPoint;if(c)c.onMouseOut();this&&a.events.mouseOut&&Ya(this,"mouseOut");b&&!a.stickyTracking&&b.hide();this.setState();d.hoverSeries=null},animate:function(a){var d=this.clipRect,b=this.options.animation;b&&!w(b)&&(b={});if(a){if(!d.isAnimating)d.attr({width:0}),d.isAnimating=!0}else d.animate({width:d.cliprectW},b),this.animate=null},drawPoints:function(){var a,d=this.data,b=this.chart,c=this.options,g,j,x,f,o,m;if(this.options.marker.enabled)for(x=d.length;x--;)if(f=d[x],g=f.plotX,
j=f.plotY,m=f.graphic,j!==e&&!isNaN(j)){var z,r,q;a=[];var l;if(f.errorValue)z=f.errorValue,q=g,o=j,l=c.errorBarWidth,l/=2,a.push(n,q,o,D),r=this.yAxis.translate(z,0,0,0,0,1),z=o-r,r=o+r,a.push(q,z,n,q+l,z,D,q-l,z),c.halfErrorBar===0&&a.push(n,q,o,D,q,r,n,q-l,r,D,q+l,r),f.errorPath=a,(o=f.errorGraph)?(ea(o),o.animate({d:a})):f.errorGraph=b.renderer.path(a).attr({"stroke-width":c.errorBarThickness,stroke:c.errorBarColor}).add(this.group);if(f.vErrorValue)z=f.vErrorValue,q=g,o=j,l=c.verticalErrorBarWidth,
l/=2,a.push(n,q,o,D),r=this.yAxis.translate(z,0,0,0,0,1),z=o-r,r=o+r,a.push(q,z,n,q+l,z,D,q-l,z),c.halfVerticalErrorBar===0&&a.push(n,q,o,D,q,r,n,q-l,r,D,q+l,r),f.vErrorPath=a,(o=f.vErrorGraph)?(ea(o),o.animate({d:a})):f.vErrorGraph=b.renderer.path(a).attr({"stroke-width":c.verticalErrorBarThickness,stroke:c.verticalErrorBarColor}).add(this.group);if(f.hErrorValue)z=f.hErrorValue,q=g,o=j,l=c.horizontalErrorBarWidth,l/=2,a.push(n,q,o,D),z=this.xAxis.translate(z,0,0,0,0,1),a.push(q+z,o,n,q+z,o+l,D,
q+z,o-l),c.halfHorizontalErrorBar===0&&a.push(n,q,o,D,q-z,o,n,q-z,o+l,D,q-z,o-l),f.hErrorPath=a,(o=f.hErrorGraph)?(ea(o),o.animate({d:a})):f.hErrorGraph=b.renderer.path(a).attr({"stroke-width":c.verticalErrorBarThickness,stroke:c.verticalErrorBarColor}).add(this.group);a=f.pointAttr[f.selected?"select":xa];o=a.r;m?m.animate({x:g,y:j,r:o}):f.graphic=b.renderer.symbol(h(f.marker&&f.marker.symbol,this.symbol),g,j,o).attr(a).add(this.group)}},convertAttribs:function(a,d,b,c){var e=this.pointAttrToOptions,
g,j,f={},a=a||{},d=d||{},b=b||{},c=c||{};for(g in e)j=e[g],f[g]=h(a[j],d[g],b[g],c[g]);return f},getAttribs:function(){var a=this,d=$[a.type].marker?a.options.marker:a.options,b=d.states,c=b[Y],e,g=a.color,j={stroke:g,fill:g},f=a.data,o=[],n,h=a.pointAttrToOptions,z;a.options.marker?(c.radius=c.radius||d.radius+2,c.lineWidth=c.lineWidth||d.lineWidth+1):c.color=c.color||Db(c.color||g).brighten(c.brightness).get();o[xa]=a.convertAttribs(d,j);B([Y,"select"],function(d){o[d]=a.convertAttribs(b[d],o[xa])});
a.pointAttr=o;for(g=f.length;g--;){j=f[g];if((d=j.options&&j.options.marker||j.options)&&d.enabled===!1)d.radius=0;e=!1;if(j.options)for(z in h)t(d[h[z]])&&(e=!0);if(e){n=[];b=d.states||{};e=b[Y]=b[Y]||{};if(!a.options.marker)e.color=Db(e.color||j.options.color).brighten(e.brightness||c.brightness).get();n[xa]=a.convertAttribs(d,o[xa]);n[Y]=a.convertAttribs(b[Y],o[Y],n[xa]);n.select=a.convertAttribs(b.select,o.select,n[xa])}else n=o;j.pointAttr=n}},destroy:function(){var a=this,d=a.chart,b=/\/5[0-9\.]+ (Safari|Mobile)\//.test(ib),
c,e;Ya(a,"destroy");vb(a);a.legendItem&&a.chart.legend.destroyItem(a);B(a.data,function(a){a.destroy()});a.graphLine&&B(a.graphLine,function(a){a.destroy()});B(["area","graph","dataLabelsGroup","group","tracker"],function(d){a[d]&&(c=b&&d==="group"?"hide":"destroy",a[d][c]())});if(d.hoverSeries===a)d.hoverSeries=null;f(d.series,a);for(e in a)delete a[e]},drawDataLabels:function(){if(this.options.dataLabels.enabled){var a=this,d,b,c=a.data,e=a.options.dataLabels,g,j=a.dataLabelsGroup,f=a.chart,o=f.renderer,
n=f.options.chart,z=f.inverted,m=a.type,r=a.options.stacking,l=/^(column|column3d|bar|bar3d|floatedcolumn)$/.test(m),P=e.verticalAlign===null,v=e.y===null,D=f.plotWidth,w=f.plotHeight,u=o.smartLabel,ca,y,Y,xa,C,G,E,A,Da,K=/^(line)$/.test(m),N=a.depthYDisplacement||0,Na=a.depthXDisplacement||0,T,Ka=e.style.fontSize.replace(/px/,""),ua=o.box.nodeName=="svg"&&!r?2:0,J=n.valuePosition&&n.valuePosition.toLowerCase(),Z=!1,ba=n.valuePadding,F=2+ba,$=m=="floatedcolumn";u.setStyle(e.style);if(l)r?(P&&(e=ka(e,
{verticalAlign:"middle"})),v&&(e=ka(e,{y:{top:14,middle:4,bottom:-6}[e.verticalAlign]})),e.align="center"):P&&(e=ka(e,{verticalAlign:"top"}));if(!/^(bar|pie|bar3d)$/.test(m))e.rotation=xa=n.rotateValues==1?270:void 0;/^(column|column3d|bar|bar3d|floatedcolumn)$/.test(m)?ca=n.placeValuesInside:K&&(ca=J==="below");if(!j)j=a.dataLabelsGroup=o.g("data-labels").attr({visibility:a.visible?q:S,zIndex:6}).translate(f.plotLeft,f.plotTop).add(),f.options.chart.hasScroll&&j.clip(a.clipRect);f=e.color;f==="auto"&&
(f=null);e.style.color=h(f,a.color);B(c,function(f,n){var aa=f.barX,q=aa&&aa+f.barW/2||(f.plotX!=null?f.plotX:-999),P=h(f.plotY,-999),ga=f.dataLabel,S=e.align,B=v?f.y>=0?-6:12:e.y;A=e.formatter.call(f.getLabelConfig());if(A!=null){$&&(P=M(f.barY,P));E=u.getOriSize(A,void 0,void 0,!1);g=E.text;d=(z?D-P:q)+e.x;b=(z?w-q:P)+B;Da=void 0;K&&!J&&(ca=0,c[n-1]&&c[n-1].plotY<f.plotY&&(ca=1));Z=f.y<0;q=Number($?f.barY:f.plotY);if(xa){S="left";B=Z?-4-ba:B+2-ba;Y=E.width-B;y=E.height-B;T=E.width;C=M(f.barH,w-
q);G=q;if(Y<=G){b=G+B;if(Z){b=P-B+N;S="right";if(ca&&C>Y||Y>w-P)b=P+B+N,S="left";d+=Na}ca&&!Z&&f.y!=0&&Y<=C&&(S=Z?"center":"right",b=G-B+N,d+=Na)}else Y<=C?(S="right",b=G-B+N,d+=Na,Z&&(b=G-B+N)):(b=G+B,Da=G,C>G&&$&&(b=w+N,d+=Na,Da=C),b<T&&(b=0,S="right"));d+=Ka/2-ua}else B=Z?-4-ba:B+2-ba,y=E.width+F,Y=Ka-B,T=Ka,C=w-q,G=q,/^(bar|bar3d)$/.test(m)?(b-=ua,C=f.barH,G=D-f.barH,Z?(G=D-q,ca&&C>y||y>G&&C>y?(d=G+ba+2+Na,b+=N):y<G?(d=G-(ba+2)+Na,b+=N,S="right"):(d=6,S="left")):(G=q,ca&&C>y||y>G&&C>y?(d=D-G-
(ba+2)+Na,S="right",b+=N):y<G?d=D-G+(ba+2):(d=D-6,b+=N,S="right")),d<0&&(d=6,S="left"),d>D&&(d=D-6,S="right")):(Z?(G=M(f.barH,q),b=ca&&G>Y||Y>C?P+B+N:P+Y+N,d+=Na):(C=M(f.barH,w-q),ca&&C>=Y&&f.y!=0||Y>G?(b=P+Y+N,d+=Na):b=P+B,m=="bubble"&&(b=P-B/2)),b>w&&(b=w),b<0&&(b=T));S=r?"center":S;if(Da)E=u.getOriSize(A,Da,void 0,!0),g=E.text;if(ga)z&&!e.y&&(b=b+p(ga.styles.lineHeight)*0.9-ga.getBBox().height/2),ga.attr({text:g}).animate({x:d,y:b});else if(t(g)&&(ga=f.dataLabel=o.text(g,d,b).attr({align:S,rotation:e.rotation,
zIndex:1}).css(e.style).add(j),z&&!e.y&&ga.attr({y:b+p(ga.styles.lineHeight)*0.9-ga.getBBox().height/2}),l&&a.options.stacking))P=f.barY,S=f.barW,B=f.barH,ga.align(e,null,{x:z?D-P-B:aa+Na,y:z?w-aa-S+Ka/2+N:P,width:z?B:S,height:z?S:B})}})}},drawGraph:function(){var a=this,d=a.options,b=[],c,e=a.area,g=a.group,f=d.lineColor||a.color,j=d.lineWidth,o=d.dashStyle,z,m=a.chart.renderer,r=a.yAxis.getThreshold(d.threshold||0),q=/^area/.test(a.type),l=[],P=[],p,v=[],t=[],S,w=0,u=[],ca=a.shadowGroup;if(!ca&&
d.shadow)ca=a.shadowGroup=m.g("shadow").add(g),ca.floated=!0;B(a.segments,function(c){z=[];B(c,function(b,k){if(a.getPointSpline){z.push.apply(z,a.getPointSpline(c,b,k));var e=a.getPointSpline(c,b,k);e[0]=="C"?(g=c[k-1],v.push(u.concat(e)),t.push(g.config),w++,u=["M"].concat(e.slice(e.length-2))):u=e}else{z.push(k?D:n);if(k&&d.step){var g=c[k-1];z.push(b.plotX,g.plotY)}k&&(g=c[k-1],v[w]=[],d.step?(v[w].push(n,g.plotX,g.plotY,D,b.plotX,g.plotY),d.drawVerticalJoins&&v[w].push(b.plotX,b.plotY)):v[w].push(n,
g.plotX,g.plotY,D,b.plotX,b.plotY),t.push(g.config),w++);z.push(b.plotX,b.plotY)}});c.length>1?b=b.concat(z):l.push(c[0]);if(q){var e=[],g,s=z.length;for(g=0;g<s;g++)e.push(z[g]);s===3&&e.push(D,z[1],z[2]);if(d.stacking&&a.type!=="areaspline")for(g=c.length-1;g>=0;g--)e.push(c[g].plotX,c[g].yBottom);else e.push(D,c[c.length-1].plotX,r,D,c[0].plotX,r);P=P.concat(e)}});if(q){var y=[];B(v,function(b){var k=[],c,e=b.length;for(c=0;c<e;c++)k.push(b[c]);if(d.stacking&&a.type!=="areaspline")for(c=v.length-
1;c>=0;c--);else k.push(D,b[b.length-2],r,D,b[1],r);y.push(k)})}a.graphPath=b;a.singlePoints=l;if(q)c=h(d.fillColor,Db(a.color).setOpacity(d.fillOpacity||0.75).get()),e?e.animate({d:P}):a.area=a.chart.renderer.path(P).attr({fill:c}).add(g);var Y,xa;if(q||a.getPointSpline)if(xa=a.graphLine,p={stroke:f,"stroke-width":j,"stroke-linecap":"round",dashstyle:o},xa)xa.animate({d:z});else{if(j)S=t,a.graphLine=m.path(z).attr(p).add(g).shadow(d.shadow,ca,S.shadow)}else{if(!a.graphLine)a.graphLine=[];B(v,function(b,
c){Y=a.graphLine;(xa=Y[c])?xa.animate({d:b}):j&&(S=t[c],p={stroke:J(S.color,f),"stroke-width":j,"stroke-linecap":"round",dashstyle:J(S.dashStyle,o)},Y[c]=m.path(b).attr(p).add(g).shadow(d.shadow,ca,S.shadow))})}},render:function(){var a=this,d=a.chart,b,c,e=a.options,g=e.animation,f=g&&a.animate,g=f?g&&g.duration||500:0,j=a.clipRect,o=d.renderer;if(!j){var n=0,h=0,z=d.plotSizeX,m=d.plotSizeY;d.options.chart.hasScroll||(n=-(d.inverted?d.plotTop:d.plotLeft),h=-(d.inverted?d.plotLeft:d.plotTop),z=d.inverted?
d.chartHeight:d.chartWidth,m=d.inverted?d.chartWidth:d.chartHeight);j=a.clipRect=!d.hasRendered&&d.clipRect?d.clipRect:o.clipRect(n,h,z,m);j.cliprectX=n;j.cliprectY=h;j.cliprectW=z;j.cliprectH=m;if(!d.clipRect)d.clipRect=j}if(!a.group)b=a.group=o.g("series"),d.inverted&&(c=function(){b.attr({width:d.plotWidth,height:d.plotHeight}).invert()},c(),ha(d,"resize",c),ha(a,"destroy",function(){vb(d,"resize",c)})),b.clip(a.clipRect).attr({visibility:a.visible?q:S,zIndex:e.zIndex}).translate(d.plotLeft,d.plotTop).add(d.seriesGroup);
a.drawDataLabels();f&&a.animate(!0);a.drawGraph&&a.drawGraph();a.drawPoints();a.options.enableMouseTracking!==!1&&a.drawTracker();f&&a.animate();setTimeout(function(){j.isAnimating=!1;if((b=a.group)&&j!==d.clipRect&&j.renderer)b.clip(a.clipRect=d.clipRect),j.destroy()},g);a.isDirty=!1},redraw:function(){var a=this.chart,d=this.group;d&&(a.inverted&&d.attr({width:a.plotWidth,height:a.plotHeight}),d.animate({translateX:a.plotLeft,translateY:a.plotTop}));this.translate();this.setTooltipPoints(!0);this.render()},
setState:function(a){var d=this.options,b=this.graph,c=d.states,d=d.lineWidth,a=a||xa;if(this.state!==a)this.state=a,c[a]&&c[a].enabled===!1||(a&&(d=c[a].lineWidth||d+1),b&&!b.dashstyle&&b.attr({"stroke-width":d},a?0:500))},setVisible:function(a,d){var b=this.chart,c=this.legendItem,g=this.group,f=this.tracker,j=this.dataLabelsGroup,o=this.shadowGroup,n,h=this.data,z=b.options.chart.ignoreHiddenSeries;n=this.visible;n=(this.visible=a=a===e?!n:a)?"show":"hide";if(g)g[n]();if(o&&o.floated)o[n]();if(f)f[n]();
else for(g=h.length;g--;)if(f=h[g],f.tracker)f.tracker[n]();if(j)j[n]();c&&b.legend.colorizeItem(this,a);this.isDirty=!0;this.options.stacking&&B(b.series,function(a){if(a.options.stacking&&a.visible)a.isDirty=!0});if(z)b.isDirtyBox=!0;d!==!1&&b.redraw();Ya(this,n)},show:function(){this.setVisible(!0)},hide:function(){this.setVisible(!1)},scroll:function(a,d,b){var c=this.chart,e=this.clipRect,g=c.plotLeft-a,f=c.plotTop-d,j=this.data,o=0,n=j.length;this.group.translate(g,f);this.dataLabelsGroup.translate(g,
f);c.trackerGroup.translate(g,f);e.attr({x:a,y:d});if(b===void 0||b){for(;o<n;o+=1)if(b=j[o],b.y!==null)c=b.plotX-a+20,e=b.plotY-d-15,c=c<0?0:c,e=e<0?0:e,b.tooltipPos=[c,e];this.xShift=F(a);this.yShift=F(d)}},select:function(a){this.selected=a=a===e?!this.selected:a;if(this.checkbox)this.checkbox.checked=a;Ya(this,a?"select":"unselect")},drawTracker:function(){var a=this,d=a.options,b=[].concat(a.graphPath),c=b.length,e=a.chart,g=e.options.tooltip.snap,f=a.tracker,j=d.cursor,o=j&&{cursor:j},j=a.singlePoints,
h;e.options.chart.hasScroll&&e.trackerGroup.clip(a.clipRect);var z,m,l,P,v=+new Date;B(a.data,function(b){if(z=b.errorValue)m=b.errorPath,(l=b.errorTracker)?l.attr({d:m}):(P={series:a,chart:e,id:b.id,label:b.label,options:b.options,svgElm:b.svgElm,toolText:z+"",getLabelConfig:b.getLabelConfig},b.errorTracker=e.renderer.path(m).attr({"stroke-width":d.errorBarThickness,stroke:r,isTracker:v,fill:r,visibility:a.visible?q:S,zIndex:1}).on($a?"touchstart":"mouseover",function(a,d){return function(b){var c=
d.plotLeft,k=d.plotTop;a.tooltipPos=[J(b.layerX,b.x)-c+20,J(b.layerY,b.y)-k-15];d.tooltip.refresh(a)}}(P,e)).on("mousemove",function(a,d){return function(b){var c=d.plotLeft,k=d.plotTop;a.tooltipPos=[J(b.layerX,b.x)-c+20,J(b.layerY,b.y)-k-15];d.tooltip.refresh(a)}}(P,e)).on("mouseout",function(a){return function(){a.tooltip.hide()}}(e)).css(o).add(b.group||e.trackerGroup))});if(c)for(h=c+1;h--;)b[h]===n&&b.splice(h+1,0,b[h+1]-g,b[h+2],D),(h&&b[h]===n||h===c)&&b.splice(h,0,D,b[h-2]+g,b[h-1]);for(h=
0;h<j.length;h++)c=j[h],b.push(n,c.plotX-g,c.plotY,D,c.plotX+g,c.plotY);f?f.attr({d:b}):a.tracker=e.renderer.path(b).attr({isTracker:!0,stroke:r,fill:ca,"stroke-width":d.lineWidth+2*g,visibility:a.visible?q:S,zIndex:1}).on($a?"touchstart":"mouseover",function(){if(e.hoverSeries!==a)a.onMouseOver()}).on("mouseout",function(){if(!d.stickyTracking)a.onMouseOut()}).css(o).add(e.trackerGroup)}};da=K(ob);Z.line=da;da=K(ob,{type:"area"});Z.area=da;da=K(ob,{type:"spline",getPointSpline:function(a,d,b){var c=
d.plotX,e=d.plotY,g=a[b-1],f=a[b+1],j,o,h,z;if(b&&b<a.length-1){a=g.plotY;h=f.plotX;var f=f.plotY,m;j=(1.5*c+g.plotX)/2.5;o=(1.5*e+a)/2.5;h=(1.5*c+h)/2.5;z=(1.5*e+f)/2.5;m=(z-o)*(h-c)/(h-j)+e-z;o+=m;z+=m;o>a&&o>e?(o=na(a,e),z=2*e-o):o<a&&o<e&&(o=nb(a,e),z=2*e-o);z>f&&z>e?(z=na(f,e),o=2*e-z):z<f&&z<e&&(z=nb(f,e),o=2*e-z);d.rightContX=h;d.rightContY=z}b?(d=["C",g.rightContX||g.plotX,g.rightContY||g.plotY,j||c,o||e,c,e],g.rightContX=g.rightContY=null):d=[n,c,e];return d}});Z.spline=da;da=K(da,{type:"areaspline"});
Z.areaspline=da;var Xb=K(ob,{type:"column",pointAttrToOptions:{stroke:"borderColor","stroke-width":"borderWidth",fill:"color",r:"borderRadius",dashstyle:"dashStyle"},init:function(){ob.prototype.init.apply(this,arguments);var a=this,d=a.chart;d.hasColumn=!0;d.hasRendered&&B(d.series,function(d){if(d.type===a.type)d.isDirty=!0})},translate:function(){var a=this,d=a.chart,b=a.options,c=b.stacking,g=b.borderWidth,f=0,j=a.xAxis.reversed,o=a.xAxis.categories,n={},z,m;ob.prototype.translate.apply(a);B(d.series,
function(d){if(d.type===a.type)d.options.stacking?(z=d.stackKey,n[z]===e&&(n[z]=f++),m=n[z]):m=f++,d.columnIndex=m});var r=a.data,q=a.closestPoints,d=va(r[1]?r[q].plotX-r[q-1].plotX:d.plotSizeX/(o&&o.length||1)),o=d*b.groupPadding,q=(d-2*o)/f,l=b.pointWidth,P=t(l)?(q-l)/2:q*b.pointPadding,v=na(h(l,q-2*P),1),l=va(M(b.maxColWidth,50))||1;v>l&&b.groupPadding===0.1&&(o+=(v-l)*f/2,q=v=l);var p=P+(o+((j?f-a.columnIndex:a.columnIndex)||0)*q-d/2)*(j?-1:1),j=M(b.threshold,na(a.yAxis.options.min,0),0),S=a.yAxis.getThreshold(j),
D=h(b.minPointLength,5);B(r,function(d){var e=d.plotY,f=M(d.yBottom,S),j=d.plotX+p,o=Fa(nb(e,f)),x=Fa(na(e,f)-o),n=a.yAxis.stacks[(d.y<0?"-":"")+a.stackKey],h;c&&n&&n[d.x]&&n[d.x].setOffset(p,v);va(x)<D&&(D&&(x=D,o=va(o-S)>D?f-D:S-(e<=S?D:0)),h=o-3);y(d,{barX:j,barY:o,barW:v,barH:x});d.shapeType="rect";e={x:F(j),y:F(o),width:F(v),height:F(x),r:b.borderRadius};g%2&&(e.y-=1,e.height+=1);d.shapeArgs=e;d.trackerArgs=t(h)&&ka(d.shapeArgs,{height:na(6,x+3),y:h})})},getSymbol:function(){},drawGraph:function(){},
drawPoints:function(){var a=this,d=a.options,b=a.chart.renderer,c,g,f=a.shadowGroup,j,o,h,z,m,r,q=a.yAxis,l=q.reversed&&q.options.min<0?!0:!1,P=b.box.nodeName.toLowerCase()==="div"?!0:!1,v=a.inverted;B(a.data,function(q){var p=q.plotY;if(p!==e&&!isNaN(p)&&q.y!==null&&(c=q.graphic,g=q.shapeArgs,c?(ea(c),c.animate(g)):(h=g.y,z=g.width,m=g.height,j=g.r,o=g.x,r=q.pointAttr[q.selected?"select":xa]["stroke-width"]%2===0?void 0:1,l&&q.color&&q.color.FCcolor&&(q.color.FCcolor.angle+=180),P&&v&&q.color&&q.color.FCcolor&&
(q.color.FCcolor.angle+=90),q.graphic=b[q.shapeType](o,h,z,m,j,r).attr(q.pointAttr[q.selected?"select":xa]).add(a.group).shadow(d.shadow,f,q.shadow)),q.errorValue)){var t=q.errorGraph,S,B,w=o+z/2,u=[n,w,p,D],ca=z*d.errorBarWidthPercent/100/2;S=a.yAxis.translate(q.errorValue,0,0,0,0,1);B=p-S;S=p+S;u.push(w,B,n,w+ca,B,D,w-ca,B);d.halfErrorBar===0&&u.push(n,w,p,D,w,S,n,w-ca,S,D,w+ca,S);q.errorPath=u;t?(ea(t),c.animate({d:u})):q.errorGraph=b.path(u).attr({"stroke-width":d.errorBarThickness,stroke:d.errorBarColor}).add(a.group)}})},
drawTracker:function(){var a=this,d=a.chart,c=d.renderer,e,g,f=+new Date,j;d.options.chart.hasScroll&&d.trackerGroup.clip(a.clipRect);B(a.data,function(o){g=o.tracker;var n=o.errorTracker,h=o.errorValue;e=o.trackerArgs||o.shapeArgs;delete e.strokeWidth;if(o.y!==null){if(g)g.attr(e);else{if(o.link!==void 0)var z={cursor:"pointer",_cursor:"hand"};o.tracker=c[o.shapeType](e).attr({isTracker:f,fill:r,visibility:a.visible?q:S,zIndex:1}).on($a?"touchstart":"mouseover",function(c){j=c.relatedTarget||c.fromElement;
if(d.hoverSeries!==a&&b(j,"isTracker")!==f)a.onMouseOver();o.onMouseOver()}).on("mouseout",function(d){if(!a.options.stickyTracking&&(j=d.relatedTarget||d.toElement,b(j,"isTracker")!==f))a.onMouseOut()}).css(z).add(o.group||d.trackerGroup)}if(h)h=o.errorPath,n?n.attr({d:h}):(n={series:a,chart:d,id:o.id,label:o.label,options:o.options,svgElm:o.svgElm,toolText:o.errorValue+"",getLabelConfig:o.getLabelConfig},o.errorTracker=c.path(h).attr({"stroke-width":na(a.options.errorBarThickness,10),stroke:r,isTracker:f,
fill:r,visibility:a.visible?q:S,zIndex:1}).on($a?"touchstart":"mouseover",function(a,d){return function(b){var c=d.plotLeft,k=d.plotTop;a.tooltipPos=[J(b.layerX,b.x)-c+20,J(b.layerY,b.y)-k-15];d.tooltip.refresh(a)}}(n,d)).on("mousemove",function(a,d){return function(b){var c=d.plotLeft,k=d.plotTop;a.tooltipPos=[J(b.layerX,b.x)-c+20,J(b.layerY,b.y)-k-15];d.tooltip.refresh(a)}}(n,d)).on("mouseout",function(a){return function(){a.tooltip.hide()}}(d)).css(z).add(o.group||d.trackerGroup))}})},animate:function(a){var d=
this,b=d.data;if(!a)B(b,function(a){var b=a.graphic,a=a.shapeArgs;b&&(b.attr({height:0,y:d.yAxis.getThreshold(d.options.threshold||0)}),b.animate({height:a.height,y:a.y},d.options.animation))}),d.animate=null},remove:function(){var a=this,d=a.chart;d.hasRendered&&B(d.series,function(d){if(d.type===a.type)d.isDirty=!0});ob.prototype.remove.apply(a,arguments)}});Z.column=Xb;da=K(Xb,{type:"bar",init:function(a){a.inverted=this.inverted=!0;Xb.prototype.init.apply(this,arguments)}});Z.bar=da;da=K(ob,{type:"scatter",
translate:function(){var a=this;ob.prototype.translate.apply(a);B(a.data,function(d){d.shapeType="circle";d.shapeArgs={x:d.plotX,y:d.plotY,r:a.chart.options.tooltip.snap}})},drawTracker:function(){var a=this,d=a.chart,b;B(a.data,function(c){var e,g,f=d.renderer,j=c.hErrorValue,o=c.vErrorValue;if(j)e=c.hErrorPath,(g=c.hErrorTracker)?g.attr({d:e}):(g={series:a,chart:d,id:c.id,label:c.label,options:c.options,svgElm:c.svgElm,toolText:j+"",getLabelConfig:c.getLabelConfig},c.hErrorTracker=f.path(e).attr({"stroke-width":a.options.verticalErrorBarThickness,
stroke:r,isTracker:void 0,fill:r,visibility:a.visible?q:S,zIndex:1}).on($a?"touchstart":"mouseover",function(a,d){return function(b){var c=d.plotLeft,k=d.plotTop;a.tooltipPos=[J(b.layerX,b.x)-c+20,J(b.layerY,b.y)-k-15];d.tooltip.refresh(a)}}(g,d)).on("mousemove",function(a,d){return function(b){var c=d.plotLeft,k=d.plotTop;a.tooltipPos=[J(b.layerX,b.x)-c+20,J(b.layerY,b.y)-k-15];d.tooltip.refresh(a)}}(g,d)).on("mouseout",function(a){return function(){a.tooltip.hide()}}(d)).css(n).add(c.group||d.trackerGroup));
if(o)e=c.vErrorPath,(g=c.vErrorTracker)?g.attr({d:e}):(g={series:a,chart:d,id:c.id,label:c.label,options:c.options,svgElm:c.svgElm,toolText:o+"",getLabelConfig:c.getLabelConfig},c.vErrorTracker=f.path(e).attr({"stroke-width":a.options.horizontalErrorBarThickness,stroke:r,isTracker:void 0,fill:r,visibility:a.visible?q:S,zIndex:1}).on($a?"touchstart":"mouseover",function(a,d){return function(b){var c=d.plotLeft,k=d.plotTop;a.tooltipPos=[J(b.layerX,b.x)-c+20,J(b.layerY,b.y)-k-15];d.tooltip.refresh(a)}}(g,
d)).on("mousemove",function(a,d){return function(b){var c=d.plotLeft,k=d.plotTop;a.tooltipPos=[J(b.layerX,b.x)-c+20,J(b.layerY,b.y)-k-15];d.tooltip.refresh(a)}}(g,d)).on("mouseout",function(a){return function(){a.tooltip.hide()}}(d)).css(n).add(c.group||d.trackerGroup));if(c.link!==void 0)var n={cursor:"pointer",_cursor:"hand"};(b=c.graphic)&&b.attr({isTracker:!0}).on("mouseover",function(){a.onMouseOver();c.onMouseOver()}).on("mouseout",function(){if(!a.options.stickyTracking)a.onMouseOut()}).css(n)})},
cleanData:function(){}});Z.scatter=da;var da=K(Ub,{init:function(){Ub.prototype.init.apply(this,arguments);var a=this,d;y(a,{visible:a.visible!==!1,name:h(a.name,"Slice")});!a.link&&!a.doNotSlice&&ha(a,"click",function(){a.series&&a.series.rotationStartInstanceAngle?delete a.series.rotationStartInstanceAngle:a.slice()});return a},setVisible:function(a){var d=this.series.chart,b=this.tracker,c=this.dataLabel,g=this.connector,f=this.shadowGroup,j;j=(this.visible=a=a===e?!this.visible:a)?"show":"hide";
this.group[j]();if(b)b[j]();if(c)c[j]();if(g)g[j]();if(f)f[j]();this.legendItem&&d.legend.colorizeItem(this,a)},slice:function(a,d,b){var c=this,e=c.series.chart,g=c.slicedTranslation;sa(b,e);d=h(d,!0);a=c.sliced=t(a)?a:!c.sliced;d={translateX:a?g[0]:e.plotLeft,translateY:a?g[1]:e.plotTop};c.group.animate(d);c.shadowGroup&&c.shadowGroup.animate(d);d=g[0]-e.plotLeft;e=g[1]-e.plotTop;c.connectorPath&&(c.connectorPath[1]+=a?d:-d,c.connectorPath[2]+=a?e:-e,c.connectorPath[4]+=a?d:-d,c.connectorPath[6]+=
a?d:-d,c.connector.animate({d:c.connectorPath}));c.dataLabel&&(c.dataLabel.animate({x:a?c.dataLabel._x+d:c.dataLabel._x}),c.dataLabelBG&&c.dataLabelBG.animate({x:a?c.dataLabelBG.x+d:c.dataLabelBG.x},void 0,function(){a||c.dataLabel.textBound()}))}}),cc=function(a,d){return dc(a[1]-this.pageY+d.top,a[0]-this.pageX+d.left)},da=K(ob,{type:"pie",isCartesian:!1,pointClass:da,pointAttrToOptions:{stroke:"borderColor","stroke-width":"borderWidth",fill:"color"},getColor:function(){this.initialColor=this.chart.counters.color},
animate:function(){var a=this;B(a.data,function(d){var b=d.graphic,d=d.shapeArgs,c=2*pb;b&&(b.attr({start:c,end:c}),b.animate({r:d.r,start:d.start,end:d.end},a.options.animation))});a.animate=null},rotate:function(a){var d=this.data,b=this.options.slicedOffset,c=this.chart.plotLeft,e=this.chart.plotTop,g=M(this.chart.options.chart.startingAngle,0),f;f=(a-g)%360;this.chart.options.chart.startingAngle=M(a,this.chart.options.chart.startingAngle)%360;f=f*pb/180;B(d,function(a){var d=a.graphic,g=a.shadowGroup,
j=a.tracker,o=a.shapeArgs,n={start:o.start+=f,end:o.end+=f},h=a.centerAngle=(n.start+n.end)/2%Kb,z=a.sliced,m=o.r*0.7,q;a.slicedTranslation=gc([Ja(h)*b+c,Ma(h)*b+e],F);q={translateX:a.slicedTranslation[0],translateY:a.slicedTranslation[1]};a.tooltipPos=[o.x+Ja(h)*m,o.y+Ma(h)*m];j&&j.attr(n);d&&(d.attr(n),z&&a.group.attr(q));g&&z&&a.shadowGroup.attr(q)});this.drawDataLabels(!0)},translate:function(){var a=0,d=this,b=d.options,c=-1*(J(this.chart.options.chart.startingAngle,0)%360/360),e=M(b.dataLabels.distance,
20),g=b.slicedOffset,f=g+b.borderWidth,j=b.center,o=d.chart,n=o.plotWidth,h=o.plotHeight,z,m,q,r=d.data,l=2*pb,P,v=nb(n,h),t,S,D,w=b.dataLabels.distance;j.push(b.size,b.innerSize||0);j=gc(j,function(a,d){return(t=/%$/.test(a))?[n,h,v,v][d]*p(a)/100:a});d.getX=function(a,d){q=O.asin((a-j[1])/(j[2]/2+w));return j[0]+(d?-1:1)*Ja(q)*(j[2]/2+w)};d.center=j;B(r,function(d){a+=d.y});d.labelsRadius=j[2]/2+e;d.quadrantHeight=h/2;d.quadrantWidth=n/2;e=b.dataLabels;b=M(parseInt(e.style.fontSize,10),10)+4;d.maxLabels=
Math.floor(d.quadrantHeight/b);d.labelFontSize=b;d.connectorPadding=M(e.connectorPadding,5);d.isSmartLineSlanted=J(e.isSmartLineSlanted,!0);d.connectorWidth=M(e.connectorWidth,1);d.enableSmartLabels=e.enableSmartLabels;B(r,function(b){P=a?b.y/a:0;z=F(c*l*1E3)/1E3;c+=P;m=F(c*l*1E3)/1E3;b.shapeType="arc";b.shapeArgs={x:j[0],y:j[1],r:j[2]/2,innerR:j[3]/2,start:z,end:m};if(b.color.FCcolor)b.color.FCcolor.cx=j[0],b.color.FCcolor.cy=j[1],b.color.FCcolor.r=j[2]/2;b.centerAngle=q=(m+z)/2%Kb;b.slicedTranslation=
gc([Ja(q)*g+o.plotLeft,Ma(q)*g+o.plotTop],F);S=Ja(q)*j[2]/2;d.radiusY=D=Ma(q)*j[2]/2;b.tooltipPos=[j[0]+S*0.7,j[1]+D*0.7];b.labelPos=[j[0]+S+Ja(q)*w,j[1]+D+Ma(q)*w,j[0]+S+Ja(q)*f,j[1]+D+Ma(q)*f,j[0]+S,j[1]+D,w<0?"center":q<l/4?"left":"right",q];b.percentage=P*100;b.total=a});this.setTooltipPoints()},render:function(){this.drawPoints();this.options.enableMouseTracking!==!1&&this.drawTracker();this.drawDataLabels();this.options.animation&&this.animate&&this.animate();this.isDirty=!1},drawPoints:function(){var a=
this.chart,d=a.renderer,b,c,e,g=this.options.shadow,j,f;B(this.data,function(o){c=o.graphic;f=o.shapeArgs;e=o.group;j=o.shadowGroup;if(g&&!j)j=o.shadowGroup=d.g("shadow").attr({zIndex:4}).add();if(!e)e=o.group=d.g("point").attr({zIndex:5}).add();b=o.sliced?o.slicedTranslation:[a.plotLeft,a.plotTop];e.translate(b[0],b[1]);j&&j.translate(b[0],b[1]);c?c.animate(f):o.graphic=d.arc(f).attr(y(o.pointAttr[xa],{"stroke-linejoin":"round"})).add(o.group).shadow(g,j,o.shadow);o.visible===!1&&o.setVisible(!1)})},
drawDataLabels:function(){var a=function(a,b){return a.point.y-b.point.y},b=function(a,b){return a.angle-b.angle},c=["left","left","right","right"],e=[-1,1,1,-1],g=[1,1,-1,-1];return function(j){var f=this.data,o,h,z=this.chart,m=this.options.dataLabels;o=m.placeInside;var r=this.connectorPadding,l=this.connectorWidth,P,v,p=m.distance>0,t,w=this.center[1],u=this.center[0],ca=this.center[2]/2,y=[[],[],[],[]],Y,xa=z.plotLeft,C=z.plotTop,G,E,A,Da,N,K,z=this.labelsRadius,Na,T=this.labelFontSize,Ka=T/
2,r=[r,r,-r,-r];t=this.maxLabels;var Z=this.isSmartLineSlanted,ua=this.enableSmartLabels,J;j||ob.prototype.drawDataLabels.apply(this);if(f.length==1){if(o=f[0],J=o.dataLabel,o.slicedTranslation=[xa,C],J)J.attr({visibility:q,align:"center",x:u,y:w+Ka-2}),J.x=u}else if(o){var m=M(this.center[3],0)/2,ba=m+(ca-m)/2;B(f,function(a){if(J=a.dataLabel){var b=a.centerAngle;K=w+ba*Ma(b)+Ka-2;A=u+ba*Ja(b);J.x=A;J._x=A;J.y=K;if(a.sliced){var b=a.slicedTranslation,d=b[1]-C;A+=b[0]-xa;K+=d}J.attr({visibility:q,
align:"center",x:A,y:K});a.dataLabelBG=J.textBound()}})}else{B(f,function(a){if(J=a.dataLabel){var b=a.centerAngle;b<0&&(b=Kb+b);y[b<ec?1:b<pb?2:b<Yb?3:0].push({point:a,angle:b})}});for(j=f=4;j--;){o=y[j].length-t;if(o>0){y[j].sort(a);Na=y[j].splice(0,o);G=0;for(E=Na.length;G<E;G+=1)o=Na[G].point,o.dataLabel.attr({visibility:S}),o.connector&&o.connector.attr({visibility:S})}y[j].sort(b)}j=na(y[0].length,y[1].length,y[2].length,y[3].length);Na=na(j*T,z);y[1].reverse();for(y[3].reverse();f--;){G=y[f];
E=G.length;o=E*T-Ka;t=Na+Ka;for(j=0;j<E;j+=1,o-=T)v=va(Na*Ma(G[j].angle)),t-v<T?v=t-T:v<o&&(v=o),t=G[j].oriY=v;Y=c[f];E=Na-E*T+Ka;t=-Ka;for(j=G.length-1;j>=0;j-=1,E+=T){o=G[j].point;h=G[j].angle;P=o.sliced;J=o.dataLabel;v=va(Na*Ma(h));v-t<T?v=t+T:v>E&&(v=E);t=v;Da=(v+G[j].oriY)/2;v=u+g[f]*z*Ja(O.asin(Da/Na));Da*=e[f];Da+=w;N=w+ca*Ma(h);h=u+ca*Ja(h);A=v+r[f];K=Da+Ka-2;J.x=A;J._x=A;J.y=K;if(P){var $=o.slicedTranslation;P=$[0]-xa;$=$[1]-C;A+=P;v+=P;h+=P;N+=$}J.attr({visibility:q,align:Y,x:A,y:K});o.dataLabelBG=
J.textBound();if(p&&l&&ua)P=o.connector,o.connectorPath=v=[n,h,N,D,Z?v:h,Da,A,Da],P?(P.attr({d:v}),P.attr("visibility",q)):o.connector=P=this.chart.renderer.path(v).attr({"stroke-width":l,stroke:m.connectorColor||"#606060",visibility:q,zIndex:3}).translate(xa,C).add()}}}}}(),drawTracker:function(){var a=this,b,c=[a.center[0]+a.chart.plotLeft,a.center[1]+a.chart.plotTop],e,g=a.options.enableRotation,j;g&&(j=function(b){e=E(a.chart.container);a.rotationStartInstanceAngle=cc.call(b,c,e)-a.chart.options.chart.startingAngle*
zb;a.chart.tooltip&&(b.type==="dragstart"?a.chart.tooltip.block(!0):(a.chart.tooltip.block(!1),a.chart.tooltip.refresh(b.data,!0)))});Xb.prototype.drawTracker.apply(this,arguments);B(a.data,function(f){if(f.y===null||!(b=f.tracker)||b.canRotate===!0)return!0;g&&(ha(b.element,"dragstart dragend",j,f),ha(b.element,"drag",function(b){a.rotate((cc.call(b,c,e)-a.rotationStartInstanceAngle)/zb)}))})},getSymbol:function(){}});Z.pie=da;var Za=C.Highcharts={Chart:Gb,dateFormat:c,pathAnim:o,getOptions:function(){return Va},
numberFormat:ia,Point:Ub,Color:Db,Renderer:$b,seriesTypes:Z,setOptions:function(a){Va=ka(Va,a);$=Va.plotOptions;oa();return Va},Series:ob,addEvent:ha,createElement:Q,discardElement:Oa,css:R,each:B,extend:y,map:gc,merge:ka,pick:h,extendClass:K,product:"Highcharts",version:"2.1.6",pvt:{},attr:b,FusionChartsModified:"3.2.2"};$.floatedcolumn=ka($.column,{states:{hover:{}}});C=Za.extendClass(Z.column,{type:"floatedcolumn",translate:function(){var a=this,b=a.chart,c=0,g=a.xAxis.reversed,j=a.xAxis.categories,
f={},o,n,z,m;ob.prototype.translate.apply(a);B(b.series,function(b){if(b.type==a.type)b.options.stacking?(o=b.stackKey,f[o]===e&&(f[o]=c++),m=f[o]):m=c++,b.columnIndex=m});var q=a.options,r=a.data,l=a.closestPoints,j=va(r[1]?r[l].plotX-r[l-1].plotX:b.plotSizeX/(j?j.length:1)),l=j*q.groupPadding,P=(j-2*l)/c,v=q.pointWidth,p=t(v)?(P-v)/2:P*q.pointPadding,S=na(h(v,P-2*p),1),D=p+(l+((g?c-a.columnIndex:a.columnIndex)||0)*P-j/2)*(g?-1:1),g=q.threshold||0,w=a.xAxis,j=a.yAxis,u=w.getExtremes(),ca=j.getExtremes(),
Y=a.yAxis.getThreshold(g),xa=h(q.minPointLength,5);B(r,function(c){var k=c.plotY,e=c.yBottom||Y,g=c.plotX+D,j=Fa(nb(k,e)),f=Fa(na(k,e)-j),o,s;c._FCX!==void 0&&(g=w.translate(c._FCX,0,0,0,1));c._FCY!==void 0&&(j=a.yAxis.translate(c._FCY,0,1,0,1));c._FCH!==void 0&&(f=F(c._FCH*Math.abs(b.plotSizeY/(ca.max-ca.min))*100)/100);s=c._FCW!==void 0?F(c._FCW*Math.abs(b.plotSizeX/(u.max-u.min))*100)/100:S;va(f)<xa&&(xa&&(f=xa,j=va(j-Y)>xa?e-xa:Y-(k<=Y?xa:0)),o=j-3);y(c,{barX:g,barY:j,barW:s,barH:f});c.shapeType=
"rect";c.shapeArgs={x:g,y:j,width:s,height:f,r:q.borderRadius};n=g+s/2+15;z=j-15;c.tooltipPos=[n<0?0:n,z<0?0:z];c.trackerArgs=t(o)&&ka(c.shapeArgs,{height:na(6,f+3),y:o})})}});Z.floatedcolumn=C;$.ssgrid=ka($.pie,{states:{hover:{}}});var Vb=function(a,b){var c=a.currentSeriesIndex,e=a.series,g=b?c-1:c+1;if(e[g])e[c].hide(),e[g].show(),a.currentSeriesIndex=g,tc(a)},tc=function(a){var b=a.series.length,c=a.currentSeriesIndex,e=a.naviigator;e.translate(0,a.series[c].data.length*a.options.chart.rowHeight);
c===0?e.navElePrv.hide():e.navElePrv.show();c===b-1?e.navEleNxt.hide():e.navEleNxt.show()},qc=function(a){var b=a.renderer,c=a.options.chart,e=c.navButtonRadius,g=e*0.67,j=c.navButtonPadding+g,f=c.width-20,o=c.navButtonHoverColor,h=c.navButtonColor,z,m;if(a.series.length>1){var q=a.naviigator=b.g("navigator").attr({zIndex:4}).add();q.navElePrv=c=b.g("navElePrv").add(q);z=b.path([n,20,j,D,20+e+g,j-g,20+e,j,20+e+g,j+g,"Z"]).attr({fill:h}).add(c);b.circle(20+e,j,e).attr({fill:Qa,cursor:"pointer"}).on("mouseover",
function(){z.attr({fill:o,cursor:"pointer"})}).on("mouseout",function(){z.attr({fill:h})}).on("click",function(){Vb(a,!0)}).add(c);q.navEleNxt=c=b.g("navEleNxt").add(q);m=b.path([n,f,j,D,f-e-g,j-g,f-e,j,f-e-g,j+g,"Z"]).attr({fill:h,cursor:"pointer"}).add(c);b.circle(f-e,j,e).attr({fill:Qa,cursor:"pointer"}).on("mouseover",function(){m.attr({fill:o})}).on("mouseout",function(){m.attr({fill:h})}).on("click",function(){Vb(a)}).attr({fill:h}).add(c)}},C=Za.extendClass(Z.pie,{type:"ssgrid",translate:function(){var a=
this.chart.options.chart,b=0,c=a.width,e=a.rowHeight,g=a.colorBoxWidth,j=a.colorBoxHeight,f=a.colorBoxX,o=a.labelX,h=a.valueX;B(this.data,function(a){a.shapeType="rect";a.alternateGradientBox={x:0,y:b,width:c,height:e,r:0};a.rowDivider=[n,0,b,D,c,b];a.colorBoxArgs={x:f,y:b+e/2-j/2,width:g,height:j,r:0};a.labelX=o;a.valueX=h;b+=e});this.options.lastRowDivider=[n,0,b,D,c,b]},drawPoints:function(){var a=this.chart.options.chart,b=this.chart.renderer,c,e=this.group,g=a.alternateRowColor,j=a.listRowDividerAttr,
f=this.options.lastRowDivider,o=a.rowHeight,h=a.textStyle,n=a.lineHeight,z=a.width,m=a.valueColumnPadding,q,r,l,P,v,p,t;B(this.data,function(a,f){c=a.graphic;q=a.rowDivider;r=a.colorBoxArgs;l=a.alternateGradientBox;p=a.valueX;if(!c)a.graphic=b.path(q).attr(j).add(e),f%2==0&&b.rect(l).attr({fill:g,"stroke-width":0}).add(e),a.symbol=b.rect(r).attr({fill:a.color,"stroke-width":0,stroke:"#000000"}).add(e),P=b.text(a.label,a.labelX).css(h).add(e),v=q[2]+o/2+n-P.getBBox().height/2,P.attr({y:v}),P=b.text(a.displayValue,
p).css(h).add(e),t=P.getBBox(),v=q[2]+o/2+n-t.height/2,P.attr({y:v,x:p+(z-p-m-t.width)})});b.path(f).attr(j).add(e)},render:function(){var d;var a,b=this.chart;a=b.renderer;var c=this.options;b.naviigator||qc(b);if(!this.group)d=this.group=a.g("series"),a=d,a.attr({visibility:this.visible?q:S,zIndex:c.zIndex}).translate(b.plotLeft,b.plotTop).add(b.seriesGroup);this.drawPoints();this.drawDataLabels();if(this.visible)b.currentSeriesIndex=this.index,b.naviigator&&tc(b);this.options.animation&&this.animate&&
this.animate();this.isDirty=!1},drawDataLabels:function(){},drawTracker:function(){},animate:function(){}});Z.ssgrid=C;var nc=function(){var a=this.radarAxis,b=a.catLength,c=this.renderer,e,g=this.plotWidth/2,j=this.plotHeight/2,f=a.radius,o=a.xAxis;e=a.yAxis;var h=e.plotLines,z=o.plotLines,m=e.min,q=a.xTrans,r=a.startAngle,l=[],P=[n],v=[],p=h.length,t=a.yTrans,S,w,B,u,ca,y,Y=O.PI*2,xa=O.PI/2,G=O.PI+xa,C=["right","center","left"],E=f+10,A=e.labels,Da,K=c.g("axis");K.attr({zIndex:2,width:this.plotWidth,
height:this.plotHeight}).translate(this.plotLeft,this.plotTop).add();a.divline=[];for(w=0;w<p;w+=1){v[w]=[n];S=!0;e=b;Da=h[w];for(y=Da.value;e--;){ca=va(y-m)*t;B=g+ca*Ja(-(r+e*q));u=j+ca*Ma(-(r+e*q));v[w].splice(v[w].length,0,B,u);if(e==0&&Da.label)A=Da.label,((ca=A.text)||ca===0)&&c.text(ca,B,u).attr({align:A.textAlign,rotation:A.rotation}).css(A.style).add(K);S&&(v[w].push(D),S=!1)}v[w].push("Z");a.divline[w]=c.path(v[w]).attr({zIndex:2,stroke:Da.color,"stroke-width":Da.width}).add(K)}S=!0;for(e=
z.length;e--;)if(Da=z[e],y=Da.value,b=r+y*q,h=b%Y,B=g+f*Ja(-b),u=j+f*Ma(-b),l.splice(l.length,0,n,g,j,D,B,u),P.splice(P.length,0,B,u),S&&(P.push(D),S=!1),Da.label&&(A=Da.label,(ca=A.text)||ca===0))B=h>xa&&h<G?0:h==xa||h==G?1:2,c.text(ca,g+E*Ja(-b),j+E*Ma(-b)).attr({align:C[B],rotation:A.rotation}).css(A.style).add(K);P.push("Z");a.spikeGraph=c.path(l).attr({zIndex:1,stroke:o.gridLineColor,"stroke-width":J(o.gridLineWidth,1)}).add(K);if(o.showRadarBorder)a.borderGraph=c.path(P).attr({stroke:o.radarBorderColor,
"stroke-width":J(o.radarBorderThickness,2),fill:o.radarFillColor}).add(K)};$.radar=ka($.area,{states:{hover:{}}});C=Za.extendClass(Z.pie,{type:"radar",isCartesian:!1,pointClass:Z.area.prototype.pointClass,pointAttrToOptions:Z.area.prototype.pointAttrToOptions,translate:function(){var a=this.chart,b=this.data,c=b.length,e,g,j,f,o,h,n;if(typeof a.radarAxis==="undefined"){h=a.plotWidth/2;n=a.plotHeight/2;e=a.options;var z=e.xAxis,m=z.max-z.min+1,q=e.yAxis instanceof Array?e.yAxis[0]:e.yAxis,r=t(e.chart.axisRadius)?
e.chart.axisRadius:nb(h,n);r<0&&(r=nb(h,n));g=q.min;j=va(q.max-g);f=r/j;e=2*O.PI/m;o=O.PI/2;a.radarAxis={};a.radarAxis.yTrans=f;a.radarAxis.xTrans=e;a.radarAxis.yRange=j;a.radarAxis.startAngle=o;a.radarAxis.yMin=g;a.radarAxis.centerX=h;a.radarAxis.centerY=n;a.radarAxis.radius=r;a.radarAxis.categories=[];a.radarAxis.catLength=m;a.radarAxis.yAxis=q;a.radarAxis.xAxis=z}else h=a.radarAxis.centerX,f=a.radarAxis.yTrans,g=a.radarAxis.yMin,o=a.radarAxis.startAngle,e=a.radarAxis.xTrans,n=a.radarAxis.centerY;
for(;c--;)a=b[c],z=t(a.y)?a.y:g,a.plotX=h+f*va(z-g)*Ja(-(o+c*e)),a.plotY=n+f*va(z-g)*Ma(-(o+c*e)),a.clientX=a.plotX},drawGraph:function(){var a=this.options,b=this.chart,c=this.graph,e=[],g=this.group,j=this.color,f=a.lineWidth,o=a.lineColor||j,a=h(a.fillColor,Db(j).setOpacity(a.fillOpacity||0.5).get()),z,b=b.renderer,j=[];this.data.length>1?(z=[],B(this.data,function(a,b){b<2&&z.push([n,D][b]);z.push(a.plotX,a.plotY)}),z.push("Z"),e=e.concat(z)):j.push(this.segment[0][0]);this.graphPath=e;this.singlePoints=
j;c?c.attr({d:e}):this.graph=b.path(e).attr({stroke:o,fill:a,"stroke-width":f+v}).add(g).shadow()},drawTracker:function(){var a=this,b=a.options.cursor,c=b&&{cursor:b},e;B(a.data,function(b){(e=b.graphic)&&e.attr({isTracker:!0}).on("mouseover",function(){a.onMouseOver();b.onMouseOver()}).on("mouseout",function(){if(!a.options.stickyTracking)a.onMouseOut()}).css(c)})},setVisible:function(a,b){var c=this.chart,g=this.legendItem,j=this.group,f=this.dataLabelsGroup,o=this.shadowGroup,h,n=this.data,z=
c.options.chart.ignoreHiddenSeries;h=this.visible;h=(this.visible=a=a===e?!h:a)?"show":"hide";if(j)j[h]();if(o&&o.floated)o[h]();for(j=n.length;j--;)if(o=n[j],o.graphic)o.graphic[h]();if(f)f[h]();g&&c.legend.colorizeItem(this,a);this.isDirty=!0;this.options.stacking&&B(c.series,function(a){if(a.options.stacking&&a.visible)a.isDirty=!0});if(z)c.isDirtyBox=!0;b!==!1&&c.redraw();Ya(this,h)},getColor:Z.area.prototype.getColor,drawDataLabels:Z.area.prototype.drawDataLabels,animate:function(){},getSymbol:Z.area.prototype.getSymbol,
drawPoints:function(){var a,b=this.data,c=this.chart,g,j,f,o,n,z;if(this.options.marker.enabled)for(f=b.length;f--;)if(o=b[f],g=o.plotX,j=o.plotY,z=o.graphic,j!==e&&!isNaN(j))a=o.pointAttr[o.selected?"select":xa],n=a.r,z?z.animate({x:g,y:j,r:n}):o.graphic=c.renderer.symbol(h(o.marker&&o.marker.symbol,this.symbol),g,j,n).attr(a).add(c.trackerGroup||this.group)},rotate:function(){},render:function(){var a=this,b=a.chart,c,e=a.options.animation,g=e&&a.animate,e=g?e&&e.duration||500:0,j,f,o,h,n=b.renderer,
z=a.clipRect;if(!z&&(j=-b.plotLeft,f=-b.plotTop,o=b.chartWidth,h=b.chartHeight,z=a.clipRect=!b.hasRendered&&b.clipRect?b.clipRect:n.clipRect(j,f,o,h),z.cliprectX=j,z.cliprectY=f,z.cliprectW=o,z.cliprectH=h,!b.clipRect))b.clipRect=a.clipRect;if(b.drawRadarAxis!==!0)nc.call(b),b.drawRadarAxis=!0;if(!a.group)c=a.group=n.g("series"),b.inverted&&c.attr({width:b.plotWidth,height:b.plotHeight}).invert(),c.clip(a.clipRect).attr({visibility:a.visible?q:S,zIndex:3}).translate(b.plotLeft,b.plotTop).add(b.seriesGroup);
a.drawDataLabels();g&&a.animate(!0);a.drawGraph&&a.drawGraph();a.drawPoints();a.options.enableMouseTracking!==!1&&a.drawTracker();g&&a.animate();setTimeout(function(){z.isAnimating=!1;if((c=a.group)&&z!==b.clipRect&&z.renderer)c.clip(a.clipRect=b.clipRect),z.destroy()},e);a.isDirty=!1}});Z.radar=C;$.column3d=ka($.column,{states:{hover:{}}});C=Za.extendClass(Z.column,{type:"column3d",initGroup:function(){var a=this.chart,b=a.renderer,c=this.xDepth,e=this.yDepth,g,j,f,o,h,n=this.yAxis;if(!a.column3DGroups)h=
a.column3DGroups=b.g("series-3d"),h.translate(a.plotLeft-c,a.plotTop+e).add(a.seriesGroup);if(n.options.min<0&&n.options.max>=0){g=a.options.chart.zeroPlaneColor;j=a.options.chart.zeroPlaneBorderColor;f=0;o=n.translate(0,0,1);var z=1,m=a.plotSizeX;a.inverted&&(f=z,z=m,m=f,f=a.plotSizeY-o-m,o=0);n.zeroPlane=b.rect3d(f,o,m,z,c,e,1,"zeroPlane").attr({fill:g,stroke:j,"stroke-width":1,zIndex:2}).add(h)}},drawNthPoint:function(a){var b=this.chart,c=b.column3DGroups,g=b.renderer,j=this.xDepth,f=this.yDepth,
o=this.data[a],h,z;h=o.plotY;var m,q,r,l,P,v,p,t;if(h!==e&&!isNaN(h)){m=o.shapeArgs;q=o.color;t=o.borderColor;l=o.borderWidth;r=o.graphic;h=parseInt(m.x,10);z=parseInt(m.y,10);P=h;v=z;p=parseInt(m.height,10);m=parseInt(m.width,10);if(this.options.enableMouseTracking!==!1){var S=h-j,w=z+f;o.trackerArgs=[n,S,w,D,S,w+p,S+m,w+p,S+m+j,w+p-f,S+m+j,w-f,S+j,w-f,"Z"];this.drawTracker(a)}b.inverted&&(a=p,p=m,m=a,P=b.plotSizeY-z-m,v=b.plotSizeX-h-p);r?g.attr({x:P,y:v,width:m,height:p,x3D:j,y3D:f,strokeWidth:l,
fill:q,stroke:t,zIndex:o.y>=0?3:1}):(o.graphic=g.rect3d(P,v,m,p,j,f,l,"point").attr({fill:q,stroke:t,zIndex:o.y>=0?3:1}).add(c),o.graphic.shadow(this.options.shadow,void 0,o.shadow))}},render:function(){var a=this.options.animation&&this.animate;this.drawDataLabels();a&&this.animate();this.isDirty=!1},animate:function(a){var b=this,c=b.data,e=b.chart,g,j;if(!a)B(c,function(a){var c=a.graphic;c&&(e.inverted?(g=c.attr("width"),j=c.attr("x"),c.attr({x:e.plotSizeY-b.yAxis.getThreshold(b.options.threshold||
0),width:0}),c.animate({x:j,width:g},b.options.animation)):(c.attr({height:0,y:b.yAxis.getThreshold(b.options.threshold||0)}),c.animate({height:a.barH,y:a.barY},b.options.animation)))}),b.animate=null},drawTracker:function(a){var d=this,c=d.chart,e=c.renderer,g,j=+new Date,f=d.options.cursor,f=f&&{cursor:f},o,h=d.data[a];g=h.tracker;c.options.chart.hasScroll&&c.trackerGroup.clip(d.clipRect);a=h.trackerArgs||h.shapeArgs;if(h.y!==null)g?g.attr(a):(h.link!==void 0&&(f={cursor:"pointer"}),h.tracker=e.path(a).attr({isTracker:j,
fill:r,visibility:d.visible?q:S,zIndex:h.y>0?-1:-3}).on($a?"touchstart":"mouseover",function(a){o=a.relatedTarget||a.fromElement;if(c.hoverSeries!=d&&b(o,"isTracker")!=j)d.onMouseOver();h.onMouseOver()}).on("mouseout",function(a){if(!d.options.stickyTracking&&(o=a.relatedTarget||a.toElement,b(o,"isTracker")!=j))d.onMouseOut()}).css(f).add(c.trackerGroup))},setVisible:function(a,b){var c=this.chart,g=this.legendItem,j=this.dataLabelsGroup,f,o,h=this.data,n,z=c.options.chart.ignoreHiddenSeries;f=this.visible;
f=(this.visible=a=a===e?!f:a)?"show":"hide";for(o=h.length;o--;){n=h[o];if(n.tracker)n.tracker[f]();if(n.graphic)n.graphic[f]()}if(j)j[f]();g&&c.legend.colorizeItem(this,a);this.isDirty=!0;this.options.stacking&&B(c.series,function(a){if(a.options.stacking&&a.visible)a.isDirty=!0});if(z)c.isDirtyBox=!0;b!==!1&&c.redraw();Ya(this,f)}});Z.column3d=C;$.bar3d=ka($.bar,{states:{hover:{}}});C=Za.extendClass(C,{type:"bar3d",init:function(a){a.inverted=this.inverted=!0;Z.column.prototype.init.apply(this,
arguments)}});Z.bar3d=C;$.bubble=ka($.scatter,{states:{hover:{}}});C=Za.extendClass(Z.scatter,{type:"bubble",drawPoints:function(){var a,b=this.data,c=this.chart,g,j,f,o,n,z,m=this.chart.plotWidth;g=this.chart.plotHeight;a=this.options.zMax;var q=this.options.bubbleScale,r,m=(m>g?g:m)/8;r=Math.sqrt(a);if(this.options.marker.enabled)for(f=b.length;f--;)if(o=b[f],g=o.plotX,j=o.plotY,z=o.graphic,j!==e&&!isNaN(j))a=o.pointAttr[o.selected?"select":xa],n=Math.sqrt(o.z),n=Math.round(n*m/r)*q,a.r=n,z?z.animate({x:g,
y:j,r:n}):o.graphic=c.renderer.symbol(h(o.marker&&o.marker.symbol,this.symbol),g,j,n).attr(a).add(this.group)}});Z.bubble=C;$.candlestick=ka($.column,{states:{hover:{}}});C=Za.extendClass(Z.column,{type:"candlestick",drawPoints:function(){var a=this,b=a.options,c=a.chart.renderer,e,g,j;B(a.data,function(f){if(t(f.plotY))if(e=f.graphic,g=f.shapeArgs,e)e.attr(g);else{j={stroke:f.borderColor,fill:f.color,"stroke-width":f.borderWidth,"stroke-linecap":"round",dashstyle:f.dashStyle};if(f.bar)f.bar.graphic=
c[f.bar.shapeType](f.bar.shapeArgs).attr(j).add(a.group).shadow(b.shadow,void 0,b.shadow);if(g)f.graphic=c[f.shapeType](g).attr(j).add(a.group).shadow(b.shadow,void 0,b.shadow)}})},translate:function(){var a=this,b=a.chart,c=0,e=a.xAxis.reversed,g=a.xAxis.categories,j,f=a.options.plotType;ob.prototype.translate.apply(a);B(b.series,function(b){if(b.type==a.type)b.options.stacking?(t(j)||(j=c++),b.columnIndex=j):b.columnIndex=c++});for(var o=a.options,z,m=a.data,q=a.closestPoints,g=va(m[1]?m[q].plotX-
m[q-1].plotX:b.plotSizeX/(g&&g.length?g.length:1)),q=g*o.groupPadding,r=(g-2*q)/c,b=o.pointWidth,l=t(b)?(r-b)/2:r*o.pointPadding,b=h(b,r-2*l),q=l+(q+((e?c-a.columnIndex:a.columnIndex)||0)*r-g/2)*(e?-1:1),r=a.yAxis.getThreshold(o.threshold||0),l=o.minPointLength,e=0;e<m.length;e+=1)if(g=m[e],f=="line"){if(e>0)z=m[e-1],z.shapeType="path",z.shapeArgs=[n,z.plotX,z.plotY,D,g.plotX,g.plotY];g.trackerShapeType="rect";g.trackerArgs={x:g.plotX-3,y:g.plotY-3,width:6,height:6}}else{z=g.plotX+q;var P=g.plotY,
v=a.yAxis.getThreshold(g.MY),p=Fa(nb(P,v)),S=Fa(va(P-v)),w=b,u=w/2,ca;if(S<(l||5))l&&(S=l,p=r-(P<=r?l:0)),ca=p-3;S<1&&(S=1);y(g,{barX:z,barY:p,barW:w,barH:S});f=="bar"?(g.trackerShapeType="rect",g.shapeType="path",g.shapeArgs=[n,g.plotX,v,D,g.plotX-u,v,n,g.plotX,P,D,g.plotX+u,P],g.trackerArgs={x:z,y:t(ca)?ca:p,width:w,height:t(ca)?6:S}):(g.shapeType="rect",g.shapeArgs={x:z,y:p,width:w,height:S,r:o.borderRadius},g.trackerShapeType="rect",g.trackerArgs=t(ca)&&ka(g.shapeArgs,{height:6,y:ca}));g.bar=
{shapeType:"path",shapeArgs:[n,g.plotX,a.yAxis.getThreshold(parseFloat(g.high)),D,g.plotX,a.yAxis.getThreshold(parseFloat(g.low))]}}},drawTracker:function(){var a=this,d=a.chart,c=d.renderer,e,g,j=+new Date,f;B(a.data,function(o){g=o.tracker;e=o.trackerArgs;delete e.strokeWidth;if(o.y!==null)if(g)g.attr(e);else{if(o.link!==void 0)var h={cursor:"pointer",_cursor:"hand"};o.tracker=c[o.trackerShapeType](e).attr({isTracker:j,fill:r,visibility:a.visible?q:S,zIndex:1}).on($a?"touchstart":"mouseover",function(c){f=
c.relatedTarget||c.fromElement;if(d.hoverSeries!==a&&b(f,"isTracker")!==j)a.onMouseOver();o.onMouseOver()}).on("mouseout",function(d){if(!a.options.stickyTracking&&(f=d.relatedTarget||d.toElement,b(f,"isTracker")!==j))a.onMouseOut()}).css(h).add(o.group||d.trackerGroup)}})}});Z.candlestick=C;var Pb={};$.dragnode=ka($.scatter,{states:{hover:{}}});C=Za.extendClass(Z.scatter,{type:"dragnode",drawPoints:function(){var a=this,b,c=a.data,g=a.chart,j,f,o,z,m,r,l,P,v,p,t,w,u=g.options.connectors,ca=g.renderer,
y=a.options.dataLabels.style,Y,G,C,E,A,Da,K,N;if(a.options.marker.enabled){for(z=c.length;z--;)if(m=c[z],j=m.options,f=m.plotX,o=m.plotY,l=m.graphic,t=m.marker,p=M(t&&t.height),v=M(t&&t.width),r=M(t&&t.radius),w=J(t&&t.symbol),P=m.id,Y=j.imageNode,G=j.imageURL,C=j.imageAlign,Da=w=="square"?v:r*1.5,E=M(j.imageWidth,Da),K=w=="square"?p:r*1.5,j=M(j.imageHeight,K),N=J(t&&t.lineWidth),o!==e&&!isNaN(o)&&(b=m.pointAttr[m.selected?"select":xa],b.r*=1,r=b.r,l?l.animate({x:f,y:o,r:r}):(w=h(t&&t.symbol,a.symbol),
w=="square"?m.graphic=ca.rect(f-v/2,o-p/2,v,p).attr(b).add(a.group):(w=w==="triangle"?"poly_3":w,w=w==="diamond"?"poly_4":w,m.graphic=ca.symbol(w,f,o,r).attr(b).add(a.group)),Pb[P]=m),Y&&G)){switch(C){case "middle":o-=j/2;break;case "bottom":o=K>j?o-j+Da/2:o-j/2;break;default:o=K>j?o-Da/2:o-j/2}A||(A=ca.g("group").attr({visibility:S}).add());m.imageNodeGraph=ca.image(G).attr({width:E,height:j}).translate(f-E/2,o).css({opacity:1}).add(a.group)}var Na,T,Ka,Z,ua,ba,$,F,da,ea,ha,ka,pa,U,R,la,Qb,L;for(z=
0;z<u.length;z+=1)da||(da=ca.g("connectors").attr({visibility:q}).translate(g.plotLeft,g.plotTop).add()),B(u[z].connector,function(b){Na=b.from;T=b.to;Ka=b.label;N=b.conStrength*b.stdThickness;ea=b.color;Ka=b.label;pa=Pb[Na];U=Pb[T];if(pa&&U){Z=pa.plotY;ua=pa.plotX;$=U.plotY;ba=U.plotX;R=pa.marker;la=U.marker;ha=(ua+ba)/2;ka=(Z+$)/2;F=[n,ua,Z,D,ba,$];a.connector=ca.path(F).attr({"stroke-width":N,stroke:ea}).add(da);Qb=[];if(b.arrowAtStart)R.symbol=="square"?(r=R.width,H=R.height):(r=R.radius,H=void 0),
Qb=Qb.concat(Hb(ua,Z,ba,$,r,H));if(b.arrowAtEnd)la.symbol=="square"?(r=la.width,H=la.height):(r=la.radius,H=void 0),Qb=Qb.concat(Hb(ba,$,ua,Z,r,H));Qb.length&&ca.path(Qb).attr({"stroke-width":N,stroke:ea}).add(da);L=ea&&ea.FCcolor&&ea.FCcolor.color;m.connectorText=ca.text(Ka,ha,ka).attr({align:"center",rotation:0}).css(y).css({backgroundColor:L,borderColor:L}).add(da).textBound()}})}}});Z.dragnode=C;$.stepzoom=ka($.line,{states:{hover:{}}});var Ac=function(a,b,c){a<=0.01&&(a=0);var e=this.stepZoom,
b=this.series,g=this.plotWidth,j=g/2,f=b.length,a=M(a,e.scrollPosition,0);a*=e.scrollablePXLength;var o,h,z,n,m,q,r=this.plotLeft-a;e.catGroup.attr("translateX",r);this.trackerGroup.attr("translateX",r);e.catClipRect.attr("x",a+e.catLabelClipXDisplacement);this.clipRect&&this.clipRect.attr("x",a);for(o=0;o<f;o+=1)if(h=b[o],h.group&&h.group.attr("translateX",r),h.dataLabelsGroup&&h.dataLabelsGroup.attr("translateX",r),h.clipRect&&h.clipRect.attr("x",a),c){e=O.min(20,j);n=h.data;z=n.length;for(q=0;q<
z;q+=1)m=n[q],m.y!==null&&m.tooltipPos&&(h=m.plotX-a,h>=0&&h<=j?h+=e:h>j&&h<=g&&(h-=e),m.tooltipPos[0]=h)}},C=Za.extendClass(Z.line,{type:"stepzoom",translate:function(){for(var a=this.chart,b=this.options.stacking,c=this.xAxis.categories,g=this.yAxis,j=this.data,f=j.length;f--;){var o=j[f],h=o.x,z=o.y,n=o.low,m=g.stacks[(z<0?"-":"")+this.stackKey];o.plotX=this.xAxis.translate(h);if(b&&m&&m[h]){var h=m[h],m=h.total,q=g.options.min,n=h.cum;h.cum+=z;z=na(q,n+z);n=na(q,n);b==="percent"&&(n=m?n*100/m:
0,z=m?z*100/m:0);o.percentage=m?o.y*100/m:0;o.stackTotal=m}if(t(n))o.yBottom=g.translate(n,0,1,0,1);if(z!==null)o.plotY=g.translate(z,0,1,0,1);o.clientX=a.inverted?a.plotHeight-o.plotX:o.plotX;o.category=c&&c[o.x]!==e?c[o.x]:o.x}},drawPoints:function(){var a,b=this.data,c=this.chart,g,j,o,f,z,n,m,q;a=c.stepZoom.zoomHistory[c.stepZoom.currentZoomLevelIndex];var r=a.perPointPixelDistance,l=a.stepping,P=a.seriesStart,v;if(this.options.marker.enabled)for(o=b.length;o--;)if(f=b[o],v=o-P,g=f.plotX=v*r,
j=f.plotY,q=f.graphic,j!==e&&!isNaN(j))a=f.pointAttr[f.selected?"select":xa],m=a.r,z=g+20,n=j-15,z=z<0?0:z,n=n<0?0:n,f.tooltipPos=[z,n],q?q.animate({x:g,y:j,r:m}):f.graphic=c.renderer.symbol(h(f.marker&&f.marker.symbol,this.symbol),g,j,m).attr(a).add(this.group),(v<0||v%l!==0)&&f.graphic.hide()},drawGraph:function(){var a=this.options,b=this.chart,c=[],e=this.group,g=a.lineColor||this.color,j=a.lineWidth,o=a.dashStyle,f=b.renderer,h=this.data,z=a.connectNullData,b=b.stepZoom.zoomHistory[b.stepZoom.currentZoomLevelIndex],
m,q,r=0,l=[],P=!0,v;for(m=b.seriesStart;m<h.length;m+=b.stepping,r+=b.visiblePointDistance)(q=h[m])&&q.plotY?(v&&(c.push(n,l[0],l[1],D),v=!1),P?(v=!0,P=!1,l[0]=r,l[1]=q.plotY):c.push(r,q.plotY)):z||(P=!0);this.graphPath=c;this.singlePoints=[];h=this.graphLine;g={stroke:g,"stroke-width":j,"stroke-linecap":"round",dashstyle:o};h?this.graphLine.animate({d:c}):this.graphLine=f.path(c).attr(g).add(e).shadow(a.shadow,this.shadowGroup)},drawDataLabels:function(){if(this.options.dataLabels.enabled){var a,
b,c=this.data,e=this.options.dataLabels,g=this.dataLabelsGroup,j=this.chart,o=j.renderer,f=j.options.chart,z=e.y===null,n=j.plotHeight,m,r=j.stepZoom.zoomHistory[j.stepZoom.currentZoomLevelIndex],l=r.stepping,P=r.seriesStart,v,w=this.type=="floatedcolumn",D=p(e.style.fontSize),r=j.stepZoom,r=j.plotLeft-r.scrollPosition*r.scrollablePXLength,u=f.valuePadding;e.rotation=f.rotateValues==1?270:void 0;if(!g)g=this.dataLabelsGroup=o.g("data-labels").attr({visibility:this.visible?q:S,zIndex:6}).translate(r,
j.plotTop).add(),j.options.chart.hasScroll&&g.clip(this.clipRect);j=e.color;j==="auto"&&(j=null);e.style.color=h(j,this.color);B(c,function(c,j){v=j-P;var k=c.plotX!=null?c.plotX:-999,f=h(c.plotY,-999),q=c.dataLabel,r=e.align,p=(z?c.y>=0?-6:12:e.y)-u;m=e.formatter.call(c.getLabelConfig());if(m!=null){w&&(f=M(c.barY,f));a=k+e.x;b=f+p;b>n&&(b=n);b<D&&(b=D);if(q)q.attr({text:m}).animate({x:a,y:b});else if(t(m))q=c.dataLabel=o.text(m,a,b).attr({align:r,rotation:e.rotation,zIndex:1}).css(e.style).add(g);
(v<0||v%l!==0)&&q&&q.hide&&q.hide()}})}},drawTracker:function(){var a=this,c=a.chart,e=+new Date,g,j,f=c.renderer,o,h=c.stepZoom.zoomHistory[c.stepZoom.currentZoomLevelIndex],z=h.stepping,n=h.seriesStart,m;c.options.chart.hasScroll&&c.trackerGroup.clip(a.clipRect);B(a.data,function(h,q){if(h.y!==null){m=q-n;if(h.link!==void 0)var l={cursor:"pointer",_cursor:"hand"};g=h.pointAttr[h.selected?"select":xa];j=g.r+3;h.tracker=f.circle(h.plotX,h.plotY,j).attr({fill:r}).on($a?"touchstart":"mouseover",function(g){o=
g.relatedTarget||g.fromElement;if(c.hoverSeries!==a&&b(o,"isTracker")!==e)a.onMouseOver();h.onMouseOver()}).on("mouseout",function(c){if(!a.options.stickyTracking&&(o=c.relatedTarget||c.toElement,b(o,"isTracker")!==e))a.onMouseOut()}).css(l).add(c.trackerGroup);(m<0||m%z!==0)&&h.tracker&&h.tracker.hide&&h.tracker.hide()}})},render:function(){var a=this,b=a.chart,c=b.renderer,e=b.options.chart,g=a.data,j;if(!b.stepZoom){var o=g.length-1,f,h,z=e.stepZoom,n=M(z.pixelsperpoint,15),m=M(z.displayStartIndex,
0),r=M(z.displayEndIndex,o),l=b.plotWidth,P=b.plotLeft,v=z.scrollHeight,p=b.plotHeight+b.plotTop+M(z.scrollPadding,0),w,D;f=r-m;var B=l/f,g=B<n?Math.ceil(n/B):1,B=l/(f-f%g);f=m%g;h=o-o%g;w=(h-f)*B;D=l/w;j=w-l;w=j>0?(m-f)*B/(w-l):0;e=e.plotBorderWidth;e=c.scroller(P-e,p,l+e+e,v,!0,{size:z.scrollBtnWidth,padding:z.scrollBtnPadding},!1).attr({fill:z.scrollColor}).setScrollRatio(D).callback(function(a,c,e){Ac.call(b,a,c,e)}).add(b.seriesGroup);b.stepZoom={zoomHistory:[{seriesStart:f,seriesEnd:h,stepping:g,
perPointPixelDistance:B,visiblePointDistance:g*B,seriesConf:[],scrollRatio:D,scrollPosition:w,scrollablePXLength:j}],currentZoomLevelIndex:0,pixelsPerPoint:n,displayStartIndex:m,displayEndIndex:r,scrollRatio:1,maxIndex:o,scrollPosition:w,scrollablePXLength:j,scroller:e};n=a.xAxis;o=n.categories;j=n.options.labels;var m=0,n=b.stepZoom,u;h=p+v;v=j.align;p=j.rotation;r=j.style;m=M(parseInt(r.lineHeight),12);n.catClipRect=c.clipRect(0,0,l+m,b.chartHeight);n.catGroup=c.g("catGroup").translate(P,h).clip(n.catClipRect).add(b.seriesGroup);
if(o&&o.length>0){l=n.catLabelArr=[];h=o.length;P=j.y||0;j=j.x||0;n.catLabelClipXDisplacement=-(m/2);n.xDisplacement=j;for(m=0;m<h;m+=1)u=o[m],t(u)&&(z=m-f,D=z*B+j,D=l[m]=c.text(u,D,P).attr({align:v,rotation:p}).css(r).add(n.catGroup),(z<0||z%g!==0)&&D.hide())}e.setScrollPosition(w)}var ca,g=a.options;f=(B=(f=g.animation)&&a.animate)?f&&f.duration||500:0;var y=a.clipRect;w=b.stepZoom;w=w.scrollPosition*w.scrollablePXLength;e=b.plotLeft-w;if(!y&&(l=b.plotSizeX,P=b.plotSizeY,y=a.clipRect=!b.hasRendered&&
b.clipRect?b.clipRect:c.clipRect(w,0,l,P),y.cliprectX=w,y.cliprectY=0,y.cliprectW=l,y.cliprectH=P,!b.clipRect))b.clipRect=y;if(!a.group)ca=a.group=c.g("series"),ca.clip(a.clipRect).attr({visibility:a.visible?q:S,zIndex:g.zIndex}).translate(e,b.plotTop).add(b.seriesGroup);B&&a.animate(!0);a.drawGraph&&a.drawGraph();a.drawPoints();a.drawDataLabels();a.options.enableMouseTracking!==!1&&a.drawTracker();B&&a.animate();setTimeout(function(){y.isAnimating=!1;if((ca=a.group)&&y!==b.clipRect&&y.renderer)ca.clip(a.clipRect=
b.clipRect),y.destroy()},f);a.isDirty=!1}});Z.stepzoom=C;Gb=Za.Chart;Q=Za.createElement;Oa=Za.discardElement;R=Za.css;Va=Za.setOptions({lang:{downloadPNG:"Download PNG image",downloadJPEG:"Download JPEG image",downloadPDF:"Download PDF document",downloadSVG:"Download SVG vector image",exportButtonTitle:"Export to raster or vector image",printButtonTitle:"Print the chart"}});Va.navigation={menuStyle:{border:"1px solid #A0A0A0",background:"#FFFFFF"},menuItemStyle:{fontFamily:"Verdana, Arial",padding:"3px 5px",
background:ca,color:"#303030",fontSize:$a?"14px":"11px"},menuItemHoverStyle:{background:"#999999",color:"#FFFFFF"},buttonOptions:{align:"right",backgroundColor:{linearGradient:[0,0,0,15],stops:[[0.4,"#F7F7F7"],[0.6,"#E3E3E3"]]},borderColor:"#B0B0B0",borderRadius:2,borderWidth:1,height:15,hoverBorderColor:"#909090",hoverSymbolFill:"#81A7CF",hoverSymbolStroke:"#4572A5",symbolFill:"#E0E0E0",symbolStroke:"#A0A0A0",symbolX:8,symbolY:7.5,verticalAlign:"top",width:16,y:10}};Va.exporting={type:"image/png",
url:"http://export.highcharts.com/",width:800,enableImages:!1,buttons:{exportButton:{symbol:"exportIcon",x:-10,symbolFill:"#A8BF77",hoverSymbolFill:"#768F3E",_titleKey:"exportButtonTitle",menuItems:[{textKey:"downloadPNG",onclick:function(){this.exportChart()}},{textKey:"downloadJPEG",onclick:function(){this.exportChart({type:"image/jpeg"})}},{textKey:"downloadPDF",onclick:function(){this.exportChart({type:"application/pdf"})}},{textKey:"downloadSVG",onclick:function(){this.exportChart({type:"image/svg+xml"})}}]},
printButton:{symbol:"printIcon",x:-36,symbolFill:"#B5C9DF",hoverSymbolFill:"#779ABF",_titleKey:"printButtonTitle",onclick:function(){this.print()}}}};y(Gb.prototype,{getSVG:function(a){var b=this,c,g,f,o,h,n,m=ka(b.options,a);if(!Aa.createElementNS)Aa.createElementNS=function(a,b){var c=Aa.createElement(b);c.getBBox=function(){return Za.Renderer.prototype.Element.prototype.getBBox.apply({element:c})};return c};a=Q(j,null,{position:z,top:"-9999em",width:b.chartWidth+v,height:b.chartHeight+v},Aa.body);
y(m.chart,{renderTo:a,forExport:!0});m.exporting.enabled=!1;if(!m.exporting.enableImages)m.chart.plotBackgroundImage=null,m.chart.bgSWF=void 0,m.chart.bgImage=void 0,m.chart.logoURL=void 0;m.series=[];B(b.series,function(a){f=a.options;f.animation=!1;f.showCheckbox=!1;f.visible=a.visible;if(!m.exporting.enableImages&&f&&f.marker&&/^url\(/.test(f.marker.symbol))f.marker.symbol="circle";f.data=[];B(a.data,function(a){o=a.config;h={x:a.x,y:a.y,name:a.name};typeof o=="object"&&a.config&&o.constructor!=
Array&&y(h,o);h.visible=a.visible;f.data.push(h);m.exporting.enableImages||(n=a.config&&a.config.marker)&&/^url\(/.test(n.symbol)&&delete n.symbol});m.series.push(f)});c=new Za.Chart(m);B(["xAxis","yAxis"],function(a){B(b[a],function(b,d){var g=c[a][d],j=b.getExtremes(),f=j.userMin,j=j.userMax;(f!==e||j!==e)&&g.setExtremes(f,j,!0,!1)})});g=c.container.innerHTML;m=null;c.destroy();Oa(a);g=g.replace(/zIndex="[^"]+"/g,"").replace(/isShadow="[^"]+"/g,"").replace(/symbolName="[^"]+"/g,"").replace(/jQuery[0-9]+="[^"]+"/g,
"").replace(/isTracker="[^"]+"/g,"").replace(/url\(([\'\"]?)[^#]+#/g,"url($1#").replace(/<svg /,'<svg xmlns:xlink="http://www.w3.org/1999/xlink" ').replace(/ href=/g," xlink:href=").replace(/id=([^" >]+)/g,'id="$1"').replace(/class=([^" ]+)/g,'class="$1"').replace(/ transform /g," ").replace(/:(path|rect)/g,"$1").replace(/<img ([^>]*)>/gi,"<image $1 />").replace(/<\/image>/g,"").replace(/<image ([^>]*)([^\/])>/gi,"<image $1$2 />").replace(/width=(\d+)/g,'width="$1"').replace(/height=(\d+)/g,'height="$1"').replace(/hc-svg-href="/g,
'xlink:href="').replace(/style="([^"]+)"/g,function(a){return a.toLowerCase()});g=g.replace(/(url\(#highcharts-[0-9]+)&quot;/g,"$1").replace(/&quot;/g,"'");g.match(/ xmlns="/g).length==2&&(g=g.replace(/xmlns="[^"]+"/,""));return g},exportChart:function(a,b){var c,e=this.getSVG(b),a=ka(this.options.exporting,a);c=Q("form",{method:"post",action:a.url},{display:ca},Aa.body);B(["filename","type","width","svg"],function(b){Q("input",{type:S,name:b,value:{filename:a.filename||"chart",type:a.type,width:a.width,
svg:e}[b]},null,c)});c.submit();Oa(c)},print:function(){var a=this,b=a.container,c=[],e=b.parentNode,g=Aa.body,j=g.childNodes;if(!a.isPrinting)a.isPrinting=!0,B(j,function(a,b){if(a.nodeType==1)c[b]=a.style.display,a.style.display=ca}),g.appendChild(b),hb.print(),setTimeout(function(){e.appendChild(b);B(j,function(a,b){if(a.nodeType==1)a.style.display=c[b]});a.isPrinting=!1},1E3)},contextMenu:function(a,b,c,e,g,f){var o=this,h=o.options.navigation,n=h.menuItemStyle,q=o.chartWidth,r=o.chartHeight,
l="cache-"+a,P=o[l],p=na(g,f),w,t;if(!P)o[l]=P=Q(j,{className:m+a},{position:z,zIndex:1E3,padding:p+v},o.container),w=Q(j,null,y({MozBoxShadow:"1px 1px 3px rgba(0,0,0,0.5)",WebkitBoxShadow:"1px 1px 3px rgba(0,0,0,0.5)",boxShadow:"1px 1px 3px rgba(0,0,0,0.5)"},h.menuStyle),P),t=function(){R(P,{display:ca})},ha(P,"mouseleave",t),B(b,function(a){a&&(Q(j,{onmouseover:function(){R(this,h.menuItemHoverStyle)},onmouseout:function(){R(this,n)},innerHTML:a.text||Za.getOptions().lang[a.textKey]},y({cursor:"pointer"},
n),w)[$a?"ontouchstart":"onclick"]=function(){t();a.onclick.apply(o,arguments)})}),o.exportMenuWidth=P.offsetWidth,o.exportMenuHeight=P.offsetHeight;a={display:"block"};c+o.exportMenuWidth>q?a.right=q-c-g-p+v:a.left=c-p+v;e+f+o.exportMenuHeight>r?a.bottom=r-e-p+v:a.top=e+f-p+v;R(P,a)},addButton:function(a){function b(){n.attr(r);z.attr(q)}var c=this,e=c.renderer,g=ka(c.options.navigation.buttonOptions,a),j=g.onclick,f=g.menuItems,o=g.width,h=g.height,z,n,m,a=g.borderWidth,q={stroke:g.borderColor},
r={stroke:g.symbolStroke,fill:g.symbolFill};g.enabled!==!1&&(z=e.rect(0,0,o,h,g.borderRadius,a).align(g,!0).attr(y({fill:g.backgroundColor,"stroke-width":a,zIndex:19},q)).add(),m=e.rect(0,0,o,h,0).align(g).attr({fill:"rgba(255, 255, 255, 0.001)",title:Za.getOptions().lang[g._titleKey],zIndex:21}).css({cursor:"pointer"}).on("mouseover",function(){n.attr({stroke:g.hoverSymbolStroke,fill:g.hoverSymbolFill});z.attr({stroke:g.hoverBorderColor})}).on("mouseout",b).on("click",b).add(),f&&(j=function(){b();
var a=m.getBBox();c.contextMenu("export-menu",f,a.x,a.y,o,h)}),m.on("click",function(){j.apply(c,arguments)}),n=e.symbol(g.symbol,g.symbolX,g.symbolY,(g.symbolSize||8)/2).align(g,!0).attr(y(r,{"stroke-width":g.symbolStrokeWidth||1,zIndex:20})).add())}});Za.Renderer.prototype.symbols.exportIcon=function(a,b,c){return[n,a-c,b+c,D,a+c,b+c,a+c,b+c*0.5,a-c,b+c*0.5,"Z",n,a,b+c*0.5,D,a-c*0.5,b-c/3,a-c/6,b-c/3,a-c/6,b-c,a+c/6,b-c,a+c/6,b-c/3,a+c*0.5,b-c/3,"Z"]};Za.Renderer.prototype.symbols.printIcon=function(a,
b,c){return[n,a-c,b+c*0.5,D,a+c,b+c*0.5,a+c,b-c/3,a-c,b-c/3,"Z",n,a-c*0.5,b-c/3,D,a-c*0.5,b-c,a+c*0.5,b-c,a+c*0.5,b-c/3,"Z",n,a-c*0.5,b+c*0.5,D,a-c*0.75,b+c,a+c*0.75,b+c,a+c*0.5,b+c*0.5,"Z"]};Gb.prototype.callbacks.push(function(a){var b,c=a.options.exporting,e=c.buttons;if(c.enabled!==!1)for(b in e)a.addButton(e[b])})}})();
(function(){function U(c,g,f){var c=c.chart,e=b(c.charttopmargin,15),j=b(c.chartrightmargin,15),h=b(c.chartbottommargin,15),l=b(c.chartleftmargin,15),p=e+h,m=l+j;f*=0.7;g*=0.7;p>f&&(e-=(p-f)*e/p,h-=(p-f)*h/p);m>g&&(l-=(m-g)*l/m,j-=(m-g)*j/m);g={_FCconf:{0:{stack:{}},1:{stack:{}},x:{stack:{}},oriCatTmp:[],noWrap:!1,marginLeftExtraSpace:0,marginRightExtraSpace:0,marginBottomExtraSpace:0,marginTopExtraSpace:0,marimekkoTotal:0},chart:{alignTicks:!1,renderTo:w,ignoreHiddenSeries:!1,events:{},reflow:!1,
spacingTop:e,spacingRight:j,spacingBottom:h,spacingLeft:l,marginTop:e,marginRight:j,marginBottom:h,marginLeft:l,borderRadius:0,plotBackgroundColor:"#FFFFFF",style:{}},colors:["AFD8F8","F6BD0F","8BBA00","FF8E46","008E8E","D64646","8E468E","588526","B3AA00","008ED6","9D080D","A186BE","CC6600","FDC689","ABA000","F26D7D","FFF200","0054A6","F7941C","CC3300","006600","663300","6DCFF6"],credits:{href:"http://www.fusioncharts.com?BS=FCHSEvalMark",text:"FusionCharts",enabled:!0},global:{},labels:{items:[]},
lang:{},legend:{enabled:!0,symbolWidth:12,borderRadius:1,backgroundColor:"#FFFFFF",initialItemX:0,title:{text:w,x:0,y:0,padding:2},scroll:{},itemStyle:{}},loading:{},plotOptions:{series:{pointPadding:0,borderColor:"#333333",events:{},animation:c.animation==ja?!1:{duration:1E3},states:{hover:{enabled:!1},select:{enabled:!1}},dataLabels:{enabled:!0,color:"#555555",style:{},formatter:function(){return this.point.showPercentValues?parseInt(this.percentage*100,10)/100+"%":this.point.displayValue}},point:{events:{}}},
area:{states:{hover:{enabled:!1}},marker:{lineWidth:1,radius:3,states:{hover:{enabled:!1},select:{enabled:!1}}}},radar:{states:{hover:{enabled:!1}},marker:{lineWidth:1,radius:3,states:{hover:{enabled:!1},select:{enabled:!1}}}},areaspline:{states:{hover:{enabled:!1}},marker:{lineWidth:1,radius:3,states:{hover:{enabled:!1},select:{enabled:!1}}}},line:{shadow:!0,states:{hover:{enabled:!1}},marker:{lineWidth:1,radius:3,states:{hover:{enabled:!1},select:{enabled:!1}}}},scatter:{states:{hover:{enabled:!1}},
marker:{lineWidth:1,radius:3,states:{hover:{enabled:!1},select:{enabled:!1}}}},bubble:{states:{hover:{enabled:!1}},marker:{lineWidth:1,radius:3,states:{hover:{enabled:!1},select:{enabled:!1}}}},spline:{states:{hover:{enabled:!1}},marker:{lineWidth:1,radius:3,states:{hover:{enabled:!1},select:{enabled:!1}}}},pie:{size:"80%",allowPointSelect:!0,cursor:"pointer",point:{events:{legendItemClick:c.interactivelegend===ja?Jb:function(){this.slice();return!1}}}},column:{},floatedcolumn:{},column3d:{},bar:{},
bar3d:{}},point:{},series:[],subtitle:{text:w,style:{}},symbols:[],title:{text:w,style:{}},toolbar:{},tooltip:{borderRadius:1,style:{},formatter:function(){return this.point.showPercentInToolTip?this.point.toolText+parseInt(this.percentage*100,10)/100+"%":this.point.toolText}},xAxis:{labels:{x:0,style:{},enabled:!1},lineWidth:0,plotLines:[],plotBands:[],title:{style:{},text:w},tickWidth:0,scroll:{enabled:!1}},yAxis:[{startOnTick:!1,endOnTick:!1,title:{style:{},text:w},tickLength:0,labels:{x:0,style:{}},
plotBands:[],plotLines:[]},{tickLength:0,gridLineWidth:0,startOnTick:!1,endOnTick:!1,title:{style:{},text:w},labels:{x:0,style:{},enabled:!1,formatter:function(){return this.value!==A?this.value:w}},opposite:!0,plotBands:[],plotLines:[]}],exporting:{buttons:{exportButton:{},printButton:{enabled:!1}}}};if(c.palettecolors&&typeof c.palettecolors==="string")g.colors=c.palettecolors.split(ya);return g}var y=FusionCharts(["private","modules.renderer.highcharts-base"]);if(y!==void 0){var p=y.hcLib,A=p.BLANKSTRINGPLACEHOLDER,
w=p.BLANKSTRING,gb=p.createTrendLine,f=p.pluck,t=p.getValidValue,b=p.pluckNumber,l=p.defaultPaletteOptions,h=p.getFirstValue,R=p.getDefinedColor,Q=p.parseUnsafeString,K=p.FC_CONFIG_STRING,ia=p.extend2,E=p.getDashStyle,fa=p.toPrecision,sa=p.graphics.getColumnColor,oa=p.getFirstColor,Oa=p.setLineHeight,sb=p.pluckFontSize,Gb=p.getFirstAlpha,Hb=p.graphics.getDarkColor,Nb=p.graphics.getLightColor,C=p.graphics.convertColor,J=p.COLOR_TRANSPARENT,M=p.POSITION_CENTER,cb=p.POSITION_TOP,jb=p.POSITION_BOTTOM,
Ta=p.POSITION_RIGHT,kb=p.POSITION_LEFT,V=p.chartAPI,Cb=p.graphics.mapSymbolName,ya=p.COMMASTRING,ja=p.ZEROSTRING,ub=p.ONESTRING,eb=p.HUNDREDSTRING,Ra=p.PXSTRING,xb=p.BGRATIOSTRING,lb=p.COMMASPACE,u=p.TESTSTR,L=p.graphics.getAngle,Qa=p.POSITION_MIDDLE,yb=p.STRINGUNDEFINED,X=p.axisLabelAdder,Jb=p.falseFN,fc=p.SmartLabelManager,uc=p.NumberFormatter,Aa=p.getLinkAction,hb=p.getAxisLimits,O=p.HASHSTRING,F=p.createDialog,mb=p.regex.dropHash,Fa=Math.max,na=Math.min,nb=p.Highcharts,va={fontWeight:{1:"bold",
0:"normal"},fontStyle:{1:"italic",0:"normal"},textDecoration:{1:"underline",0:"none"}},Ja={font:function(b,g){g.style.fontFamily=b},size:function(b,g){if(b)g.style.fontSize=sb(b)+Ra},color:function(b,g,f){g.style.color=b&&b.replace&&b.replace(mb,O)||w;if(f)g.color=g.style.color},bgcolor:function(b,g){g.style.backgroundColor=b&&b.replace&&b.replace(mb,O)||w},bordercolor:function(b,g){g.style.border="1px solid";g.style.borderColor=b&&b.replace&&b.replace(mb,O)||w},ishtml:w,leftmargin:function(c,g){g.style.marginLeft=
b(c,0)+Ra},letterspacing:function(c,g){g.style.letterSpacing=b(c,0)+Ra},bold:function(b,g){g.style.fontWeight=va.fontWeight[b]||""},italic:function(b,g){g.style.fontStyle=va.fontStyle[b]||""},underline:function(b,g){g.style.textDecoration=va.textDecoration[b]||""}},Ma={text:"",style:{}},pb={font:function(b,g,f){var e,j=!1,h,l,p;switch(g){case "caption":g=b.title;break;case "datalabels":g=b.xAxis.labels;break;case "datavalues":g=b.plotOptions.series.dataLabels;j=!0;break;case "subcaption":g=b.subtitle;
break;case "tooltip":g=b.tooltip;break;case "trendvalues":g={style:b[K].trendStyle};break;case "xaxisname":g=b.xAxis.title;break;case "yaxisname":g=[];h=0;for(l=b.yAxis.length;h<l;h+=1)g.push(b.yAxis[h].title);break;case "yaxisvalues":g=[];h=0;for(l=b.yAxis.length;h<l;h+=1)g.push(b.yAxis[h].labels);break;case "vlinelabels":g={style:b[K].divlineStyle};break;default:g=Ma}if(typeof g==="object")if(g instanceof Array){h=0;for(l=g.length;h<l;h+=1){p=g[h];for(e in f)if(b=e.toLowerCase(),typeof Ja[b]===
"function")Ja[b](f[e],p,j);Oa(p.style)}}else{for(e in f)if(b=e.toLowerCase(),typeof Ja[b]==="function")Ja[b](f[e],g,j);Oa(g.style)}}},zb={chart2D:{bgColor:"bgColor",bgAlpha:"bgAlpha",bgAngle:"bgAngle",bgRatio:"bgRatio",canvasBgColor:"canvasBgColor",canvasBaseColor:"canvasBaseColor",divLineColor:"divLineColor",legendBgColor:"legendBgColor",legendBorderColor:"legendBorderColor",toolTipbgColor:"toolTipbgColor",toolTipBorderColor:"toolTipBorderColor",baseFontColor:"baseFontColor",anchorBgColor:"anchorBgColor"},
chart3D:{bgColor:"bgColor3D",bgAlpha:"bgAlpha3D",bgAngle:"bgAngle3D",bgRatio:"bgRatio3D",canvasBgColor:"canvasBgColor3D",canvasBaseColor:"canvasBaseColor3D",divLineColor:"divLineColor3D",divLineAlpha:"divLineAlpha3D",legendBgColor:"legendBgColor3D",legendBorderColor:"legendBorderColor3D",toolTipbgColor:"toolTipbgColor3D",toolTipBorderColor:"toolTipBorderColor3D",baseFontColor:"baseFontColor3D",anchorBgColor:"anchorBgColor3D"}},dc=function(){var b={},g,f=function(){var e,j,f,o,h=0;for(e in b)if(h+=
1,j=b[e],f=j.jsVars,o=j.ref&&j.ref.parentNode){if(!f.resizeLocked&&(f._containerOffsetW!==o.offsetWidth||f._containerOffsetH!==o.offsetHeight))j.ref.resize(),f._containerOffsetW=o.offsetWidth,f._containerOffsetH=o.offsetHeight}else delete b[e],h-=1;h||(g=clearInterval(g))};return function(e){var j=e.jsVars,h=e.ref&&e.ref.parentNode||{};j._containerOffsetW=h.offsetWidth;j._containerOffsetH=h.offsetHeight;b[e.id]=e;g||(g=setInterval(f,300))}}(),ec={getExternalInterfaceMethods:function(){var b=V[this.jsVars.type],
b=b&&b.eiMethods,g="saveAsImage,print,exportChart,getXML,hasRendered,signature,cancelExport,";if(typeof b==="string")g+=b+ya;else if(b!==void 0||b!==null)for(var f in b)g+=f+ya;return g.substr(0,g.length-1)},drawOverlayButton:function(b){var g=this.jsVars,f=g.$overlayButton;if(b&&b.show){if(!f)f=g.$overlayButton=jQuery("<span>"),f.click(function(){y.raiseEvent("OverlayButtonClick",b,g.fcObj)});f.html(b.message?b.message:"Back");f.css({border:"1px solid #"+(b.borderColor?b.borderColor:"7f8975"),backgroundColor:"#"+
(b.bgColor?b.bgColor:"edefec"),fontFamily:b.font?b.font:"Verdana",color:"#"+b.fontColor?b.fontColor:"49563a",fontSize:(b.fontSize?b.fontSize:"10")+Ra,padding:(b.padding?b.padding:"3")+Ra,fontWeight:parseInt(b.bold,10)===0?"normal":"bold",position:"absolute",top:"0",right:"1px",_cursor:"hand",cursor:"pointer"});g.hcObj.container.appendChild(f[0])}else f&&f.detach()},print:function(){return this.jsVars.hcObj&&this.jsVars.hcObj.hasRendered&&this.jsVars.hcObj.print()},exportChart:function(b){var g=this.jsVars.hcObj,
f={},e;if(g&&g.options&&g.options.exporting&&g.options.exporting.enabled){if(!(b===void 0||b===null))for(e in b)switch(e.toLowerCase()){case "exportfilename":f.filename=b[e];break;case "exportformat":f.type={png:"image/png",jpg:"image/jpeg",pdf:"application/pdf",svg:"image/svg+xml"}[b[e].toLowerCase()]||"image/png"}g.exportChart(f)}},getSVGString:function(){return this.jsVars&&this.jsVars.hcObj&&this.jsVars.hcObj.getSVG&&this.jsVars.hcObj.getSVG()},resize:function(){var b=this.jsVars,g=b.container,
f=b.fcObj,e=b.hcObj;e&&(e&&e.destroy&&e.destroy(),p.createChart(b.fcObj,g,b.type,void 0,void 0,!1),delete b.isResizing,p.raiseEvent("resized",{width:f.width,height:f.height,prevWidth:b.width,prevHeight:b.height},f,[f.id]))},lockResize:function(b){return this.jsVars.resizeLocked=b===void 0&&!0||b},showChartMessage:function(b){var g=this.jsVars,f=g.hcObj;g.msgStore[b]&&(b=g.msgStore[b]);f&&f.destroy&&f.destroy();p.createChart(g.fcObj,g.container,g.type,void 0,b);return b},signature:function(){return"FusionCharts/3.2.2 (XT)"}};
p.createChart=function(b,g,f,e,j,h){var l=b.jsVars,w=l.msgStore,m,q=V[f],v,t=function(j){var h,m=l.fcObj,z=m.width,w=m.height;h=q&&q.eiMethods;var t=l.$overlayButton;g.jsVars=b.jsVars;l.container=g;l.hcObj=j;l.type=f;l.width=g.offsetWidth;l.height=g.offsetHeight;l.instanceAPI=v;g.style.backgroundColor=l.transparent?"transparent":"#ffffff";if(j.hasRendered){y.extend(g,ec);if(h&&typeof h!=="string")for(var S in h)g[S]=h[S];h={width:l.width,height:l.height,drawCount:l.drawCount};t&&j.container.appendChild(t[0]);
p.raiseEvent("drawcomplete",h,m,[m.id])}e&&(e({success:j.hasRendered,ref:g,id:b.id}),j.hasRendered&&(z=Number((z&&z.match&&z.match(/^\s*(\d*\.?\d*)\%\s*$/)||[])[1]),w=Number((w&&w.match&&w.match(/^\s*(\d*\.?\d*)\%\s*$/)||[])[1]),(z||w)&&m.ref&&m.ref.parentNode&&dc(m),p.raiseEvent("loaded",{type:f},m,[m.id])))};l.instanceAPI&&l.instanceAPI.dispose&&l.instanceAPI.dispose();v=q?new V(f):new V("stub");v.chartInstance=b;if(j!==void 0){if(typeof j==="string")j=new F(g,j),l.hasNativeMessage=!0}else!q||!q.init?
(j=new F(g,w.ChartNotSupported),l.hasNativeMessage=!0):l.message?(j=new F(g,l.message),l.hasNativeMessage=!0):l.loadError?(j=new F(g,w.LoadDataErrorText),l.hasNativeMessage=!0):l.stallLoad?(j=new F(g,w.XMLLoadingText),l.hasNativeMessage=!0):(j=b.getChartData(FusionChartsDataFormats.JSON,!0),m=j.data,j.error instanceof Error?(j=new F(g,w.InvalidXMLText),l.hasNativeMessage=!0,p.raiseEvent("dataxmlinvalid",{},l.fcObj,[l.fcObj.id])):(p.raiseEvent("dataloaded",{},l.fcObj,[l.fcObj.id]),j=v.init(g,m,b,t),
l.drawCount+=1,j.series.length===0?(j=new F(g,w.ChartNoDataText),l.hasNativeMessage=!0,p.raiseEvent("nodatatodisplay",{},l.fcObj,[l.fcObj.id])):(l.hasNativeMessage=!1,delete l.message)));if(!j)j=new F(g,"Error rendering chart {0x01}"),l.hasNativeMessage=!0;j.chart=j.chart||{};j.chart.renderTo=g;j.instanceAPI=v;j.credits=j.credits||{};j.credits.enabled=q&&q.creditLabel===!0?!0:!1;if(h===!1)j.chart.animation=!1,(j.plotOptions||!(j.plotOptions={}))&&(j.plotOptions.series||(j.plotOptions.series={})),
j.plotOptions.series.animation=!1;return v.draw(j,t)};var Yb=p.axisMinMaxSetter=function(c,g,f,e,j,h,l,p){f=g.stacking100Percent?hb(99,1,100,0,j,h,l,p):hb(b(g.max,f),b(g.min,e),f,e,j,h,l,p);c.min=Number(fa(f.Min,10));c.max=Number(fa(f.Max,10));c.tickInterval=Number(fa(f.divGap,10));if(f.Range/f.divGap<=2)c.alternateGridColor=J;delete g.max;delete g.min},Kb=p.configureAxis=function(c,g,o,e,j,h,p,t,m,q){var v;v=o.min;var u=o.max,n=o.tickInterval,D=q?"xAxis":e.stacking100Percent?"percentValue":m?"sYAxis":
"yAxis",r=v,y=1,Y,E=o.gridLineColor,A=o.gridLineWidth,G=o.gridLineDashStyle,Ka=v<0&&u>0?!0:!1,F=!0,ua,T=1,ba=g[K].axisGridManager;if(q&&!e.catOccupied)e.catOccupied={};if(Ka&&(!q||!e.catOccupied[0]))q?(F=Boolean(b(c.showvzeroplane,1)),Y=b(c.vzeroplanethickness,1),c=Y>0?C(f(c.vzeroplanecolor,E),f(c.vzeroplanealpha,c.vdivlinealpha,l.divLineAlpha[g.chart.paletteIndex])):J):(ua=b(c.divlinealpha,l.divLineAlpha[g.chart.paletteIndex]),g.chart.defaultSeriesType==="line"||g.chart.defaultSeriesType==="spline"||
g[K].is3d?(F=Boolean(b(c.showzeroplane,1)),Y=1):(Y=A===1?2:A,T=5,ua*=2),Y=b(c.zeroplanethickness,Y),c=Y>0?C(f(c.zeroplanecolor,E),f(c.zeroplanealpha,ua)):J),F&&(F=h===1?t[D](0):w,ba.addAxisGridLine(o,0,F,Y,G,c,T,q));if(j===1&&(!q||!e.catOccupied[v]))F=t[D](v),ba.addAxisGridLine(o,v,F,0.1,void 0,J,1,q);A<=0&&(A=0.1,E=J);for(v=Number(fa(r+n,10));v<u;v=Number(fa(v+n,10)),y+=1){Ka&&r<0&&v>0&&!m&&(ba.addAxisAltGrid(o,0),y+=1);if(v!==0&&(!q||!e.catOccupied[v]))F=h===1&&y%p===0?t[D](v):w,ba.addAxisGridLine(o,
v,F,A,G,E,2,q);r=v;m||ba.addAxisAltGrid(o,v)}m||ba.addAxisAltGrid(o,u);if(j===1&&y%p===0&&(!q||!e.catOccupied[u]))F=t[D](u),ba.addAxisGridLine(o,u,F,0.1,G,J,2,q);o.labels.enabled=!1;o.gridLineWidth=0;o.alternateGridColor=J;o.plotLines.sort($a)},ib=p.placeVerticalAxis=function(c,g,f,e,j,h,l,p,m,q){var v=f[K],ca=v.smartLabel,n,D,r,y,Y=0,p=v.marginRightExtraSpace,C=v.marginLeftExtraSpace,E={},G={},A={},J=c.plotLines,ua=c.plotBands,T=g.verticalAxisValuesPadding,ba=g.verticalAxisValuesPadding,F=g.verticalAxisNamePadding,
U=g.verticalAxisNameWidth,R=g.rotateVerticalAxisName,L=0,da=0,M=0,B=0,N=0,O=0,ka,ha,X,Q,la,v=2,ea=function(b,c){var e,f;if(b&&b.label&&t(b.label.text)!==void 0){Q=b.label;if(Q.style&&Q.style!==X)X=Q.style,ca.setStyle(X);n=ca.getOriSize(b.label.text);f=(e=n.width)?e+2:0;if(b.isGrid){if(E[c]={width:e,height:n.height,label:Q},M<=f)M=f,g.lYLblIdx=c}else b.isTrend&&(l&&Q.textAlign===kb||Q.textAlign===Ta?(G[c]={width:e,height:n.height,label:Q},B=Fa(B,f)):(A[c]={width:e,height:n.height,label:Q},N=Fa(N,f)))}},
O=function(b,e){var g,f=e?Y:Y+b;return f>0?(R?(f<D.height&&(D=ca.getSmartText(c.title.text,j,f)),g=D.height):(f<D.width&&(D=ca.getSmartText(c.title.text,f,j)),g=D.width),c.title.text=D.text,e?f-g+b:f-g):(c.title.text=w,0)},Z=function(b,c,e){for(var g in b)b[g].label.x=c,b[g].label.y=e};ka=0;for(ha=ua.length;ka<ha;ka+=1)ea(ua[ka],ka);ka=0;for(ha=J.length;ka<ha;ka+=1)ea(J[ka],ka);if(c.title&&c.title.text!=w)X=c.title.style,ca.setStyle(X),r=ca.getOriSize(u).height,R?(D=ca.getSmartText(c.title.text,j,
h),Y=D.height,y=r):(c.title.rotation=0,D=ca.getSmartText(c.title.text,U!==void 0?U:h,j),Y=D.width,y=20);N>0&&(da=N+ba);m&&(e=b(e.chart.maxlabelwidthpercent,0),e>=1&&e<=100&&(m=e*m/100,M>m&&(M=m)));L=Fa(B,M);Y>0&&(L+=Y+2+F);(function(){if(da+L>h){la=da+L-h;if(da)if(ba>=la){ba-=la;return}else la-=ba,ba=0;if(T+F>=la)F>=la?F-=la:(T-=la-F,F=0);else{la-=T+F;F=T=0;if(N>20)if(B>M)if(N-B>=la){N-=la;return}else if(B-N>=la){B-=la;return}else if(B>N?(la-=B-N,B=N):(la-=N-B,N=B),2*(B-M)>=la){N-=la/2;B-=la/2;return}else la-=
2*(B-M),N=B=M;else if(N-20>=la){N-=la;return}else la-=N-20,N=20;if(B>M)if(B-M>=la){B-=la;return}else la-=B-M,B=M;Y-y>=la?Y-=la:(la-=Y-y,Y=y,N>=la?N=0:(la-=N,N=0,Y>=la?Y=0:(la-=Y,Y=0,M>=la&&(M-=la,B=M))))}}})();surplusWidth=function(b,c){var e,g=0,f=c?N-2:N+b-2;if(N>0){for(ka in A)if(Q=A[ka].label,A[ka].width>f){if(Q.style&&Q.style!==X)X=Q.style,ca.setStyle(X);e=ca.getSmartText(Q.text,f,j,!0);Q.text=e.text;A[ka].height=e.height;g=Math.max(g,e.width)}else g=Math.max(g,A[ka].width);return c?f-g+b:f-
g}else{for(ka in A)A[ka].label.text=w;return 0}}(0,!0);surplusWidth=O(surplusWidth,!0);surplusWidth=function(b){var c=0,e=Math.max(M,B)+b-2;if(e>0){for(ka in E)if(Q=E[ka].label,E[ka].width>e){if(Q.style&&Q.style!==X)X=Q.style,ca.setStyle(X);b=ca.getSmartText(Q.text,e,j,!0);Q.text=b.text;E[ka].height=b.height;c=Math.max(c,b.width)}else c=Math.max(c,E[ka].width);for(ka in G)if(Q=G[ka].label,G[ka].width>e){if(Q.style&&Q.style!==X)X=Q.style,ca.setStyle(X);b=ca.getSmartText(Q.text,e,j,!0);Q.text=b.text;
G[ka].height=b.height;c=Math.max(c,b.width)}else c=Math.max(c,G[ka].width);return e-c}else{for(ka in E)E[ka].label.text=w;for(ka in G)G[ka].label.text=w;return 0}}(surplusWidth);surplusWidth=O(surplusWidth);m=g.verticalAxisNamePadding-F;surplusWidth&&m&&(surplusWidth>m?(F+=m,surplusWidth-=m):(F+=surplusWidth,surplusWidth=0));m=g.verticalAxisValuesPadding-T;surplusWidth&&m&&(surplusWidth>m?(T+=m,surplusWidth-=m):(T+=surplusWidth,surplusWidth=0));m=g.verticalAxisOppValuesPadding-ba;surplusWidth&&m&&
(surplusWidth>m?(ba+=m,surplusWidth-=m):(ba+=surplusWidth,surplusWidth=0));N>0&&(da=N+ba);L=Fa(B,M)+T;Y>0&&(L+=Y+F);m=Fa(B,M);m+=m>0?F:0;Y>0?(m+=F+(m>0?2:0),R?Y<D.height&&(D=ca.getSmartText(c.title.text,j,Y)):(Y<D.width&&(D=ca.getSmartText(c.title.text,Y,j)),c.title.y=-((D.height-r)/2)),c.title.text=D.text,c.title.margin=m+C+(R?D.height-r:D.width/2)):c.title.text=w;r=-(T+C+2);p=p+T+2;O=Fa(B,M);c.labels.style&&(v=parseInt(c.labels.style.fontSize,10)*0.35);l?(N>0&&Z(A,r,v),O>0&&(Z(E,p,v),Z(G,p,v))):
(N>0&&Z(A,p,v),O>0&&(Z(E,r,v),Z(G,r,v)));q?(f.chart.marginLeft+=l?da:L-q,f.chart.marginRight+=l?L-q:da):(f.chart.marginLeft+=l?da:L,f.chart.marginRight+=l?L:da);return da+L},Lb=p.titleSpaceManager=function(c,g,f,e){var j=g.chart,h=Q(j.caption),g=Q(j.subcaption),l=j=b(j.captionpadding,10),p=c[K].smartLabel,m=!1,q=0,v,t,n=0,D=0,r=0,u=0,y=0,C=0,E=c.title,G=c.subtitle;if(h!==w)v=E.style,r=b(parseInt(v.fontHeight,10),parseInt(v.lineHeight,10),12),C=b(parseInt(v.fontSize,10),10);if(g!==w)t=G.style,u=b(parseInt(t.fontHeight,
10),parseInt(t.lineHeight,10),12),y=b(parseInt(t.fontSize,10),10);if(r>0||u>0){q=r+u+j;q>e?(n=q-e,m=!0,n<j?j-=n:(n-=j,j=0,u>n?(D=u-n+10,u=0):(n-=u,u=0,r>n&&(D=r-n),r=0))):D=e-q;if(r>0)p.setStyle(v),r+=D,e=p.getSmartText(h,f,r),D=r-e.height,r=e.height,E.text=e.text,E.y=C;if(u>0)p.setStyle(t),u+=D,f=p.getSmartText(g,f,u),D=u-f.height,u=f.height,G.text=f.text,G.y=y+r;m&&D>0&&(j+=na(l-j,D));q=r+u+j;c.chart.marginTop+=q}return q},Ab=p.stepYAxisNames=function(b,g,f,e,j,h){for(var l=0,p=e.plotLines,m=[],
q,v=e.plotLines.length,g=g[K].smartLabel,t=parseFloat(sb(f.basefontsize,10)),n;l<v;l+=1)f=p[l],f.isGrid&&f.label&&f.label.text&&(m.push(f),f.value===0&&(q=m.length-1));if(v=m.length)if(e.labels.style?g.setStyle(e.labels.style):m[0].label&&m[0].label.style&&g.setStyle(e.labels.style),l=g.getOriSize("W").height,h||(l+=t*0.4),b/=v-1,b<l){h=Math.ceil(l/b);for(l=b=q;l<v;l+=1){f=m[l];if(l===j){if((l-b)%h&&n)n.label.text="";b=j}if(f&&f.label)(l-b)%h?f.label.text=w:n=f}for(l=b=q;l>=0;l-=1){f=m[l];if(l===
j){if((b-l)%h&&n)n.label.text="";b=j}if(f&&f.label)(b-l)%h?f.label.text=w:n=f}}},Zb=p.placeHorizontalAxis=function(c,g,f,e,j,h,l){var p=f[K],m,q,v,u,n,D,r,y,Y,C,E=0,G=0,A=10,J=1,F=0,T=F=0,ba=!1,Q=!1,R=!1,L=b(e.chart.labelstep,0),U=g.labelDisplay,da=g.horizontalLabelPadding,X=p.marginBottomExtraSpace;Y=f.chart.marginLeft;var B=f.chart.marginRight,N=p.smartLabel,O=g.catCount,ka=g.slantLabels,ha=j/(c.max-c.min),ja=0,V=0,T={w:0,h:0};if(c.labels.style)D=c.labels.style,N.setStyle(D),y=N.getOriSize("W"),
A=y.height,r=y.width+4,C=N.getOriSize("WWW").width+4;var la,ea,Z=[],oa=[],pa=0,ia=0,Ba,fa,sa,va=g.horizontalAxisNamePadding;la=0;var g=g.staggerLines,na=ja,Ca=!1;if(c.title&&c.title.text!=w)D=c.title.style,N.setStyle(D),F=N.getOriSize("W").height,c.title.rotation=0,u=N.getSmartText(c.title.text,j,h),G=u.height;f.chart.marginLeft!=parseInt(e.chart.chartleftmargin,10)&&(m=!0);f.chart.marginRight!=parseInt(e.chart.chartrightmargin,10)&&(ea=!0);la=j-l;switch(U){case "none":ba=R=!0;break;case "rotate":E=
ka?300:270;y=A;A=r;r=y;ba=!0;break;case "stagger":Q=ba=!0,e=Math.floor((h-F)/A),e<g&&(g=e)}p.isBar&&(ba=!0);e=0;l=c.plotLines;if(typeof f._FCconf.isXYPlot===yb&&!p.isBar){for(Ba=l.length;e<Ba;e+=1)(q=l[e])&&(q.isGrid?Z.push(q):q.isTrend&&oa.push(q));u=c.plotBands;e=0;for(Ba=u.length;e<Ba;e+=1)(q=u[e])&&oa.push(q);u=Z.length-1;Ba=Z.length;Q&&(g>Ba?g=Ba:g<2&&(g=2));if(Ba){c.scroll&&c.scroll.viewPortMin&&c.scroll.viewPortMax?(y=c.scroll.viewPortMin,v=c.scroll.viewPortMax,ea=m=!1):(y=c.min,v=c.max);e=
(Z[u].value-Z[0].value)*ha;fa=e/(O-1);sa=(Z[0].value-y)*ha;e=(v-Z[u].value)*ha;U==="auto"?fa<C&&(E=ka?300:270,y=A,A=r,r=y,ba=!0):U==="stagger"&&(fa*=g);Y=(sa+Y)*2;if((n=l[0].label)&&n.text)n.style&&N.setStyle(n.style),C=Math.min(fa,N.getOriSize(n.text).width+4),C>Y&&(pa=(C-Y)/2);Y=(e+B)*2;if((n=l[u].label)&&n.text)n.style&&N.setStyle(n.style),C=Math.min(fa,N.getOriSize(n.text).width+4),C>Y&&(ia=(C-Y)/2);e=pa+ia;e>0&&(la>e?(B=(B=ia*j/(ia+j))?B+4:0,f.chart.marginRight+=B,j-=B,B=(B=pa*j/(pa+j))?B+4:
0,f.chart.marginLeft+=B,j-=B,ha=j/(c.max-c.min)):pa<ia?la>=ia&&ea?(B=(B=ia*j/(ia+j))?B+4:0,f.chart.marginRight+=B,j-=B,ha=j/(c.max-c.min)):m&&(B=(B=pa*j/(pa+j))?B+4:0,f.chart.marginLeft+=B,j-=B,ha=j/(c.max-c.min)):la>=pa&&m?(B=(B=pa*j/(pa+j))?B+4:0,f.chart.marginLeft+=B,j-=B,ha=j/(c.max-c.min)):ea&&(B=(B=ia*j/(ia+j))?B+4:0,f.chart.marginRight+=B,j-=B,ha=j/(c.max-c.min)),e=(Z[u].value-Z[0].value)*ha,fa=e/(O-1));!Q&&!R&&(J=Math.ceil(r/fa),L&&(fa*=L,J=Math.ceil(r/fa)),fa*=J);for(v=0;v<Ba;v+=1)if(q=Z[v],
v%J&&q.label)q.label.text=w;else if(q&&q.label&&t(q.label.text)!==void 0){n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);if(!R)m=E||Q?N.getOriSize(n.text):N.getSmartText(n.text,fa-4,h,ba),T.w=Fa(T.w,m.width+4),T.h=Fa(T.h,m.height)}}v=0;for(Ba=oa.length;v<Ba;v+=1)if((q=oa[v])&&q.label&&t(q.label.text)!==void 0){n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);m=N.getOriSize(n.text);n.verticalAlign===jb?ja=Fa(ja,m.height):V=Fa(V,m.height)}c.scroll&&c.scroll.enabled&&!E&&!R&&(B=T.w/
2,f.chart.marginLeft<B&&(Y=B-f.chart.marginLeft,la>Y&&(j-=Y,la-=Y,f.chart.marginLeft+=Y)),f.chart.marginRight<B&&(Y=B-f.chart.marginRight,la>Y&&(j-=Y,la-=Y,f.chart.marginRight+=Y)))}else{var L={},$;sa=ia=0;var ya=null,Aa=null,O={},Ca=!0,ha=j/(c.max-c.min),U=function(b,e,g){var j,h,m,z,l,p;p=b.plotObj;l=b.labelTextWidth;if(!l){n=p.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);l=N.getOriSize(n.text).width+4;b.oriWidth=l;l>$&&(l=$);b.labelTextWidth=l;b.leftEdge=p.value*ha-l/2;b.rightEdge=p.value*
ha+l/2;if(g)l=Math.min(l,2*(q.value-c.min)*ha+f.chart.marginLeft),b.labelTextWidth=l}if(typeof e!==yb){g=e.plotObj;n=g.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);e.oriWidth?m=e.oriWidth:(m=N.getOriSize(n.text).width+4,e.oriWidth=m);m>$&&(m=$);e.labelTextWidth=m;e.leftEdge=g.value*ha-m/2;e.rightEdge=g.value*ha+m/2;j=p.value*ha;h=j+l/2;z=g.value*ha;m=z-m/2;if(m<h)if(j+r<z-r)h-=m,j=z-j,b.labelTextWidth=h>j?Math.min(l,j):Math.max(r,l-h/2),e.labelTextWidth=2*(j-b.labelTextWidth/2),b.leftEdge=
p.value*ha-b.labelTextWidth/2,b.rightEdge=p.value*ha+b.labelTextWidth/2,e.leftEdge=g.value*ha-e.labelTextWidth/2,e.rightEdge=g.value*ha+e.labelTextWidth/2;else return e.labelTextWidth=0,g.label.text=w,!1}else if(g)l=Math.min(l,2*(c.max-q.value)*ha+f.chart.marginRight),b.labelTextWidth=l,b.leftEdge=p.value*ha-l/2,b.rightEdge=p.value*ha+l/2;b.nextCat=e;return!0};Q?g>Ba?g=Ba:g<2&&(g=2):g=1;for(Ba=l.length;e<Ba;e+=1)if((q=l[e])&&q.label&&typeof q.label.text!==yb)q.isGrid?(pa={plotObj:q},q.isCat&&(u=e%
g,L[u]||(L[u]=[]),ya?(Aa=pa,L[u].push(Aa)):(Aa=ya=pa,L[u].push(ya))),Z.push(pa)):q.isTrend&&oa.push({plotObj:q});u=c.plotBands;e=0;for(Ba=u.length;e<Ba;e+=1)(q=u[e])&&q.isTrend&&q.label&&typeof q.label.text!==yb&&oa.push({plotObj:q});if(Z.length)if(!R&&!E)if(p.distributedColumns){e=0;for(Ba=Z.length;e<Ba;e+=1)if(v=Z[e],ea=e%g,q=v.plotObj,q.label&&q.isCat){e-g>=0?(m=Z[e-g],ea=m.plotObj.value*ha+m.plotObj._weight*ha/2):(m=null,ea=c.min*ha-Y);e+g<Ba?(y=Z[e+g],y=y.plotObj.value*ha-y.plotObj._weight*ha/
2):(y=null,y=c.max*ha+B);n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);pa=q.value*ha;la=pa-q._weight*ha/2;L=pa+q._weight*ha/2;g>1?(m=la-ea,ea=L+y,ea=L-la+Math.min(m,ea)):ea=L-la;n=q.label;n.style&&n.style!==D&&N.setStyle(n.style);ea<r&&r<N.getOriSize(n.text).width?(q.label.text=w,v.labelTextWidth=0):(v.labelTextWidth=ea,m=N.getSmartText(n.text,ea-4,h,ba),ea=m.width+4,v.labelTextWidth=ea,T.h=Math.max(T.h,m.height))}}else{Ba=Z.length;u=Z.length-1;(e=(Z[u].plotObj.value-Z[0].plotObj.value)*
ha)?($=e*0.1,l=Math.max(e*0.2,e/Ba)):l=$=j;for(v in L){e=0;for(C=L[v].length;e<C;){for(pa=e+1;!U(L[v][e],L[v][pa]);)pa+=1;e=pa}}ya&&(sa=(ya.plotObj.value-c.min)*ha+Y-ya.labelTextWidth/2);q=Z[0].plotObj;if(!ya||q!==ya.plotObj){n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);C=N.getOriSize(n.text).width+4;pa=(q.value-c.min)*ha+Y;ya&&(e=sa-pa,C=e<C&&e>r/2?e*2:0);Z[0].labelTextWidth=C;C>0&&(y=pa-C/2);y<sa&&(sa=y)}if(Aa)C=Aa.labelTextWidth,ia=(c.max-Aa.plotObj.value)*ha+B-C/2;q=Z[u].plotObj;
if(!Aa||q!==Aa.plotObj){n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);C=N.getOriSize(n.text).width+4;pa=(c.max-q.value)*ha+B;Aa&&(e=pa-ia,C=e<C&&e>r/2?e*2:0);Z[u].labelTextWidth=C;C>0&&(y=pa-C/2);y<ia&&(ia=y)}pa=sa<0?-sa:0;ia=ia<0?-ia:0;e=pa+ia;if(e>0)for(v in la>e?(B=(B=ia*j/(ia+j))?B+4:0,f.chart.marginRight+=B,j-=B,B=(B=pa*j/(pa+j))?B+4:0,f.chart.marginLeft+=B,j-=B,ha=j/(c.max-c.min)):pa<ia?la>=ia&&ea?(B=(B=ia*j/(ia+j))?B+4:0,f.chart.marginRight+=B,j-=B,ha=j/(c.max-c.min)):m&&(B=(B=
pa*j/(pa+j))?B+4:0,f.chart.marginLeft+=B,j-=B,ha=j/(c.max-c.min)):la>=pa&&m?(B=(B=pa*j/(pa+j))?B+4:0,f.chart.marginLeft+=B,j-=B,ha=j/(c.max-c.min)):ea&&(B=(B=ia*j/(ia+j))?B+4:0,f.chart.marginRight+=B,j-=B,ha=j/(c.max-c.min)),B=f.chart.marginRight,Y=f.chart.marginLeft,e=(Z[u].plotObj.value-Z[0].plotObj.value)*ha,$=e*0.1,l=Math.max(e*0.2,e/Ba),L){e=0;for(C=L[v].length;e<C;){for(pa=e+1;!U(L[v][e],L[v][pa],!0);)pa+=1;e=pa}v+=1}e=0;for(Ba=Z.length;e<Ba;e+=1)if(v=Z[e],ea=e%g,q=v.plotObj,q.label)if(q.isCat)v.labelTextWidth&&
(O[ea]=v);else{y=(m=O[ea])?m.nextCat:L[ea]?L[ea][0]:null;m=null;if(e>=g){ea=e-g;for(m=Z[ea];!m.labelTextWidth;)if(ea>=g)ea-=g,m=Z[ea];else{m=null;break}}ea=m?m.rightEdge:c.min*ha-Y;y=y?y.leftEdge:c.max*ha+B;n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);C=N.getOriSize(n.text).width+4;la=q.value*ha-C/2;if(p.isBar&&e==Ba-1&&m){if(ea>la)m.plotObj.label.text=w,m.labelTextWidth=0,ea=m.leftEdge}else if(ea>la||y<la+C){q.label.text=w;v.labelTextWidth=0;continue}ea=Math.max(ea,la);pa=q.value*ha;
ea=2*Math.min(pa-ea,y-pa);n=q.label;n.style&&n.style!==D&&N.setStyle(n.style);ea<r&&r<N.getOriSize(n.text).width?(q.label.text=w,v.labelTextWidth=0):(v.labelTextWidth=ea,m=N.getSmartText(n.text,ea-4,h,ba),ea=m.width+4,v.labelTextWidth=ea,v.leftEdge=pa-ea/2,v.rightEdge=pa+ea/2,T.h=Math.max(T.h,m.height))}m=B=ea=Y=null;e=0;for(Ba=Z.length;e<Ba;e+=1)if(v=Z[e],q=v.plotObj,ea=e%g,q.isCat&&v.labelTextWidth){m=B=null;pa=q.value*ha;if(e>=g){ea=e-g;for(m=Z[ea];!m.labelTextWidth;)if(ea>g)ea-=g,m=Z[ea];else{m=
null;break}}m=m?pa-m.rightEdge:pa-c.min*ha+f.chart.marginLeft;if(e+g<Ba){Y=e+g;for(B=Z[Y];!B.labelTextWidth;)if(Y+g<Ba-1)Y+=g,B=Z[Y];else{B=null;break}}ea=B?B.leftEdge-pa:c.max*ha+f.chart.marginRight-pa;ea=Math.min(m,ea)*2;ea>l&&(ea=l);if(ea>v.oriWidth)ea=v.oriWidth;v.labelTextWidth=ea;n=q.label;n.style&&n.style!==D&&N.setStyle(n.style);m=N.getSmartText(n.text,ea-4,h,ba);v.labelTextWidth=m.width+4;T.h=Math.max(T.h,m.height);v.rightEdge=pa+v.labelTextWidth/2}}else if(E){e=0;for(Ba=Z.length;e<Ba;e+=
1)if((q=Z[e].plotObj)&&q.label&&q.label.text){n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);v=1;if(e+v<Ba)for(B=Z[v+e].plotObj;B&&(B.value-q.value)*ha<r;)if(q.isCat){if(B.label){B.label.text=w;v+=1;if(v+e>=Ba-1)break;B=l[v+e].plotObj}}else if(B.isCat){q.label.text=w;q=B;e+=v-1;n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);break}T.w=Math.max(T.w,N.getOriSize(n.text).width+4)}}v=0;for(Ba=oa.length;v<Ba;v+=1)if((q=oa[v].plotObj)&&q.label&&t(q.label.text)!==void 0){n=q.label;if(n.style&&
n.style!==D)D=n.style,N.setStyle(D);m=N.getOriSize(n.text);n.verticalAlign===jb?ja=Fa(ja,m.height):V=Fa(V,m.height)}}la=R?A:E?T.w:Q?g*A:T.h;la>0&&(na+=da+la);G>0&&(na+=G+va);T=V+na+2;y=0;T>h&&(e=T-h,va>e?(va-=e,e=0):(e-=va,va=0,da>e?(da-=e,e=0):(e-=da,da=0)),V>e?(V-=e,e=0):(V>0&&(e-=V,V=0),e>0&&(ja>e?(ja-=e,e=0):(ja>0&&(e-=ja,ja=0),e>0&&((y=G-F)>e?(G-=e,e=0):(e-=y,G=F,e>0&&((y=la-A)>e?(la-=e,e=0):(e-=y,la=A,e>0&&(e-=G+va,G=0,e>0&&(e-=la,la=0,e>0&&(da-=e)))))))))));da+=X;var B=p.is3d?-f.chart.xDepth:
0,h=la+da,za,na=B;Y=A*0.5;F=A+da;Ba=Z.length;T=0;if(Ca)if(E){fa=Ta;za=ka?da+8:da+4;Ba=Z.length;for(v=0;v<Ba;v+=1)if((q=Z[v].plotObj)&&q.label&&t(q.label.text)!==void 0){n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);e=1;m=N.getSmartText(n.text,la-4,r,ba);n.text=m.text;na=B+Y/2;n.y=za;n.x=na;n.rotation=E;n.textAlign=fa;T+=1}}else{Ca=la;fa=M;za=F;for(v=0;v<Ba;v+=J)if((q=Z[v].plotObj)&&q.label&&t(q.label.text)!==void 0){n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);if(!R)m=N.getSmartText(n.text,
Z[v].labelTextWidth-4,Ca,ba),n.text=m.text,Q&&(za=F+T%g*A);n.y=za;n.x=na;n.rotation=E;n.textAlign=fa;T+=1}}else{E?(Ca=fa,e=la-4,fa=Ta,za=ka?da+8:da+4):Q?(Ca=A,e=fa*g-4,fa=M):(Ca=la,e=fa-4,fa=M,za=F);for(v=0;v<Ba;v+=J)if((q=Z[v])&&q.label&&t(q.label.text)!==void 0){n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);if(!R)m=N.getSmartText(n.text,e,Ca,ba),n.text=m.text,Q&&(za=F+T%g*A),E&&(na=B+Y/2);n.y=za;n.x=na;n.rotation=E;n.textAlign=fa;T+=1}}Ba=oa.length;for(v=A=E=0;v<Ba;v+=1)if((q=oa[v].plotObj?
oa[v].plotObj:oa[v])&&q.label&&t(q.label.text)!==void 0){n=q.label;if(n.style&&n.style!==D)D=n.style,N.setStyle(D);n.verticalAlign===jb?(m=N.getSmartText(n.text,j,ja,!0),A=Math.max(A,m.height),n.text=m.text,n.y=h+N.getOriSize("W").height,n.x=na):(m=N.getSmartText(n.text,j,V,!0),E=Math.max(E,m.height),n.text=m.text,n.y=-(V-N.getOriSize("W").height+da+2))}if(G>0)N.setStyle(c.title.style),u=N.getSmartText(c.title.text,j,G),c.title.text=u.text,c.title.margin=h+A+va;na=A;if(la>0)p.horizontalAxisHeight=
da+la-X,na+=p.horizontalAxisHeight;G>0&&(na+=G+va);f.chart.marginBottom+=na;E>0&&(f.chart.marginTop+=E,na+=E);return na},za=function(c,g,o,e,j){var z=c.legend,p=c.chart,t=p.paletteIndex,m=p.is3D?zb.chart3D:zb.chart2D,q=p.useRoundEdges,v=b(g.legendiconscale,1),u=(parseInt(z.itemStyle.fontSize,10)||10)-1,n=3;if(v<=0||v>5)v=1;z.padding=4;u<=0&&(u=1);j-=8;u*=v;n*=v;u=Math.min(u,j);u<=0&&(n=u=0);z.symbolWidth=u;z.symbolPadding=n;z.textPadding=4;z.legendHeight=j=u+2*n;z.rowHeight=Math.max(parseInt(z.itemStyle.lineHeight,
10)||12,j);o?(z.align=Ta,z.verticalAlign="middle",z.layout="vertical"):z.x=(p.marginLeft-p.spacingLeft-p.marginRight+p.spacingRight)/2;o=f(g.legendbordercolor,l[m.legendBorderColor][t]);p=b(g.legendborderalpha,100);j=b(g.legendbgalpha,100);z.backgroundColor=C(f(g.legendbgcolor,l[m.legendBgColor][t]),j);z.borderColor=C(o,p);z.borderWidth=b(g.legendborderthickness,!q||g.legendbordercolor?1:0);z.shadow=Boolean(b(g.legendshadow,1));if(z.shadow)z.shadow={enabled:z.shadow,opacity:Fa(p,j)/100};z.reversed=
Boolean(b(g.reverselegend,0));if(e)z.reversed=!z.reversed;z.style={padding:4};Boolean(b(g.interactivelegend,1))?z.symbolStyle={_cursor:"hand",cursor:"pointer"}:(c.plotOptions.series.events.legendItemClick=Jb,z.itemStyle.cursor="default",z.itemHoverStyle={cursor:"inherit"});z.borderRadius=q?3:0;z.legendAllowDrag=Boolean(b(g.legendallowdrag,0));z.title.text=Q(h(g.legendcaption,w));z.legendScrollBgColor=oa(f(g.legendscrollbgcolor,l.altHGridColor[c.chart.paletteIndex]));z.legendScrollBarColor=f(g.legendscrollbarcolor,
o);z.legendScrollBtnColor=f(g.legendscrollbtncolor,o);z.title.style=ia({fontWeight:"bold"},z.itemStyle)},Ca=p.placeLegendBlockRight=function(c,g,f,e,j){za(c,g.chart,!0,j,f);var h=0,l=c.series,p,m,q=c[K].smartLabel,v=c.legend,t,n=v.textPadding,u=v.title.padding,r=v.symbolWidth+2*v.symbolPadding,y=e*2,C=0,g=b(g.chart.legendpadding,7),E=2*v.padding,A={width:E,height:E};f-=E;j&&(l=l&&l[0]&&l[0].data);if(typeof l===yb||typeof l.length===yb)return 0;else p=l.length;t=f-r-2-n;t<0&&(t=0);for(q.setStyle(v.itemStyle);h<
p;h+=1)if(m=l[h],m.showInLegend!==!1)t===0?(A.height+=r,m.name=w):(j=q.getSmartText(m.name,t,y),m.name=j.text,A.height+=Math.max(j.height,r),C=Math.max(j.width,C));v.width=C+r+2+n+E;if(v.title.text!==w){q.setStyle(v.title.style);j=q.getSmartText(v.title.text,f,y);v.title.text=j.text;h=j.width+E;if(v.width<h)v.initialItemX=(h-v.width)/2,v.width=h;A.height+=j.height+u}v.height=v.totalHeight=A.height;if(v.height>e)v.height=e,v.scroll.enabled=!0,v.width+=(v.scroll.scrollBarWidth=10)+(v.scroll.scrollBarPadding=
2);g=Math.min(v.width+g,f);c.chart.marginRight+=g;return g},$b=p.placeLegendBlockBottom=function(c,g,f,e,j){za(c,g.chart,!1,j,f);var h=0,l=c.series,p=c[K].smartLabel,m=c.legend,q,v,t=m.textPadding,n=m.title.padding,D=m.symbolPadding;v=m.legendHeight;var r=g.chart,y=0,C=e*2,E=m.rowHeight,A=[],G=b(r.minimisewrappinginlegend,0),r=b(parseInt(r.legendnumcolumns,10),0),F=0,J=0,L=0,T=q=0,ba=2*m.padding,g=b(g.chart.legendpadding,7),Q={width:ba,height:ba};r<0&&(r=0);f-=ba;p.setStyle(m.itemStyle);q=p.getSmartText(u).height;
g=Math.min(g,e-q-8);e-=g;j&&(l=l&&l[0]&&l[0].data);if(typeof l===yb||typeof l.length===yb)return 0;else j=l.length;for(p.setStyle(m.itemStyle);h<j;h+=1)l[h].showInLegend!==!1&&(q=p.getOriSize(l[h].name),F=Math.max(F,q.width),J+=q.width,L+=1);q=J/L;q+=v+2+t;F+=v+2+t;r>0&&L<r&&(r=L);r>0&&(T=f/r)>q?T>F&&(T=F):f>F&&(G||q*1.5>F)?(r=Math.floor(f/F),L<r&&(r=L),T=F):f>=2*q?(r=Math.floor(f/q),L<r&&(r=L),T=Math.floor(f/r),T>F&&(T=F)):(r=1,T=f);m.itemWidth=T;v=T-v-2-t;v<0&&(D=v=t=0);m.symbolPadding=D;m.textPadding=
t;m.width=T*r+ba;if(m.title.text!==w){p.setStyle(m.title.style);q=p.getSmartText(m.title.text,f,C);m.title.text=q.text;h=q.width+ba;if(m.width<h)m.initialItemX=(h-m.width)/2,m.width=h;y=q.height+n}p.setStyle(m.itemStyle);n=0;if(m.reversed)for(h=j-1;h>=0;h-=1){if(f=l[h],f.showInLegend!==!1){if(v===0)A[n]=!0,f.name=w;else{q=p.getSmartText(f.name,v,C);for(f.name=q.text;A[n]===!0;)n+=1;f=q.height/E;D=n;for(t=0;t<f;t+=1,D+=r)A[D]=!0}n+=1}}else for(h=0;h<j;h+=1)if(f=l[h],f.showInLegend!==!1){if(v===0)A[n]=
!0,f.name=w;else{q=p.getSmartText(f.name,v,C);for(f.name=q.text;A[n]===!0;)n+=1;f=q.height/E;D=n;for(t=0;t<f;t+=1,D+=r)A[D]=!0}n+=1}Q.height+=Math.ceil(A.length/r)*E+y;m.height=m.totalHeight=Q.height;m.rowHeight=E;m.legendNumColumns=r;if(m.height>e)m.height=e,m.scroll.enabled=!0,m.width+=(m.scroll.scrollBarWidth=10)+(m.scroll.scrollBarPadding=2);g+=m.height;c.chart.marginBottom+=g;return g},$a=function(b,g){return b.value-g.value},Bb=p.adjustVerticalAxisTitle=function(b,g,f){if(g&&g.text){var e=g.text,
j=b[K].smartLabel,h=2*Math.min(b.chart.marginTop,b.chart.marginBottom)+f,l=f+b.chart.marginTop+b.chart.marginBottom;g.style&&j.setStyle(g.style);e=j.getOriSize(e);if(g.rotation=="0"){if(e.height>h)g.y=(l/2-(f/2+b.chart.marginTop))/2}else if(e.width>h)g.y=l/2-(f/2+b.chart.marginTop)}},Wb=p.adjustVerticalCanvasMargin=function(c,g,f,e){var j=g.chart,h=g=0,l=0,p=b(j.canvastopmargin,0),j=b(j.canvasbottommargin,0),m=p/(p+j),q=c.chart.marginTop,v=c.chart.marginBottom;j>v&&(g+=j-v);p>q&&(g+=p-q);g>f?p>q&&
j>v?(h=f*m,l=f*(1-m)):p>q?h=f:l=f:g>0&&(j>v&&(l=j-v),p>q&&(h=p-q));h&&(c.chart.marginTop+=h);l&&(c.chart.marginBottom+=l,e&&e.title&&(e.title.margin+=l));return h+l},tb=p.adjustHorizontalCanvasMargin=function(c,g,f,e,j){var h=g.chart,g=b(h.canvasleftmargin,0),h=b(h.canvasrightmargin,0),l=g/(g+h),p=0,m=c.chart.marginLeft,q=c.chart.marginRight,v=0,t=0;g>m&&(p+=g-m);h>q&&(p+=h-q);p>f?g>m&&h>q?(v=f*l,t=f*(1-l)):h>q?t=f:v=f:p>0&&(g>m&&(v=g-m),h>q&&(t=h-q));v&&(c.chart.marginLeft+=v,e&&e.title&&(e.title.margin+=
v));t&&(c.chart.marginRight+=t,j&&j.title&&(j.title.margin+=t));return t+v};V("base",{draw:function(b,g){return new nb.Chart(b,g)},init:function(c,g,f){this.dataObj=g=ia({},g);g.chart=g.chart||g.graph||{};delete g.graph;this.containerElement=c;this.config={};this.smartLabel=new fc(f.id,document.getElementsByTagName("body")[0]||c,b(g.chart.useellipseswhenoverflow,g.chart.useellipsewhenoverflow,1));if(!this.standaloneInit)return new p.createDialog(c,f.jsVars.msgStore.ChartNotSupported);return this.chart(c,
this.name,g,c.offsetWidth||parseFloat(c.style.width),c.offsetHeight||parseFloat(c.style.height),f)},chart:function(c,g,o,e,j,h){var p,u,m,q,v,y=this.defaultSeriesType,n,D,r=o.chart,A,Y,F,J,G,c=U(o,e,j);G=c.chart;J=c.xAxis;n=c[K];c.labels.smartLabel=v=n.smartLabel=this.smartLabel;n.width=e;n.height=j;D=c.plotOptions;n.isDual=this.isDual;n.numberFormatter=new uc(r,g);n.axisGridManager=new X(y,r);n.FCchartName=g;G.is3D=p=n.is3d=/3d$/.test(y);G.isBar=u=n.isBar=this.isBar;F=r.useroundedges==1;m=p?zb.chart3D:
zb.chart2D;G.events.click=c.plotOptions.series.point.events.click=n.linkClickFN=Aa(o,h);G.defaultSeriesType=y;h=r.palette>0&&r.palette<6?r.palette:b(this.paletteIndex,1);h-=1;G.paletteIndex=h;G.useRoundEdges=F&&!p&&!this.distributedColumns&&this.defaultSeriesType!=="pie";if(f(r.clickurl)!==void 0)G.link=r.clickurl,G.style.cursor="pointer",c.plotOptions.series.point.events.click=function(){G.events.click.call({link:r.clickurl})};var R=f(r.basefont,"Verdana"),M=sb(r.basefontsize,10),ua=f(r.basefontcolor,
l[m.baseFontColor][h]);A=f(r.outcnvbasefont,R);var T=sb(r.outcnvbasefontsize,M),ba=T+Ra,O=f(r.outcnvbasefontcolor,ua).replace(/^#?([a-f0-9]+)/ig,"#$1"),V;M+=Ra;ua=ua.replace(/^#?([a-f0-9]+)/ig,"#$1");n.trendStyle=n.outCanvasStyle={fontFamily:A,color:O,fontSize:ba};q=Oa(n.trendStyle);n.inCanvasStyle={fontFamily:R,fontSize:M,color:ua};V=Oa(n.inCanvasStyle);n.divlineStyle={fontFamily:R,fontSize:M,color:ua,lineHeight:V};J.labels.style={fontFamily:A,fontSize:ba,lineHeight:q,color:O};c.yAxis[0].labels.style=
{fontFamily:A,fontSize:ba,lineHeight:q,color:O};c.yAxis[1].labels.style={fontFamily:A,fontSize:ba,lineHeight:q,color:O};c.legend.itemStyle={fontFamily:A,fontSize:ba,lineHeight:q,color:O};c.plotOptions.series.dataLabels.style={fontFamily:R,fontSize:M,lineHeight:V,color:ua};c.plotOptions.series.dataLabels.color=c.plotOptions.series.dataLabels.style.color;c.tooltip.style={fontFamily:R,fontSize:M,lineHeight:V,color:ua};c.title.style={fontFamily:A,color:O,fontSize:T+3+Ra,fontWeight:"bold"};Oa(c.title.style);
c.subtitle.style={fontFamily:A,color:O,fontSize:T+1+Ra,fontWeight:"bold"};Oa(c.subtitle.style);J.title.style={fontFamily:A,color:O,fontSize:ba,fontWeight:"bold"};R=Oa(J.title.style);c.yAxis[0].title.style={fontFamily:A,color:O,fontSize:ba,lineHeight:R,fontWeight:"bold"};c.yAxis[1].title.style={fontFamily:A,color:O,fontSize:ba,lineHeight:R,fontWeight:"bold"};ba={};if(o.styles&&o.styles.definition instanceof Array&&o.styles.application instanceof Array){for(A=0;A<o.styles.definition.length;A+=1)O=o.styles.definition[A],
typeof pb[O.type.toLowerCase()]==="function"&&(ba[O.name.toLowerCase()]=O);for(A=0;A<o.styles.application.length;A+=1){O=o.styles.application[A].styles.split(ya);for(M=0;M<O.length;M+=1)if(R=O[M].toLowerCase(),ba[R])pb[ba[R].type.toLowerCase()](c,o.styles.application[A].toobject.toLowerCase(),ba[R])}}delete c.xAxis.labels.style.backgroundColor;delete c.xAxis.labels.style.borderColor;delete c.yAxis[0].labels.style.backgroundColor;delete c.yAxis[0].labels.style.borderColor;delete c.yAxis[1].labels.style.backgroundColor;
delete c.yAxis[1].labels.style.borderColor;n.showTooltip=b(r.showtooltip,1);n.tooltipSepChar=f(r.tooltipsepchar,lb);n.showValues=b(r.showvalues,this.showValues,1);n.seriesNameInToolTip=b(r.seriesnameintooltip,1);n.showVLineLabelBorder=b(r.showvlinelabelborder,1);n.rotateVLineLabels=b(r.rotatevlinelabels,0);n.vLineColor=f(r.vlinecolor,"333333");n.vLineThickness=f(r.vlinethickness,1);n.vLineAlpha=b(r.vlinealpha,80);n.vLineLabelBgColor=f(r.vlinelabelbgcolor,"ffffff");n.vLineLabelBgAlpha=b(r.vlinelabelbgalpha,
p?50:100);c.plotOptions.series.connectNullData=b(r.connectnulldata,0);G.backgroundColor={FCcolor:{color:f(r.bgcolor,l[m.bgColor][h]),alpha:f(r.bgalpha,l[m.bgAlpha][h]),angle:f(r.bgangle,l[m.bgAngle][h]),ratio:f(r.bgratio,l[m.bgRatio][h])}};G.rotateValues=b(r.rotatevalues,0);G.placeValuesInside=b(r.placevaluesinside,0);G.valuePosition=r.valueposition;G.valuePadding=b(r.valuepadding,2);G.borderColor=C(f(r.bordercolor,p?"#666666":l.borderColor[h]),f(r.borderalpha,p?"100":l.borderAlpha[h]));A=b(r.showborder,
p?0:1);G.borderWidth=A?b(r.borderthickness,1):0;G.plotBorderColor=C(f(r.canvasbordercolor,l.canvasBorderColor[h]),f(r.canvasborderalpha,l.canvasBorderAlpha[h]));Y=Boolean(f(r.canvasborderthickness,F?0:1));G.plotBorderWidth=p||!Y?0:b(r.canvasborderthickness,G.useRoundEdges?1:2);G.bgSWF=f(r.bgimage,r.bgswf);G.bgSWFAlpha=b(r.bgimagealpha,r.bgswfalpha,100);F=f(r.bgimagedisplaymode,"none").toLowerCase();A=t(r.bgimagevalign,w).toLowerCase();ba=t(r.bgimagehalign,w).toLowerCase();F=="tile"||F=="fill"||F==
"fit"?(A!=cb&&A!=Qa&&A!=jb&&(A=Qa),ba!=kb&&ba!=Qa&&ba!=Ta&&(ba=Qa)):(A!=cb&&A!=Qa&&A!=jb&&(A=cb),ba!=kb&&ba!=Qa&&ba!=Ta&&(ba=kb));G.bgImageDisplayMode=F;G.bgImageVAlign=A;G.bgImageHAlign=ba;G.bgImageScale=b(r.bgimagescale,100);G.logoURL=t(r.logourl);G.logoPosition=f(r.logoposition,"tl").toLowerCase();G.logoAlpha=b(r.logoalpha,100);G.logoLink=t(r.logolink);G.logoScale=b(r.logoscale,100);G.logoLeftMargin=b(r.logoleftmargin,0);G.logoTopMargin=b(r.logotopmargin,0);M=f(r.divlinecolor,l[m.divLineColor][h]);
R=f(r.divlinealpha,p?l.divLineAlpha3D[h]:l.divLineAlpha[h]);F=b(r.divlinethickness,1);A=Boolean(b(r.divlineisdashed,this.divLineIsDashed,0));ba=b(r.divlinedashlen,4);O=b(r.divlinedashgap,2);c.yAxis[0].gridLineColor=C(M,R);c.yAxis[0].gridLineWidth=F;c.yAxis[0].gridLineDashStyle=A?E(ba,O,F):void 0;c.yAxis[0].alternateGridColor=u?C(f(r.alternatevgridcolor,l.altVGridColor[h]),b(r.showalternatevgridcolor,1)===1?f(r.alternatevgridalpha,l.altVGridAlpha[h]):ja):C(f(r.alternatehgridcolor,l.altHGridColor[h]),
r.showalternatehgridcolor==0?0:f(r.alternatehgridalpha,l.altHGridAlpha[h]));ua=b(r.vdivlinethickness,1);T=Boolean(b(r.vdivlineisdashed,0));q=b(r.vdivlinedashlen,4);V=b(r.vdivlinedashgap,2);J.gridLineColor=C(f(r.vdivlinecolor,l[m.divLineColor][h]),f(r.vdivlinealpha,l.divLineAlpha[h]));J.gridLineWidth=ua;J.gridLineDashStyle=T?E(q,V,ua):void 0;J.alternateGridColor=C(f(r.alternatevgridcolor,l.altVGridColor[h]),r.showalternatehgridcolor==="1"?f(r.alternatevgridalpha,l.altVGridAlpha[h]):0);var ua=f(r.canvasbgcolor,
l[m.canvasBgColor][h]),ia,T=f(r.canvasbgalpha,l.canvasBgAlpha[h]);f(r.showcanvasbg,ub)==ja&&(T="0");c.plotOptions.series.shadow=b(r.showshadow,r.showcolumnshadow,this.defaultPlotShadow,l.showShadow[h]);if(this.inversed)c.yAxis[0].reversed=!0,c.yAxis[1].reversed=!0;if(this.isStacked)this.distributedColumns?(n.showStackTotal=Boolean(b(r.showsum,1)),q=b(r.usepercentdistribution,1),V=b(r.showpercentvalues,0),ia=b(r.showpercentintooltip,1),n.showXAxisPercentValues=b(r.showxaxispercentvalues,1)):(n.showStackTotal=
Boolean(b(this.showSum,r.showsum,0)),q=b(this.stack100percent,r.stack100percent,0),V=b(r.showpercentvalues,q),ia=b(r.showpercentintooltip,V)),n.showPercentValues=V,n.showPercentInToolTip=ia,q?(n.isValueAbs=!0,D[y].stacking="percent",n[0].stacking100Percent=!0):D[y].stacking="normal";if(this.isDual){if(r.primaryaxisonleft==="0")c.yAxis[0].opposite=!0,c.yAxis[1].opposite=!1;c.yAxis[0].showAlways=!0;c.yAxis[1].showAlways=!0}if(G.useRoundEdges){c.plotOptions.series.shadow=b(r.showshadow,r.showcolumnshadow,
1);c.plotOptions.series.borderRadius=1;c.tooltip.borderRadius=2;G.plotBorderRadius=3;if(!Y)G.plotBorderWidth=0;G.plotShadow=c.plotOptions.series.shadow?{enabled:!0,opacity:T/100}:0}c.plotOptions.series.maxColWidth=Math.abs(b(u?r.maxbarheight:r.maxcolwidth,50))||1;c.title.text=Q(r.caption);c.subtitle.text=Q(r.subcaption);if(r.showtooltip==ja)c.tooltip.enabled=!1;y=c.tooltip.style;c.tooltip.backgroundColor=C(f(y.backgroundColor,r.tooltipbgcolor,l.toolTipBgColor[h]),f(r.tooltipbgalpha,100));c.tooltip.borderColor=
C(f(y.borderColor,r.tooltipbordercolor,l.toolTipBorderColor[h]),f(r.tooltipborderalpha,100));y.backgroundColor=void 0;y.borderColor=void 0;y.border=void 0;c.tooltip.shadow=r.showtooltipshadow==ub?{enabled:!0,opacity:b(Fa(r.tooltipbgalpha,r.tooltipborderalpha),100)/100}:!1;c.tooltip.borderWidth=b(r.tooltipborderthickness,1);if(r.tooltipborderradius)c.tooltip.borderRadius=b(r.tooltipborderradius,1);c.tooltip.padding=b(r.tooltippadding,2);if(r.tooltipcolor)y.color=oa(r.tooltipcolor);y=b(r.plotspacepercent,
20);if(y>80||y<0)y=20;n.plotSpacePercent=c.plotOptions.series.groupPadding=y/200;p?(G.series2D3Dshift=g==="mscombi3d"?!0:Boolean(b(r.use3dlineshift,0)),G.canvasBaseColor3D=f(r.canvasbasecolor,l.canvasBaseColor3D[h]),G.canvasBaseDepth=b(r.canvasbasedepth,10),G.canvasBgDepth=b(r.canvasbgdepth,3),G.showCanvasBg=Boolean(b(r.showcanvasbg,1)),G.showCanvasBase=Boolean(b(r.showcanvasbase,1)),u?(G.xDepth=5,G.yDepth=5,G.showCanvasBg&&(n.marginTopExtraSpace+=G.canvasBgDepth),n.marginLeftExtraSpace+=G.yDepth+
(G.showCanvasBase?G.canvasBaseDepth:0),n.marginBottomExtraSpace+=5):(G.xDepth=10,G.yDepth=10,G.showCanvasBg&&(n.marginRightExtraSpace+=G.canvasBgDepth),n.marginBottomExtraSpace+=G.yDepth+(G.showCanvasBase?G.canvasBaseDepth:0)),ua=ua.split(ya)[0],T=T.split(ya)[0],G.use3DLighting=Boolean(b(r.use3dlighting,1)),G.plotBackgroundColor=G.use3DLighting?{FCcolor:{color:Hb(ua,85)+ya+Nb(ua,55),alpha:T+ya+T,ratio:xb,angle:L(e-(G.marginLeft+G.marginRight),j-(G.marginTop+G.marginBottom),1)}}:C(ua,T),G.canvasBgColor=
C(Hb(ua,80),T),u=f(r.zeroplanecolor,r.divlinecolor,l[m.divLineColor][h]),m=f(r.zeroplanealpha,r.divlinealpha,l.divLineAlpha[h]),G.zeroPlaneColor=C(u,m),G.zeroPlaneBorderColor=C(f(r.zeroplanebordercolor,u),b(r.zeroplaneshowborder,1)?m:0)):G.plotBackgroundColor={FCcolor:{color:ua,alpha:T,angle:f(r.canvasbgangle,l.canvasBgAngle[h]),ratio:f(r.canvasbgratio,l.canvasBgRatio[h])}};c.exporting.enabled=r.exportenabled=="1"?!0:!1;c.exporting.url=Boolean(b(r.exportatclient))?t(r.exporturl):c.exporting.url;c.exporting.buttons.exportButton.enabled=
r.exportshowmenuitem=="0"?!1:!0;c.exporting.filename=r.exportfilename?r.exportfilename:"FusionCharts";c.exporting.width=e;this.series(o,c,g,e,j);this.postSeriesAddition(c,o,e,j);this.spaceManager(c,o,e,j);g=b(r.drawquadrant,0);if(n.isXYPlot&&g&&(q=J.min,V=J.max,D=c.yAxis[0].min,ua=c.yAxis[0].max,Y=b(r.quadrantxval,(q+V)/2),T=b(r.quadrantyval,(D+ua)/2),T>=D&&T<=ua&&Y>=q&&Y<=V)){ia=C(f(r.quadrantlinecolor,G.plotBorderColor),f(r.quadrantlinealpha,eb));var fa=b(r.quadrantlinethickness,G.plotBorderWidth),
da=b(r.quadrantlineisdashed,0),na=b(r.quadrantlinedashLen,4),B=b(r.quadrantlinedashgap,2);m=t(r.quadrantlabeltl,w);g=t(r.quadrantlabeltr,w);o=t(r.quadrantlabelbl,w);u=t(r.quadrantlabelbr,w);y=b(r.quadrantlabelpadding,3);da=da?E(na,B,fa):void 0;J.plotLines.push({color:ia,value:Y,width:fa,dashStyle:da,zIndex:3});c.yAxis[0].plotLines.push({color:ia,value:T,width:fa,dashStyle:da,zIndex:3});e=e-G.marginRight-G.marginLeft;ia=j-G.marginTop-G.marginBottom;j=n.inCanvasStyle;fa=parseInt(j.fontSize,10);q=e/
(V-q)*(Y-q);V=e-q;ua=ia/(ua-D)*(T-D);D=ia-ua;q-=y;V-=y;D-=y;ua-=y;T=y+fa+Ra;Y=ia-y+fa+Ra;ia=y+Ra;e=e-y+Ra;v.setStyle(j);D>0&&(m!==w&&q>0&&(m=v.getSmartText(m,q,D),c.labels.items.push({html:m.text,zIndex:3,style:{left:ia,top:T,fontSize:j.fontSize,lineHeight:j.lineHeight,fontFamily:j.fontFamily,color:j.color}})),g!==w&&V>0&&(m=v.getSmartText(g,V,D),c.labels.items.push({html:m.text,textAlign:Ta,zIndex:3,style:{left:e,top:T,fontSize:j.fontSize,lineHeight:j.lineHeight,fontFamily:j.fontFamily,color:j.color}})));
ua>0&&(o!==w&&q>0&&(m=v.getSmartText(o,q,ua),c.labels.items.push({html:m.text,vAlign:jb,zIndex:3,style:{left:ia,top:Y,fontSize:j.fontSize,lineHeight:j.lineHeight,fontFamily:j.fontFamily,color:j.color}})),u!==w&&V>0&&(m=v.getSmartText(u,V,ua),c.labels.items.push({html:m.text,textAlign:Ta,vAlign:jb,zIndex:3,style:{left:e,top:Y,fontSize:j.fontSize,lineHeight:j.lineHeight,fontFamily:j.fontFamily,color:j.color}})))}if(this.hasVDivLine&&(j=b(r.numvdivlines,0)+1,j>1)){n=n.x.catCount-1;v=J.max;var j=n/j,
e=!0,g=J.min,N,M=f(r.vdivlinecolor,M),R=b(r.vdivlinealpha,R),ua=b(r.vdivlinethickness,F),T=b(r.vdivlineisdashed,A);q=b(r.vdivlinedashlen,ba);V=b(r.vdivlinedashgap,O);(F=b(r.showalternatevgridcolor,0))&&(N=C(f(r.alternatevgridcolor,l.altVGridColor[h]),f(r.alternateVGridAlpha,l.altVGridAlpha[h])));for(h=j;h<n;h+=j,e=!e)e&&F&&J.plotBands.push({color:N,from:g,to:h,zIndex:1}),J.plotLines.push({width:ua,color:C(M,R),dashStyle:T?E(q,V,ua):void 0,value:h,zIndex:1}),g=h;e&&F&&J.plotBands.push({color:N,from:g,
to:v,zIndex:1})}if(p&&G.xDepth>G.marginLeft)G.marginLeft=G.xDepth;delete c._FCconf;window.console&&window.console.log&&window.FC_DEV_ENVIRONMENT&&console.log(c);return c},defaultSeriesType:w,paletteIndex:1,creditLabel:!0,placeTitle:Lb,placeLegendBottom:$b,placeLegendRight:Ca,placeHorizontalAxis:Zb,placeVerticalAxis:ib,placeHorizontalCanvasMarginAdjustment:tb,placeVerticalCanvasMarginAdjustment:Wb,placeHorizontalXYSpaceManager:function(c,g,h,e){var j=c[K],l,p,t,m=g.chart,q,v,w,n,u=j.marginLeftExtraSpace,
r=j.marginTopExtraSpace,y=j.marginBottomExtraSpace,A=j.marginRightExtraSpace;t=h-(u+A+c.chart.marginRight+c.chart.marginLeft);var C=e-(y+c.chart.marginBottom+c.chart.marginTop),E=t*0.3,h=C*0.3;l=t-E;e=C-h;q=f(m.legendposition,jb).toLowerCase();c.legend.enabled&&q===Ta&&(l-=Ca(c,g,l/2,C));v=b(m.xaxisnamepadding,5);w=b(m.labelpadding,2);n=m.rotatexaxisname!==ja;p=j.x;p.verticalAxisNamePadding=v;p.verticalAxisValuesPadding=w;p.rotateVerticalAxisName=n;p.verticalAxisNameWidth=b(m.xaxisnamewidth);l-=ib(c.xAxis,
p,c,g,C,l,!1,!1,t);l-=tb(c,g,l,c.xAxis);t=l+E;c.legend.enabled&&q!==Ta&&(e-=$b(c,g,t,e/2));e-=Lb(c,g,t,e/2);p=j[0];p.horizontalAxisNamePadding=b(m.yaxisnamepadding,5);p.horizontalLabelPadding=b(m.yaxisvaluespadding,2);p.labelDisplay="auto";p.staggerLines=b(m.staggerlines,2);p.slantLabels=b(m.slantlabels,0);this.xAxisMinMaxSetter(c,g,t);e-=this.placeHorizontalAxis(c.yAxis[0],p,c,g,t,e,E);e-=Wb(c,g,e,c.yAxis[0]);Ab(h+e,c,m,c.xAxis,j.x.lYLblIdx,!0);Bb(c,c.xAxis.title,e);if(c.legend.enabled&&q===Ta){g=
c.legend;j=h+e;if(g.height>j)g.height=j,g.scroll.enabled=!0,j=(g.scroll.scrollBarWidth=10)+(g.scroll.scrollBarPadding=2),g.width+=j,c.chart.marginRight+=j;g.y=20}g=(c.chart.marginLeft-b(c.chart.spacingLeft,0)-(c.chart.marginRight-b(c.chart.spacingRight,0)))/2;j=c.chart.marginLeft-b(c.chart.spacingLeft,0);m=-(c.chart.marginRight-b(c.chart.spacingRight,0));switch(c.title.align){case "left":c.title.x=j;break;case "right":c.title.x=m;break;default:c.title.x=g}switch(c.subtitle.align){case "left":c.subtitle.x=
j;break;case "right":c.subtitle.x=m;break;default:c.subtitle.x=g}c.chart.marginLeft+=u;c.chart.marginTop+=r;c.chart.marginBottom+=y;c.chart.marginRight+=A},placeVerticalXYSpaceManager:function(c,g,h,e){var j=c[K],l,p=!0,t=g.chart,m=!1,q,v,w,n=j.marginLeftExtraSpace,u=j.marginTopExtraSpace,r=j.marginBottomExtraSpace,y=j.marginRightExtraSpace,A=h-(n+y+c.chart.marginRight+c.chart.marginLeft),C=e-(r+c.chart.marginBottom+c.chart.marginTop),h=A*0.3,e=C*0.3,E=A-h,A=C-e,G=f(t.legendposition,jb).toLowerCase();
c.legend.enabled&&G===Ta&&(E-=Ca(c,g,E/2,C));q=b(t.yaxisnamepadding,5);v=b(t.yaxisvaluespadding,2);w=t.rotateyaxisname!==ja;if(j.isDual)m=!0,l=j[1],l.verticalAxisNamePadding=q,l.verticalAxisValuesPadding=v,l.rotateVerticalAxisName=w,l.verticalAxisNameWidth=b(t.syaxisnamewidth),p=c.yAxis[1].opposite,E-=ib(c.yAxis[1],l,c,g,C,E/2,p,m);l=j[0];l.verticalAxisNamePadding=q;l.verticalAxisValuesPadding=v;l.rotateVerticalAxisName=w;l.verticalAxisNameWidth=b(m?t.pyaxisnamewidth:t.yaxisnamewidth);E-=ib(c.yAxis[0],
l,c,g,C,E,!p,m);E-=tb(c,g,E,c.yAxis[0],c.yAxis[1]);p=E+h;c.legend.enabled&&G!==Ta&&(A-=$b(c,g,p,A/2));A-=Lb(c,g,p,A/2);l=j.x;l.horizontalAxisNamePadding=b(t.xaxisnamepadding,5);l.horizontalLabelPadding=b(t.labelpadding,2);l.labelDisplay=t.rotatelabels=="1"?"rotate":f(t.labeldisplay,"auto").toLowerCase();l.staggerLines=b(t.staggerlines,2);l.slantLabels=b(t.slantlabels,t.slantlabel,0);this.xAxisMinMaxSetter(c,g,p);A-=this.placeHorizontalAxis(c.xAxis,l,c,g,p,A,h);A-=Wb(c,g,A,c.xAxis);m&&(Ab(e+A,c,t,
c.yAxis[1],j[1].lYLblIdx),Bb(c,c.yAxis[1].title,A));Ab(e+A,c,t,c.yAxis[0],j[0].lYLblIdx);Bb(c,c.yAxis[0].title,A);if(c.legend.enabled&&G===Ta){g=c.legend;j=e+A;if(g.height>j)g.height=j,g.scroll.enabled=!0,j=(g.scroll.scrollBarWidth=10)+(g.scroll.scrollBarPadding=2),g.width+=j,c.chart.marginRight+=j;g.y=20}g=(c.chart.marginLeft-b(c.chart.spacingLeft,0)-(c.chart.marginRight-b(c.chart.spacingRight,0)))/2;j=c.chart.marginLeft-b(c.chart.spacingLeft,0);t=-(c.chart.marginRight-b(c.chart.spacingRight,0));
switch(c.title.align){case "left":c.title.x=j;break;case "right":c.title.x=t;break;default:c.title.x=g}switch(c.subtitle.align){case "left":c.subtitle.x=j;break;case "right":c.subtitle.x=t;break;default:c.subtitle.x=g}c.chart.marginLeft+=n;c.chart.marginTop+=u;c.chart.marginBottom+=r;c.chart.marginRight+=y},placeVerticalAxisTitle:Bb,spaceManager:function(){return this.placeVerticalXYSpaceManager.apply(this,arguments)},xAxisMinMaxSetter:function(c,g,f){var e=c[K],j=e.x,h=g.chart,l=j.min=b(j.min,0),
p=j.max=b(j.max,j.catCount-1),m,q=0,t=0,w=c.chart.defaultSeriesType,n=/^(column|column3d|bar|bar3d|floatedcolumn)$/.test(w);/^(line|area|spline|areaspline)$/.test(w);var w=/^(scatter|bubble|candlestick)$/.test(w),u=c.xAxis,r=u.scroll,y=m=Math.min(b(h.canvaspadding,0),f/2-10);if(j.adjustMinMax){var p=l=!b(h.setadaptivexmin,1),A=b(this.numVDivLines,h.numvdivlines,4),C=h.adjustvdiv!==ja,E=b(h.showxaxisvalues,h.showxaxisvalue,1),G=b(h.showvlimits,E),E=b(h.showvdivlinevalue,h.showvdivlinevalues,E);Yb(u,
j,h.xaxismaxvalue,h.xaxisminvalue,l,p,A,C);l=u.min;p=u.max;j.requaredAutoNumeicLabels&&(A=b(parseInt(h.xaxisvaluesstep,10),1),Kb(h,c,u,j,G,E,A<1?1:A,e.numberFormatter,!1,!0));u.plotLines.sort($a)}u.labels.enabled=!1;u.gridLineWidth=0;u.alternateGridColor=J;if((n||e.isScroll)&&!e.hasNoColumn)t=e.isBar?q=0.55:q=0.5;e.is3d&&(y+=b(c.chart.xDepth,0));c=(f-(y+m))/(p-l+(q+t));u.min=l-(q+y/c);u.max=p+(t+m/c);if(r&&r.enabled)q=r.vxLength,t=r.startPercent,c=u.max-u.min,r.viewPortMin=u.min,r.viewPortMax=u.max,
r.scrollRatio=q/c,u.min+=(c-q)*t,u.max=u.min+q;w&&g.vtrendlines&&gb(g.vtrendlines,u,e,!1,!0,!0)},postSeriesAddition:function(b){var g=b[K],f=g.isBar,e=g.is3d,j=b.chart.rotateValues&&!f?270:0;if(this.isStacked&&g.showStackTotal){var h,l,p,m=1-g.plotSpacePercent,q,t,w,n,u,r,y,A,C,E=ia({},b.plotOptions.series.dataLabels.style),G=parseFloat(E.fontSize);l=g[0];var F=!l.stacking100Percent;E.color=b.plotOptions.series.dataLabels.color;p=l.stack;for(h in p){l=p[h].length;q=m/l;w=-(m-q)/2;for(t=0;t<l;t+=1,
w+=q){y=p[h][t];r=0;for(u=y.length;r<u;r+=1)A=y[r],n=r,C=n+w,n=(A.n||0)+(A.p||0),A=n<0?A.n:A.p,b.xAxis.plotLines.push({value:C,width:0,isVline:F,isTrend:!F,zIndex:4,label:{align:M,textAlign:!e&&j===270?n<0?Ta:kb:M,offsetScale:F?A:void 0,offsetScaleIndex:0,rotation:j,style:E,verticalAlign:cb,y:f?0:n<0?j===270?4:G:-4,x:0,text:g.numberFormatter.yAxis(n)}})}}}},dispose:function(){this.smartLabel&&this.smartLabel.dispose()}});V("stub",{standaloneInit:!0},V.base);V("barbase",{spaceManager:function(){return this.placeHorizontalXYSpaceManager.apply(this,
arguments)}},V.base);V("singleseries",{series:function(b,g,f){if(b.data&&b.data.length>0)g.legend.enabled=!1,f=this.point(f,{data:[],colorByPoint:!0},b.data,b.chart,g),f instanceof Array?g.series=g.series.concat(f):g.series.push(f),this.configureAxis(g,b),b.trendlines&&gb(b.trendlines,g.yAxis,g[K],!1,this.isBar)},defaultSeriesType:w,configureAxis:function(c,f){var h=c[K],e=f.chart,j,l,p,t,m,q,v,w,n,u,r,y,A;c.xAxis.title.text=Q(e.xaxisname);A=b(parseInt(e.yaxisvaluesstep,10),parseInt(e.yaxisvaluestep,
10),1);A=A<1?1:A;j=c.yAxis[0];l=h[0];h.isDual?(p=e.pyaxismaxvalue,t=e.pyaxisminvalue,j.title.text=Q(e.pyaxisname)):(p=e.yaxismaxvalue,t=e.yaxisminvalue,j.title.text=Q(e.yaxisname));v=b(this.isStacked?0:this.setAdaptiveYMin,e.setadaptiveymin,0);q=m=!v;w=b(h.numdivlines,e.numdivlines,4);n=e.adjustdiv!==ja;u=b(this.showYAxisValues,e.showyaxisvalues,e.showyaxisvalue,1);r=b(e.showlimits,u);y=b(e.showdivlinevalue,e.showdivlinevalues,u);Yb(j,l,p,t,m,q,w,n);Kb(e,c,j,l,r,y,A,h.numberFormatter,!1);if(j.reversed&&
j.min>=0)c.plotOptions.series.threshold=j.max;if(h.isDual)p=e.syaxismaxvalue,t=e.syaxisminvalue,v=b(e.setadaptivesymin,v),q=m=!v,r=b(e.showsecondarylimits,r),y=b(e.showdivlinesecondaryvalue,u),j=c.yAxis[1],l=h[1],Yb(j,l,p,t,m,q,w,n),Kb(e,c,j,l,r,y,A,h.numberFormatter,!0),j.title.text=Q(e.syaxisname)},pointValueWatcher:function(c,g,h,e,j,l,p){if(g!==null){c=c[K];h=b(h,0);c[h]||(c[h]={});h=c[h];if(e)this.distributedColumns&&(c.marimekkoTotal+=g),e=h.stack,j=b(j,0),l=b(l,0),p=f(p,yb),e[p]||(e[p]=[]),
p=e[p],p[l]||(p[l]=[]),l=p[l],l[j]||(l[j]={}),j=l[j],g>=0?j.p?g=j.p+=g:j.p=g:j.n?g=j.n+=g:j.n=g;h.max=h.max>g?h.max:g;h.min=h.min<g?h.min:g}},getPointStub:function(c,g,h,e){var e=e[K],g=g===null?g:e.numberFormatter.dataLabels(g),j=t(Q(c.tooltext)),l=t(Q(c.displayvalue)),h=e.showTooltip?j!==void 0?j:g===null?!1:h!==w?h+e.tooltipSepChar+g:g:w,e=b(c.showvalue,e.showValues)?l!==void 0?l:g:w,c=f(c.link);return{displayValue:e,toolText:h,link:c}}},V.base);V("multiseries",{series:function(c,f,h){var e,j,
l=f[K],p;f.legend.enabled=Boolean(b(c.chart.showlegend,1));if(c.dataset&&c.dataset.length>0){this.categoryAdder(c,f);e=0;for(j=c.dataset.length;e<j;e+=1)p={data:[]},p=this.point(h,p,c.dataset[e],c.chart,f,l.oriCatTmp.length,e),p instanceof Array?f.series=f.series.concat(p):f.series.push(p);this.configureAxis(f,c);c.trendlines&&!this.isLog&&gb(c.trendlines,f.yAxis,l,!1,this.isBar)}},categoryAdder:function(c,f){var o,e=0,j=f[K],l=j.axisGridManager,p=c.chart,t=f.xAxis,m,j=j.x;if(c.categories&&c.categories[0]&&
c.categories[0].category){if(c.categories[0].font)f.xAxis.labels.style.fontFamily=c.categories[0].font;if((o=b(c.categories[0].fontsize))!==void 0)o<1&&(o=1),f.xAxis.labels.style.fontSize=o+Ra,Oa(f.xAxis.labels.style);if(c.categories[0].fontcolor)f.xAxis.labels.style.color=c.categories[0].fontcolor.split(ya)[0].replace(/^\#?/,"#");var q=f[K].oriCatTmp,v=c.categories[0].category;for(o=0;o<v.length;o+=1)v[o].vline?l.addVline(t,v[o],e,f):(m=(m=b(v[o].showlabel,p.showlabels,1))?Q(h(c.categories[0].category[o].label,
c.categories[0].category[o].name)):w,l.addXaxisCat(t,e,e,m),q[e]=h(Q(c.categories[0].category[o].tooltext),m),e+=1)}j.catCount=e},getPointStub:function(c,g,o,e,j,l,p){var u,e=e[K],m,q,g=g===null?g:e.numberFormatter.dataLabels(g,p===1?!0:!1),v,y=t(Q(c.tooltext)),p=e.tooltipSepChar;e.showTooltip?y!==void 0?j=y:g===null?j=!1:(e.seriesNameInToolTip&&(v=h(j&&j.seriesname)),j=v?v+p:w,j+=o?o+p:w,e.showPercentInToolTip?q=!0:j+=g):j=!1;b(c.showvalue,l)?t(c.displayvalue)!==void 0?u=c.displayvalue:e.showPercentValues?
m=!0:u=g:u=w;c=f(c.link);return{displayValue:u,toolText:j,link:c,showPercentValues:m,showPercentInToolTip:q}}},V.singleseries);var Ib=function(b,f){return b-f};V("xybase",{hideRLine:function(){var b=this.chart.series[this.index+1];b&&b.hide&&b.hide()},showRLine:function(){var b=this.chart.series[this.index+1];b&&b.show&&b.show()},getRegressionLineSeries:function(b,f,h){var e,j,l,p;p=b.sumXY;var t=b.sumX,m=b.sumY;j=b.xValues;l=b.sumXsqure;e=b.yValues;b=b.sumYsqure;f?(j.sort(Ib),e=j[0],j=j[j.length-
1],p=(h*p-t*m)/(h*l-Math.pow(t,2)),l=!isNaN(p)?p*(e-t/h)+m/h:m/h,h=!isNaN(p)?p*(j-t/h)+m/h:m/h,h=[{x:e,y:l},{x:j,y:h}]):(e.sort(Ib),l=e[0],e=e[e.length-1],p=(h*p-t*m)/(h*b-Math.pow(m,2)),j=!isNaN(p)?p*(l-m/h)+t/h:t/h,h=!isNaN(p)?p*(e-m/h)+t/h:t/h,h=[{x:j,y:l},{x:h,y:e}]);return h},pointValueWatcher:function(b,f,h,e){var j=b[K];if(f!==null)b=j[0],b.max=b.max>f?b.max:f,b.min=b.min<f?b.min:f;if(h!==null)b=j.x,b.max=b.max>h?b.max:h,b.min=b.min<h?b.min:h;e&&(h=h||0,f=f||0,e.sumX+=h,e.sumY+=f,e.sumXY+=
h*f,e.sumXsqure+=Math.pow(h,2),e.xValues.push(h),e.sumYsqure+=Math.pow(f,2),e.yValues.push(f))}},V.multiseries);V("scrollbase",{postSeriesAddition:function(c,g){c.chart.hasScroll=!0;var h=c.xAxis.scroll,e=c[K],j=e.width,p=e.x.catCount,t=g.chart;e.isScroll=!0;if(this.isStacked)u=1;else{var u=0,m=0,q,v=c.series,w,n=c.chart.defaultSeriesType;for(q=v.length;m<q;m++)w=f(v[m].type,n),w==="column"&&(u+=1);u<1&&(u=1)}p*=u;j=b(t.numvisibleplot,Math.floor(j/this.avgScrollPointWidth));if(h&&j>=2&&j<p)h.enabled=
!0,h.vxLength=j/u,h.startPercent=t.scrolltoend===ub?1:0,h.padding=b(t.scrollpadding,c.chart.plotBorderWidth/2),h.height=b(t.scrollheight,16),h.buttonWidth=b(t.scrollbtnwidth,t.scrollheight,16),h.buttonPadding=b(t.scrollbtnpadding,0),h.color=oa(f(t.scrollcolor,l.altHGridColor[c.chart.paletteIndex])),e.marginBottomExtraSpace+=h.padding+h.height}},V.multiseries);var Zb=V.singleseries,Va=V.multiseries;V("column2dbase",{point:function(c,g,o,e,j){var p,t,u,m,q,v,y,n,A,r,c=o.length;p=j[K];var C=p.axisGridManager,
F=j.xAxis,J=j.chart.paletteIndex,L=p.x,G=j.colors,M=j.colors.length,O=/3d$/.test(j.chart.defaultSeriesType),V=this.isBar,T=f(e.showplotborder,O?ja:ub)===ub?O?1:b(e.plotborderthickness,1):0,ba=j.chart.useRoundEdges,U=b(e.plotborderalpha,e.plotfillalpha,100),X=f(e.plotbordercolor,l.plotBorderColor[J]).split(ya)[0],fa=0,oa=Boolean(b(e.use3dlighting,1)),da=j[K].numberFormatter,na=b(e.plotborderdashed,0),B=b(e.plotborderdashlen,5),N=b(e.plotborderdashgap,4);for(u=t=0;t<c;t+=1)n=o[t],n.vline?C.addVline(F,
n,fa,j):(p=da.getCleanValue(n.value),m=b(n.showlabel,e.showlabels,1),m=Q(!m?w:h(n.label,n.name)),C.addXaxisCat(F,fa,fa,m),fa+=1,q=f(n.color,G[u%M])+ya+R(e.plotgradientcolor,l.plotGradientColor[J]),v=f(n.alpha,e.plotfillalpha,eb),y=f(n.ratio,e.plotfillratio),A=f(360-e.plotfillangle,90),p<0&&(A=360-A),r={opacity:v/100,inverted:V},q=sa(q,v,y,A,ba,X,f(n.alpha,U)+w,V,O),v=b(n.dashed,na)?E(f(n.dashlen,B),f(n.dashgap,N),T):void 0,g.data.push(ia(this.getPointStub(n,p,m,j),{y:p,shadow:r,color:q[0],borderColor:q[1],
borderWidth:T,use3DLighting:oa,dashStyle:v})),this.pointValueWatcher(j,p),u+=1);L.catCount=fa;return g},defaultSeriesType:"column"},Zb);V("linebase",{defaultSeriesType:"line",hasVDivLine:!0,defaultPlotShadow:1,point:function(c,g,o,e,j){var p,t,u,m,q,v,y,n,A,r,C,F,J,L,G,R,M,O,T,ba,V,U,X,ja,c=o.length,da=j.xAxis;p=j[K];var fa=p.axisGridManager,B=0,N=p.x,na=j.chart.paletteIndex,ka=j[K].numberFormatter;G=oa(f(e.linecolor,e.palettecolors,l.plotFillColor[na]));R=f(e.linealpha,eb);F=b(e.linethickness,4);
J=Boolean(b(e.linedashed,0));n=b(e.linedashlen,5);A=b(e.linedashgap,4);g.color={FCcolor:{color:G,alpha:R}};g.lineWidth=F;g.step=this.stepLine;g.drawVerticalJoins=Boolean(b(e.drawverticaljoins,1));g.kagi=this.isKagi;L=b(e.drawanchors,e.showanchors);for(q=t=0;t<c;t+=1)m=o[t],m.vline?fa.addVline(da,m,B,j):(p=ka.getCleanValue(m.value),u=b(m.showlabel,e.showlabels,1),u=Q(!u?w:h(m.label,m.name)),fa.addXaxisCat(da,B,B,u),B+=1,r=oa(f(m.color,G)),C=f(m.alpha,R),v=f(m.dashed,J)?E(n,A,F):void 0,y={opacity:C/
100},O=b(m.anchorsides,e.anchorsides,0),V=b(m.anchorradius,e.anchorradius,3),ba=oa(f(m.anchorbordercolor,e.anchorbordercolor,G)),T=b(m.anchorborderthickness,e.anchorborderthickness,1),U=oa(f(m.anchorbgcolor,e.anchorbgcolor,l.anchorBgColor[na])),X=f(m.anchoralpha,e.anchoralpha,eb),ja=f(m.anchorbgalpha,e.anchorbgalpha,X),M=L===void 0?C!=0:!!L,g.data.push(ia(this.getPointStub(m,p,u,j),{y:p,color:{FCcolor:{color:r,alpha:C}},shadow:y,dashStyle:v,marker:{enabled:!!M,fillColor:{FCcolor:{color:U,alpha:ja*
X/100+w}},lineColor:{FCcolor:{color:ba,alpha:X}},lineWidth:T,radius:V,symbol:Cb(O)}})),this.pointValueWatcher(j,p),q+=1);N.catCount=B;return g}},Zb);V("area2dbase",{defaultSeriesType:"area",hasVDivLine:!0,point:function(c,g,o,e,j){var p,u,y,m,q,v,A,n,D,r,C,F,J,L,G,M,O,V,T,ba,U,X,c=o.length,fa=j.xAxis;m=j[K];var na=j.chart.paletteIndex,da=m.axisGridManager,sa=m.x,B=0,N=j[K].numberFormatter;m=f(e.plotfillcolor,e.areabgcolor,t(e.palettecolors)?j.colors[0]:l.plotFillColor[na]).split(ya)[0];U=ya+R(e.plotgradientcolor,
l.plotGradientColor[na]);q=f(e.plotfillalpha,e.areaalpha,"90");v=b(e.plotfillangle,270);A=f(e.plotbordercolor,e.areabordercolor,t(e.palettecolors)?j.colors[0]:l.plotBorderColor[na]).split(ya)[0];n=e.showplotborder==ja?ja:f(e.plotborderalpha,e.plotfillalpha,e.areaalpha,eb);p=b(e.plotborderangle,270);u=Boolean(b(e.plotborderdashed,0));F=b(e.plotborderdashlen,5);M=b(e.plotborderdashgap,4);O=b(e.plotborderthickness,e.areaborderthickness,1);X=g.fillColor={FCcolor:{color:m+U,alpha:q,ratio:xb,angle:v}};
g.lineWidth=O;g.dashStyle=u?E(F,M,O):void 0;g.lineColor={FCcolor:{color:A,alpha:n,ratio:eb,angle:p}};M=Boolean(b(e.drawanchors,e.showanchors,1));for(O=u=0;u<c;u+=1)F=o[u],F.vline?da.addVline(fa,F,B,j):(p=N.getCleanValue(F.value),y=b(F.showlabel,e.showlabels,1),y=Q(!y?w:h(F.label,F.name)),da.addXaxisCat(fa,B,B,y),B+=1,D=b(F.anchorsides,e.anchorsides,0),r=b(F.anchorradius,e.anchorradius,3),C=oa(f(F.anchorbordercolor,e.anchorbordercolor,A)),V=b(F.anchorborderthickness,e.anchorborderthickness,1),J=oa(f(F.anchorbgcolor,
e.anchorbgcolor,l.anchorBgColor[na])),L=f(F.anchoralpha,e.anchoralpha,this.anchorAlpha,ja),G=f(F.anchorbgalpha,e.anchorbgalpha,L),T=t(F.color),ba=b(F.alpha),T=T!==void 0||ba!==void 0?{FCcolor:{color:T?oa(T)+U:m,alpha:void 0===ba?Gb(ba)+w:q,ratio:xb,angle:v}}:X,ba={opacity:Math.max(ba,n)/100,inverted:!0},g.data.push(ia(this.getPointStub(F,p,y,j),{y:p,shadow:ba,color:T,marker:{enabled:M,fillColor:{FCcolor:{color:J,alpha:G*L/100+w}},lineColor:{FCcolor:{color:C,alpha:L}},lineWidth:V,radius:r,symbol:Cb(D)}})),
this.pointValueWatcher(j,p),O+=1);sa.catCount=B;return g}},Zb);V("mscolumn2dbase",{point:function(c,g,h,e,j,p,u,y){c=!1;if(h.data){var m,q,v,A,n,D,r,C,F,J,L,G,M,O,Q,T,ba=h.data,V=j[K],U=f(g.type,this.defaultSeriesType),X=j.plotOptions[U]&&j.plotOptions[U].stacking,fa=f(this.isValueAbs,V.isValueAbs,!1),da=b(h.showvalues,V.showValues),oa=b(g.yAxis,0),B=b(e.use3dlighting,1),N=j[K].numberFormatter,na=j.chart.paletteIndex,ka=b(h.dashed,e.plotborderdashed,0),ha=b(h.dashlen,e.plotborderdashlen,5),va=b(h.dashgap,
e.plotborderdashgap,4);g.name=t(h.seriesname);if(b(h.includeinlegend)===0||g.name===void 0)g.showInLegend=!1;g.color=f(h.color,j.colors[u%j.colors.length]).split(ya)[0].replace(/^#?/g,"#");C=f(e.plotborderthickness,ub);F=j.chart.useRoundEdges;J=this.isBar;L=/3d$/.test(j.chart.defaultSeriesType);G=f(e.plotbordercolor,l.plotBorderColor[na]).split(ya)[0];M=e.showplotborder==ja?ja:f(e.plotborderalpha,eb);M=L?e.showplotborder?M:ja:M;G=L?f(e.plotbordercolor,"#FFFFFF"):G;for(q=0;q<p;q+=1)(n=ba[q])?(m=N.getCleanValue(n.value,
fa),m===null?g.data.push({y:null}):(c=!0,O=V.oriCatTmp[q],v=f(n.color,h.color,j.colors[u%j.colors.length])+ya+R(e.plotgradientcolor,l.plotGradientColor[na]),A=f(n.alpha,h.alpha,e.plotfillalpha,eb),D=f(n.ratio,h.ratio,e.plotfillratio),r=f(360-e.plotfillangle,90),m<0&&(r=360-r),Q={opacity:A/100},T=Math.min(A,M)+w,v=sa(v,A,D,r,F,G,T,J,L),A=b(n.dashed,ka)?E(f(n.dashlen,ha),f(n.dashgap,va),C):void 0,g.data.push(ia(this.getPointStub(n,m,O,j,h,da,oa),{y:m,shadow:Q,color:v[0],borderColor:v[1],borderWidth:C,
use3DLighting:B,dashStyle:A})),this.pointValueWatcher(j,m,oa,X,q,y,U))):g.data.push({y:null})}if(!c)g.showInLegend=!1;return g},defaultSeriesType:"column"},Va);V("mslinebase",{hasVDivLine:!0,point:function(c,g,h,e,j,p,u){c=!1;if(h.data){var y,m,q,v,A,n,C,r,F,J,L,M,G,O,R,Q,T,ba,V,U,X,fa,da,ja,B,N,na,ka,ha=h.data,sa=j[K],va=f(g.type,this.defaultSeriesType),la=j.plotOptions[va]&&j.plotOptions[va].stacking,ea=f(this.isValueAbs,sa.isValueAbs,!1),Z=b(h.showvalues,sa.showValues),ya=b(g.yAxis,0),pa=sa.numberFormatter;
g.name=t(h.seriesname);N=oa(f(h.color,e.linecolor,j.colors[u%j.colors.length]));B=f(h.alpha,e.linealpha,eb);u=b(h.linethickness,e.linethickness,2);v=Boolean(b(h.dashed,e.linedashed,0));A=b(h.linedashlen,e.linedashlen,5);n=b(h.linedashgap,e.linedashgap,4);g.color={FCcolor:{color:N,alpha:B}};g.step=this.stepLine;g.drawVerticalJoins=Boolean(b(e.drawverticaljoins,1));g.lineWidth=u;C=b(h.drawanchors,h.showanchors,e.drawanchors,e.showanchors);ba=b(h.anchorsides,e.anchorsides,0);V=b(h.anchorradius,e.anchorradius,
3);U=oa(f(h.anchorbordercolor,e.anchorbordercolor,N));X=b(h.anchorborderthickness,e.anchorborderthickness,1);fa=oa(f(h.anchorbgcolor,e.anchorbgcolor,l.anchorBgColor[j.chart.paletteIndex]));da=f(h.anchoralpha,e.anchoralpha,eb);ja=f(h.anchorbgalpha,e.anchorbgalpha,da);if(b(h.includeinlegend)===0||g.name===void 0||B==0&&C!==1)g.showInLegend=!1;g.marker={fillColor:{FCcolor:{color:fa,alpha:ja*da/100+w}},lineColor:{FCcolor:{color:U,alpha:da+w}},lineWidth:X,radius:V,symbol:Cb(ba)};for(y=0;y<p;y+=1)(Q=ha[y])?
(e=pa.getCleanValue(Q.value,ea),e===null?g.data.push({y:null}):(c=!0,O=b(Q.anchorsides,ba),G=b(Q.anchorradius,V),L=oa(f(Q.anchorbordercolor,U)),M=b(Q.anchorborderthickness,X),J=oa(f(Q.anchorbgcolor,fa)),r=f(Q.anchoralpha,da),F=f(Q.anchorbgalpha,ja),m=oa(f(Q.color,N)),q=f(Q.alpha,B),ka=b(Q.dashed,v)?E(A,n,u):void 0,T={opacity:q/100},na=C===void 0?q!=0:!!C,R=sa.oriCatTmp[y],g.data.push(ia(this.getPointStub(Q,e,R,j,h,Z,ya),{y:e,shadow:T,dashStyle:ka,color:{FCcolor:{color:m,alpha:q}},marker:{enabled:na,
fillColor:{FCcolor:{color:J,alpha:F*r/100+w}},lineColor:{FCcolor:{color:L,alpha:r}},lineWidth:M,radius:G,symbol:Cb(O)}})),this.pointValueWatcher(j,e,ya,la,y,0,va))):g.data.push({y:null})}if(!c)g.showInLegend=!1;return g},defaultSeriesType:"line",defaultPlotShadow:1},Va);V("msareabase",{hasVDivLine:!0,point:function(c,g,h,e,j,p,t){c=!1;if(h.data){var u,m,q,v,y,n,A,r,C,F,J,L,G,M,Q,O,T,V,U,X,fa,na,da,sa=h.data,B=j[K],N=f(g.type,this.defaultSeriesType),va=j.plotOptions[N]&&j.plotOptions[N].stacking,ka=
f(this.isValueAbs,B.isValueAbs,!1),ha=j._FCconf.linkClickFN;U=j.chart.paletteIndex;var Aa=b(h.showvalues,B.showValues),za=b(g.yAxis,0),la=j[K].numberFormatter;g.name=f(h.seriesname,w);if(b(h.includeinlegend)===0||g.name===void 0)g.showInLegend=!1;v=f(h.color,j.colors[t%j.colors.length]).split(ya)[0].replace(/^#?/g,"#").split(ya)[0];this.isRadar||(v+=ya+R(e.plotgradientcolor,l.plotGradientColor[U]));y=f(h.alpha,e.plotfillalpha,e.areaalpha,this.isRadar?"50":"70");n=b(e.plotfillangle,270);A=f(h.plotbordercolor,
e.plotbordercolor,e.areabordercolor,this.isRadar?j.colors[t%j.colors.length]:l.plotFillColor[j.chart.paletteIndex]).split(ya)[0];t=f(h.showplotborder,e.showplotborder)==ja?ja:f(h.plotborderalpha,e.plotborderalpha,h.plotfillalpha,e.plotfillalpha,"95");r=b(e.plotborderangle,270);O=Boolean(b(h.dashed,e.plotborderdashed,0));T=b(h.dashlen,e.plotborderdashlen,5);V=b(h.dashgap,e.plotborderdashgap,4);u=f(h.plotborderthickness,e.plotborderthickness,1);g.fillColor={FCcolor:{color:v,alpha:y,ratio:xb,angle:n}};
g.color=v;g.lineColor={FCcolor:{color:A,alpha:t,ratio:eb,angle:r}};g.lineWidth=u;g.dashStyle=O?E(T,V,u):void 0;n=Boolean(b(e.drawanchors,e.showanchors,1));A=b(h.anchorsides,e.anchorsides,0);r=b(h.anchorradius,e.anchorradius,3);O=oa(f(h.anchorbordercolor,e.anchorbordercolor,v));T=b(h.anchorborderthickness,e.anchorborderthickness,1);U=oa(f(h.anchorbgcolor,e.anchorbgcolor,l.anchorBgColor[U]));V=b(h.anchoralpha,e.anchoralpha,this.anchorAlpha,0);X=b(h.anchorbgalpha,e.anchorbgalpha,V);g.marker={fillColor:{FCcolor:{color:U,
alpha:X*V/100+w}},lineColor:{FCcolor:{color:O,alpha:V+w}},lineWidth:T,radius:r,symbol:Cb(A)};for(u=0;u<p;u+=1)(q=sa[u])?(e=q?la.getCleanValue(q.value,ka):null,e===null?g.data.push({y:null}):(c=!0,m=B.oriCatTmp[u],C=b(q.anchorsides,A),F=b(q.anchorradius,r),J=oa(f(q.anchorbordercolor,O)),L=b(q.anchorborderthickness,T),G=oa(f(q.anchorbgcolor,U)),M=f(q.anchoralpha,V),Q=f(q.anchorbgalpha,X),fa=f(q.color,v),na=f(q.alpha,y),da={opacity:Math.max(na,t)/100,inverted:!0},g.data.push(ia(this.getPointStub(q,e,
m,j,h,Aa,za),{y:e,shadow:da,color:{FCcolor:{color:fa,alpha:na}},marker:{enabled:n,fillColor:{FCcolor:{color:G,alpha:Q*M/100+w}},lineColor:{FCcolor:{color:J,alpha:M+w}},lineWidth:L,radius:F,symbol:Cb(C)},events:{click:ha}})),this.pointValueWatcher(j,e,za,va,u,0,N))):g.data.push({y:null})}if(!c)g.showInLegend=!1;return g},defaultSeriesType:"area",defaultPlotShadow:0},Va);V("scatterbase",{showValues:0,defaultPlotShadow:0,point:function(c,g,h,e,j,p,u){if(h.data){var y,m,q,v,A,n,D,r,F,J,L,M,G,O,Q,R,T=
!1,U,c=V[c];q=b(h.drawline,0);v=b(h.drawprogressioncurve,0);var X=j[K],p=h.data,fa=p.length,ia=b(h.showvalues,X.showValues),X=X.numberFormatter,ja=b(h.showregressionline,e.showregressionline,0);g.zIndex=1;g.name=t(h.seriesname);if(b(h.includeinlegend)===0||g.name===void 0)g.showInLegend=!1;if(q||v){if(v)g.type="spline";m=oa(f(h.color,e.linecolor,j.colors[u%j.colors.length]));q=f(h.alpha,e.linealpha,eb);v=b(h.linethickness,e.linethickness,2);A=Boolean(b(h.dashed,e.linedashed,0));n=b(h.linedashlen,
e.linedashlen,5);D=b(h.linedashgap,e.linedashgap,4);g.color={FCcolor:{color:m,alpha:q}};g.lineWidth=v;g.dashStyle=A?E(n,D,v):void 0}q=Boolean(b(h.drawanchors,h.showanchors,e.drawanchors,e.showanchors,1));v=b(h.anchorsides,e.anchorsides,u+3);A=b(h.anchorradius,e.anchorradius,3);u=oa(f(h.anchorbordercolor,h.color,e.anchorbordercolor,m,j.colors[u%j.colors.length]));m=b(h.anchorborderthickness,e.anchorborderthickness,1);n=oa(f(h.anchorbgcolor,e.anchorbgcolor,l.anchorBgColor[j.chart.paletteIndex]));D=
f(h.anchoralpha,e.anchoralpha,eb);F=f(h.anchorbgalpha,e.anchorbgalpha,D);g.marker={fillColor:this.getPointColor(n,eb),lineColor:{FCcolor:{color:u,alpha:D+w}},lineWidth:m,radius:A,symbol:Cb(v)};if(ja){g.events={hide:this.hideRLine,show:this.showRLine};var da={sumX:0,sumY:0,sumXY:0,sumXsqure:0,sumYsqure:0,xValues:[],yValues:[]},na=b(h.showyonx,e.showyonx,1),B=oa(f(h.regressionlinecolor,e.regressionlinecolor,u)),N=b(h.regressionlinethickness,e.regressionlinethickness,m),e=Gb(b(h.regressionlinealpha,
e.regressionlinealpha,D)),B=C(B,e)}for(y=0;y<fa;y+=1)(r=p[y])?(e=X.getCleanValue(r.y),R=X.getCleanValue(r.x),e===null?g.data.push({y:null,x:R}):(T=!0,U=c.getPointStub(r,e,X.xAxis(R),j,h,ia),J=b(r.anchorsides,v),L=b(r.anchorradius,A),M=oa(f(r.anchorbordercolor,u)),G=b(r.anchorborderthickness,m),O=oa(f(r.anchorbgcolor,n)),Q=f(r.anchoralpha,D),r=f(r.anchorbgalpha,F),g.data.push({y:e,x:R,displayValue:U.displayValue,toolText:U.toolText,link:U.link,marker:{enabled:q,fillColor:{FCcolor:{color:O,alpha:r*
Q/100+w}},lineColor:{FCcolor:{color:M,alpha:Q}},lineWidth:G,radius:L,symbol:Cb(J)}}),this.pointValueWatcher(j,e,R,ja&&da))):g.data.push({y:null});ja&&(h=this.getRegressionLineSeries(da,na,fa),this.pointValueWatcher(j,h[0].y,h[0].x),this.pointValueWatcher(j,h[1].y,h[1].x),j={type:"line",color:B,showInLegend:!1,lineWidth:N,enableMouseTracking:!1,marker:{enabled:!1},data:h,zIndex:0},g=[g,j])}if(!T)g.showInLegend=!1;return g},categoryAdder:function(c,g){var o,e=0,j,p=g[K].x,t,u=g.xAxis,m,q;q=c.chart;
var v=parseInt(q.labelstep,10),y=b(q.showlabels,1),n=f(q.xaxislabelmode,"categories").toLowerCase(),A=g[K].numberFormatter;g._FCconf.isXYPlot=!0;v=v>1?v:1;p.catOccupied={};if(n!=="auto"&&c.categories&&c.categories[0]&&c.categories[0].category){q=c.categories[0];if(q.font)g.xAxis.labels.style.fontFamily=q.font;if((j=b(q.fontsize))!==void 0)j<1&&(j=1),g.xAxis.labels.style.fontSize=j+Ra,Oa(g.xAxis.labels.style);if(q.fontcolor)g.xAxis.labels.style.color=q.fontcolor.split(ya)[0].replace(/^\#?/,"#");o=
f(q.verticallinecolor,l.divLineColor[g.chart.paletteIndex]);j=b(q.verticallinethickness,1);t=b(q.verticallinealpha,l.divLineAlpha[g.chart.paletteIndex]);var r=b(q.verticallinedashed,0),F=b(q.verticallinedashlen,4),J=b(q.verticallinedashgap,2),L=C(o,t),O,G,R;for(o=0;o<q.category.length;o+=1)m=q.category[o],t=A.getCleanValue(m.x),t!==null&&!m.vline&&(p.catOccupied[t]=!0,R=b(m.showlabel,m.showname,y),O=b(m.showverticalline,m.showline,m.sl,0),G=b(m.linedashed,r),m=R===0||e%v!==0?w:Q(h(m.label,m.name)),
u.plotLines.push({isGrid:!0,isCat:!0,width:O?j:0,color:L,dashStyle:E(F,J,j,G),value:t,label:{text:m,style:u.labels.style,align:M,verticalAlign:jb,textAlign:M,rotation:0,x:0,y:0}}),this.pointValueWatcher(g,null,t),e+=1);if(n==="mixed")p.requaredAutoNumeicLabels=b(this.requaredAutoNumeicLabels,1)}else p.requaredAutoNumeicLabels=b(this.requaredAutoNumeicLabels,1);p.adjustMinMax=!0},getPointColor:function(b,f){var h,e,b=oa(b),f=Gb(f);h=Nb(b,70);e=Hb(b,50);return{FCcolor:{gradientUnits:"objectBoundingBox",
cx:0.4,cy:0.4,r:"100%",color:h+ya+e,alpha:f+ya+f,ratio:xb,radialGradient:!0}}},defaultSeriesType:"scatter"},V.xybase)}})();
