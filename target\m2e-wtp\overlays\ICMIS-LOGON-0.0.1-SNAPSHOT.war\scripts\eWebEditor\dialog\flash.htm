<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj="INSERT";var ak=lang["DlgComInsert"];var D;var aI="http://";var au="200";var at="200";var bt="";var cJ="";var cL="";var fm="true";var fl="true";var aS="";var jQ="";var jW="high";var jD="";var jr="";var iZ="";var cX="file";var bz=((parseFloat(config.AllowFlashSize)>0)?true:false);pw();var Q=lang["DlgFlash"]+"("+ak+")";document.write("<title>"+Q+"</title>");function pw(){if(I.ay()!="Control"){return;}D=I.aX();if(D.tagName=="IMG"&&rX(D)=="flash"){aj="MODI";}if(aj!="MODI"){return;}ak=lang["DlgComModify"];cX="url";bt=bU(D,"align");au=jV(dE(D,"width"));at=jV(dE(D,"height"));cJ=bU(D,"vspace");cL=bU(D,"hspace");bg.Init(rZ(D));if(bg.ka=="object"){aI=bg.cl("movie");}else{aI=bg.cl("src");}fm=bg.cl("loop");if(fm=="-1"||fm=="1"){fm="true";}else if(fm=="0"){fm="false";}fl=bg.cl("play");if(fl=="-1"||fl=="1"){fl="true";}else if(fl=="0"){fl="false";}aS=bg.cl("bgcolor");jQ=bg.cl("scale");jW=bg.cl("quality");jD=bg.cl("menu");jr=bg.cl("wmode");iZ=bg.cl("flashvars");};function ah(){lang.TranslatePage(document);bb($("d_align"),bt.toLowerCase());bb($("d_loop"),fm.toLowerCase());bb($("d_play"),fl.toLowerCase());bb($("d_scale"),jQ.toLowerCase());bb($("d_quality"),jW.toLowerCase());bb($("d_menu"),jD.toLowerCase());bb($("d_wmode"),jr.toLowerCase());if(!bz){cX="url";}xM(cX);$("d_fromurl").value=aI;$("d_bgcolor").value=aS;$("s_bgcolor").style.backgroundColor=aS;$("d_width").value=au;$("d_height").value=at;$("d_vspace").value=cJ;$("d_hspace").value=cL;$("d_flashvars").value=iZ;var kP=$("TD_Right");var eU=$("Fieldset_Right");var h1=kP.offsetHeight;var h2=eU.offsetHeight;if(h1>h2){if(O.ae){eU.style.height=h1+"px";}else{eU.style.height=(h1-2)+"px";}}xN();if($("d_fromurl")){if(O.ae){$("d_fromurl").onpropertychange=lg;}else{C.ao($("d_fromurl"),"input",lg);}}parent.bp(Q);};function lg(){if(O.ae){if(event.propertyName!='value'){return;}}xN();};function xM(dc){if(dc=="url"){$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_checkfromurl").checked=true;if(bz){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}}else{$("d_checkfromurl").checked=false;$("uploadfile").disabled=false;$("d_checkfromfile").checked=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}}};function UploadError(an){eW();xM('file');$("divProcessing").style.display="none";try{cv($("uploadfile"),jF(an,config.AllowFlashExt,config.AllowFlashSize));}catch(e){}};function UploadSaved(fw){$("d_fromurl").value=fw;cH();};function cH(){aI=dX($("d_fromurl").value);aS=$("d_bgcolor").value;bt=$("d_align").value;au=$("d_width").value;at=$("d_height").value;cJ=$("d_vspace").value;cL=$("d_hspace").value;fm=$("d_loop").value;fl=$("d_play").value;jQ=$("d_scale").value;jW=$("d_quality").value;jD=$("d_menu").value;jr=$("d_wmode").value;iZ=$("d_flashvars").value;if(aj=="MODI"){D.style.width=au;D.style.height=at;bm(D,'align',bt);bm(D,'vspace',cJ);bm(D,'hspace',cL);bg.hq('bgcolor',aS);bg.hq('loop',fm);bg.hq('play',fl);bg.hq('scale',jQ);bg.hq('quality',jW);bg.hq('menu',jD);bg.hq('wmode',jr);bg.hq('flashvars',iZ);wg(D,bg.ww())}else{var f="<embed type=\"application/x-shockwave-flash\" pluginspage=\"http://www.macromedia.com/go/getflashplayer\"";if(bt!=""){f+=" align=\""+bt+"\"";}if(au!=""){f+=" width=\""+au+"\"";}if(at!=""){f+=" height=\""+at+"\"";}if(cJ!=""){f+=" vspace=\""+cJ+"\"";}if(cL!=""){f+=" hspace=\""+cL+"\"";}f+=" src=\""+aI+"\"";if(aS!=""){f+=" bgcolor=\""+aS+"\"";}if(fm!=""){f+=" loop=\""+fm+"\"";}if(fl!=""){f+=" play=\""+fl+"\"";}if(jQ!=""){f+=" scale=\""+jQ+"\"";}if(jW!=""){f+=" quality=\""+jW+"\"";}if(jD!=""){f+=" menu=\""+jD+"\"";}if(jr!=""){f+=" wmode=\""+jr+"\"";}if(iZ!=""){f+=" flashvars=\""+iZ+"\"";}f+="></embed>";EWIN.insertHTML(f);}parent.aT();};function ok(){$("d_width").value=du($("d_width").value);$("d_height").value=du($("d_height").value);$("d_vspace").value=du($("d_vspace").value);$("d_hspace").value=du($("d_hspace").value);if($("d_checkfromurl").checked){cH();}else{if(!hw($("uploadfile").value,config.AllowFlashExt)){UploadError("ext");return false;}fD();$("divProcessing").style.display="";document.myuploadform.submit();}};function fD(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_bgcolor").disabled=true;$("d_align").disabled=true;$("d_width").disabled=true;$("d_height").disabled=true;$("d_vspace").disabled=true;$("d_hspace").disabled=true;$("d_loop").disabled=true;$("d_play").disabled=true;$("d_scale").disabled=true;$("d_quality").disabled=true;$("d_menu").disabled=true;$("d_wmode").disabled=true;$("d_flashvars").disabled=true;$("d_ok").disabled=true;};function eW(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_bgcolor").disabled=false;$("d_align").disabled=false;$("d_width").disabled=false;$("d_height").disabled=false;$("d_vspace").disabled=false;$("d_hspace").disabled=false;$("d_loop").disabled=false;$("d_play").disabled=false;$("d_scale").disabled=false;$("d_quality").disabled=false;$("d_menu").disabled=false;$("d_wmode").disabled=false;$("d_flashvars").disabled=false;$("d_ok").disabled=false;};function xN(){var R,v;if($("d_checkfromurl").checked){v=$("d_fromurl").value;R=v;if(config.BaseHref!=""){R=nk(R);}}else{v=$("uploadfile").value;R="file:///"+v;}var cy="";if(v.length>4){cy=v.substr(v.length-4).toLowerCase();}if(cy!=".swf"){$("tdPreview").innerHTML="";return;}if(!$("d_checkfromurl").checked&&O.ae){if(cy==".swf"){if(!dz(true)){return;}var h=U.GetFlashHeader(v);if(ef()){return;}U=null;if(h){var a=h.split("|");$("d_width").value=a[0];$("d_height").value=a[1];}}}$("tdPreview").innerHTML="";var f="";f="<embed src=\""+R+"\" quality=\"high\" wmode=\"transparent\" width=\"180\" height=\"200\" type=\"application/x-shockwave-flash\" pluginspage=\"http://www.macromedia.com/go/getflashplayer\"></embed>";$("tdPreview").innerHTML=f;};function js(bG,hp,co){if(co=="tab_mfu"){DLGMFU.lY("flash",$(co),DLGTab.gR[1].Width+"px",DLGTab.gR[1].Height+"px");}}</script> <script event="OnCancel(an)" for="eWebEditorMFU">

if(an==""){parent.aP();}</script> <script event="OnUpload(an, aa)" for="eWebEditorMFU">

if(an=="endall"||an=="endapart"){var f="";var dY=aa.split("|");for(var i=0;i<dY.length;i++){var a=dY[i].split("::");if(a.length==3&&a[1]!=""){var bO=a[0].substr(a[0].lastIndexOf("\\")+1);var fq=a[1];var cy=fq.substr(fq.lastIndexOf(".")+1);var di="200";var dq="200";if(cy.toLowerCase()=="swf"){var ad=U.GetFlashHeader(bO);if(!ef()){if(ad){var ub=ad.split("|");di=ub[0];dq=ub[1];}}}f+='<embed src="'+fq+'" quality="high" loop="true" play="true" width="'+di+'" height="'+dq+'" type="application/x-shockwave-flash" pluginspage="http://www.macromedia.com/go/getflashplayer"></embed><br>';EWIN.addUploadFile(bO,fq);}}EWIN.insertHTML(f);parent.aT();}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <script type="text/javascript">if(config.MFUEnable=="1"){DLGTab.jB([[lang["DlgComTabNormal"],"tab_normal"],[lang["DlgComTabMFU"],"tab_mfu"]]);}</script> <table id="tab_normal" border=0 cellpadding=0 cellspacing=5 align=center> <tr valign=top><td> <table border=0 cellpadding=0 cellspacing=0 align=center width="100%"> <tr> <td> <fieldset> <legend><span lang=DlgFlashSource></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript">

if(bz){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"xM('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\">");document.write(jL("flash"));document.write("</td>");document.write("</tr>");}</script> <tr> <td noWrap width="20%"><input type=radio id="d_checkfromurl" value="1" onclick="xM('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td noWrap width="80%"> <script type="text/javascript">

if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:100%' size=20 value=''></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"xQ('flash','fromurl')\" value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:100%' size=30 value=''>");}</script> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgFlashEffect></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgFlashPlay></span>:</td> <td noWrap width="29%"> <select id=d_play size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='true' lang=DlgComYes></option> <option value='false' lang=DlgComNo></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgFlashLoop></span>:</td> <td noWrap width="29%"> <select id=d_loop size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='true' lang=DlgComYes></option> <option value='false' lang=DlgComNo></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgFlashMenu></span>:</td> <td noWrap width="29%"> <select id=d_menu size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='true' lang=DlgComYes></option> <option value='false' lang=DlgComNo></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgFlashWMode></span>:</td> <td noWrap width="29%"> <select id=d_wmode size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='window' lang=DlgFlashWmWindow></option> <option value='opaque' lang=DlgFlashWmOpaque></option> <option value='transparent' lang=DlgFlashWmTransparent></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgFlashQuality></span>:</td> <td noWrap width="29%"> <select id=d_quality size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='low' lang=DlgFlashQuLow></option> <option value='autolow' lang=DlgFlashQuAutoLow></option> <option value='medium' lang=DlgFlashQuMedium></option> <option value='autohigh' lang=DlgFlashQuAutoHigh></option> <option value='high' lang=DlgFlashQuHigh></option> <option value='best' lang=DlgFlashQuBest></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgFlashScale></span>:</td> <td noWrap width="29%"> <select id=d_scale size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='showall' lang=DlgFlashScShowall></option> <option value='noborder' lang=DlgFlashScNoborder></option> <option value='exactfit' lang=DlgFlashScExactfit></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgAlign></span>:</td> <td noWrap width="29%"> <select id=d_align size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='right' lang=DlgAlignRight></option> <option value='top' lang=DlgAlignTop></option> <option value='middle' lang=DlgAlignMiddle></option> <option value='bottom' lang=DlgAlignBottom></option> <option value='absmiddle' lang=DlgAlignAbsmiddle></option> <option value='absbottom' lang=DlgAlignAbsbottom></option> <option value='baseline' lang=DlgAlignBaseline></option> <option value='texttop' lang=DlgAlignTexttop></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgComBgColor></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bgcolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bgcolor onclick="fX('bgcolor')" align=absmiddle></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgComWidth></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_width size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgComHeight></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_height size=10 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgComVSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_vspace size=10 value="" onkeydown="et(event);"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgComHSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_hspace size=10 value="" onkeydown="et(event);"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgFlashVars title="FlashVars"></span>:</td> <td noWrap width="80%" colspan=4><input type=text id=d_flashvars size=10 style="width:100%"></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> </table> </td><td id="TD_Right"> <fieldset id="Fieldset_Right"> <legend><span lang=DlgComPreview></span></legend> <table border=0 cellpadding=0 cellspacing=5 width="200" height="240" valign=top id=tablePreview> <tr><td colspan=2 bgcolor=#FFFFFF align=center valign=middle id=tdPreview height="100%"> </td></tr> <tr><td id=tdPreviewSize></td><td align=right><input type=button class="dlgBtn" id=btnPreivew onclick="xN()" lang=DlgComPreview></td></tr> </table> </fieldset> </td></tr> <tr><td noWrap align=right colspan=2><input type=submit class="dlgBtnCommon dlgBtn" value='' id=d_ok onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="$('tdPreview').innerHTML='';parent.aP();" lang=DlgBtnCancel></td></tr> </table> <div id="tab_mfu" style="display:none"></div> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:70px;top:100px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> </body> </html>