<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/test">
  <process id="sealUserSbApplyNew" name="公章使用申请" isExecutable="true">
    <userTask id="dz" name="公章使用申请订正" activiti:assignee="${startUser}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditeitParentInput.ac" default="0&amp;pageState=edit"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <userTask id="ksfzrshnextzg" name="第一步：科室负责人" activiti:candidateGroups="OS100000104">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditParentInput.ac" default="1&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow4" sourceRef="dz" targetRef="ksfzrshnextzg"></sequenceFlow>
    <userTask id="usertask4" name="第二步：主管部门审批" activiti:assignee="${manager}" activiti:candidateGroups="OS100000105">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditParentInput.ac" default="2&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <userTask id="usertask6" name="第三步：所领导审批" activiti:assignee="${leader}" activiti:candidateGroups="OS100000106">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditParentInput.ac" default="3&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="exclusivegateway6" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow18" sourceRef="ksfzrshnextzg" targetRef="exclusivegateway6"></sequenceFlow>
    <sequenceFlow id="flow19" name="通过" sourceRef="exclusivegateway6" targetRef="usertask4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <startEvent id="startevent1" name="Start"></startEvent>
    <endEvent id="endevent1" name="End"></endEvent>
    <exclusiveGateway id="exclusivegateway8" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow22" sourceRef="usertask4" targetRef="exclusivegateway8"></sequenceFlow>
    <sequenceFlow id="flow24" name="驳回" sourceRef="exclusivegateway6" targetRef="dz">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow25" sourceRef="startevent1" targetRef="ksfzrshnextzg"></sequenceFlow>
    <sequenceFlow id="flow26" name="驳回" sourceRef="exclusivegateway8" targetRef="dz">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="bureau" name="第四步：法人审核" activiti:candidateGroups="OS100000403">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditParentInput.ac" default="4&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow29" name="通过" sourceRef="bureau" targetRef="endevent1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow30" name="驳回" sourceRef="bureau" targetRef="dz">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="exclusivegateway9" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow31" sourceRef="usertask6" targetRef="exclusivegateway9"></sequenceFlow>
    <sequenceFlow id="flow32" sourceRef="exclusivegateway9" targetRef="endevent1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${step=='2'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow33" sourceRef="exclusivegateway9" targetRef="bureau">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${step=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow34" sourceRef="exclusivegateway9" targetRef="dz">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${step=='dz'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="office" name="第三步：合规性审查" activiti:candidateGroups="OS100000107">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditParentInput.ac" default="5&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow37" sourceRef="exclusivegateway8" targetRef="office">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="exclusivegateway12" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow41" sourceRef="exclusivegateway12" targetRef="usertask6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow42" sourceRef="office" targetRef="exclusivegateway12"></sequenceFlow>
    <sequenceFlow id="flow43" sourceRef="exclusivegateway12" targetRef="dz">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_sealUserSbApplyNew">
    <bpmndi:BPMNPlane bpmnElement="sealUserSbApplyNew" id="BPMNPlane_sealUserSbApplyNew">
      <bpmndi:BPMNShape bpmnElement="dz" id="BPMNShape_dz">
        <omgdc:Bounds height="83.0" width="121.0" x="120.0" y="214.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ksfzrshnextzg" id="BPMNShape_ksfzrshnextzg">
        <omgdc:Bounds height="76.0" width="121.0" x="120.0" y="92.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask4" id="BPMNShape_usertask4">
        <omgdc:Bounds height="76.0" width="142.0" x="400.0" y="92.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask6" id="BPMNShape_usertask6">
        <omgdc:Bounds height="86.0" width="127.0" x="850.0" y="87.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway6" id="BPMNShape_exclusivegateway6">
        <omgdc:Bounds height="40.0" width="40.0" x="300.0" y="109.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="startevent1" id="BPMNShape_startevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="2.0" y="112.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endevent1" id="BPMNShape_endevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="1090.0" y="112.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway8" id="BPMNShape_exclusivegateway8">
        <omgdc:Bounds height="40.0" width="40.0" x="579.0" y="109.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="bureau" id="BPMNShape_bureau">
        <omgdc:Bounds height="76.0" width="105.0" x="959.0" y="0.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway9" id="BPMNShape_exclusivegateway9">
        <omgdc:Bounds height="40.0" width="40.0" x="991.0" y="109.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="office" id="BPMNShape_office">
        <omgdc:Bounds height="75.0" width="123.0" x="650.0" y="92.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway12" id="BPMNShape_exclusivegateway12">
        <omgdc:Bounds height="40.0" width="40.0" x="790.0" y="109.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow4" id="BPMNEdge_flow4">
        <omgdi:waypoint x="180.0" y="214.0"></omgdi:waypoint>
        <omgdi:waypoint x="180.0" y="168.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow18" id="BPMNEdge_flow18">
        <omgdi:waypoint x="241.0" y="130.0"></omgdi:waypoint>
        <omgdi:waypoint x="300.0" y="129.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow19" id="BPMNEdge_flow19">
        <omgdi:waypoint x="340.0" y="129.0"></omgdi:waypoint>
        <omgdi:waypoint x="400.0" y="130.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="54.0" width="24.0" x="353.0" y="112.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow22" id="BPMNEdge_flow22">
        <omgdi:waypoint x="542.0" y="130.0"></omgdi:waypoint>
        <omgdi:waypoint x="579.0" y="129.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow24" id="BPMNEdge_flow24">
        <omgdi:waypoint x="320.0" y="149.0"></omgdi:waypoint>
        <omgdi:waypoint x="320.0" y="255.0"></omgdi:waypoint>
        <omgdi:waypoint x="241.0" y="255.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="54.0" width="24.0" x="324.0" y="180.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow25" id="BPMNEdge_flow25">
        <omgdi:waypoint x="37.0" y="129.0"></omgdi:waypoint>
        <omgdi:waypoint x="120.0" y="130.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow26" id="BPMNEdge_flow26">
        <omgdi:waypoint x="599.0" y="149.0"></omgdi:waypoint>
        <omgdi:waypoint x="599.0" y="253.0"></omgdi:waypoint>
        <omgdi:waypoint x="241.0" y="255.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="54.0" width="24.0" x="609.0" y="180.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow29" id="BPMNEdge_flow29">
        <omgdi:waypoint x="1064.0" y="38.0"></omgdi:waypoint>
        <omgdi:waypoint x="1107.0" y="38.0"></omgdi:waypoint>
        <omgdi:waypoint x="1107.0" y="112.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="32.0" x="1098.0" y="65.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow30" id="BPMNEdge_flow30">
        <omgdi:waypoint x="1064.0" y="38.0"></omgdi:waypoint>
        <omgdi:waypoint x="1151.0" y="38.0"></omgdi:waypoint>
        <omgdi:waypoint x="1151.0" y="255.0"></omgdi:waypoint>
        <omgdi:waypoint x="241.0" y="255.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="32.0" x="1191.0" y="121.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow31" id="BPMNEdge_flow31">
        <omgdi:waypoint x="977.0" y="130.0"></omgdi:waypoint>
        <omgdi:waypoint x="991.0" y="129.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow32" id="BPMNEdge_flow32">
        <omgdi:waypoint x="1031.0" y="129.0"></omgdi:waypoint>
        <omgdi:waypoint x="1090.0" y="129.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow33" id="BPMNEdge_flow33">
        <omgdi:waypoint x="1011.0" y="109.0"></omgdi:waypoint>
        <omgdi:waypoint x="1011.0" y="76.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow34" id="BPMNEdge_flow34">
        <omgdi:waypoint x="1011.0" y="149.0"></omgdi:waypoint>
        <omgdi:waypoint x="1010.0" y="255.0"></omgdi:waypoint>
        <omgdi:waypoint x="859.0" y="255.0"></omgdi:waypoint>
        <omgdi:waypoint x="241.0" y="255.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow37" id="BPMNEdge_flow37">
        <omgdi:waypoint x="619.0" y="129.0"></omgdi:waypoint>
        <omgdi:waypoint x="650.0" y="129.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow41" id="BPMNEdge_flow41">
        <omgdi:waypoint x="830.0" y="129.0"></omgdi:waypoint>
        <omgdi:waypoint x="850.0" y="130.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow42" id="BPMNEdge_flow42">
        <omgdi:waypoint x="773.0" y="129.0"></omgdi:waypoint>
        <omgdi:waypoint x="790.0" y="129.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow43" id="BPMNEdge_flow43">
        <omgdi:waypoint x="810.0" y="149.0"></omgdi:waypoint>
        <omgdi:waypoint x="809.0" y="256.0"></omgdi:waypoint>
        <omgdi:waypoint x="241.0" y="255.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>