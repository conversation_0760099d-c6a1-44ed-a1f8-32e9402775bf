<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var Q=lang["DlgMap"];document.write("<title>"+Q+"</title>");var D;var sT;var tx;var fU=null;var tn="";var lK="";var au="";var at="";var ho=new Array();var kc=new Array();var tV=new Array();var pB=new Array();var pD=new Array();var tU=new Array();var tP=new Array();var nf=new Array();var oA="";var sY;var cC;D=I.aX();tn=D.src;lK=D.useMap;au=D.offsetWidth;at=D.offsetHeight;var nQ=A.F.body.getElementsByTagName("MAP");for(var i=0;i<nQ.length;i++){nf[i]=nQ[i].name.toUpperCase();if(("#"+nf[i])==lK.toUpperCase()){fU=nQ[i];}}oA="<img id='SOURCEIMAGE' border=0 src='"+tn+"' width='"+au+"' height='"+at+"'>";if(fU){for(var i=0;i<fU.areas.length;i++){ho[i]=fU.areas(i).href;kc[i]=fU.areas(i).target;tV[i]=fU.areas(i).coords;var a=tV[i].split(",");pB[i]=parseInt(a[0]);pD[i]=parseInt(a[1]);tU[i]=parseInt(a[2])-pB[i];tP[i]=parseInt(a[3])-pD[i];oA+="<img id='myIMAGE"+i+"' border=1 src='../sysimage/space.gif' style='position:absolute;left:"+pB[i]+"px;top:"+pD[i]+"px;width:"+tU[i]+"px;height:"+tP[i]+"px;zIndex:"+(i+1)+"'>";}}function wI(){sY=$("mapIframe").contentWindow;cC=sY.document;cC.designMode="On";cC.open();cC.write("<head><style>body,a,table,td {font-size:9pt;font-family:Verdana, Arial, Helvetica, sans-serif;Color:#000000;}</style></head><body MONOSPACE>");cC.body.innerHTML=oA;cC.body.contentEditable="true";cC.execCommand("2D-Position",true,true);cC.execCommand("LiveResize",true,true);cC.close();C.ao(cC,'paste',C.aR);C.ao(cC,'help',C.aR);C.ao(cC,'keydown',C.aR);C.ao(cC,'keyup',C.aR);C.ao(cC,'contextmenu',C.aR);C.ao(cC,'dblclick',vA);};var fz=new Object();function vA(e){if(!e){e=mapIframe.event;}var dv=e.target||e.srcElement;if(dv){if((dv.tagName=="IMG")&&(dv.id!="SOURCEIMAGE")){var vp=dv.id;var mK=parseInt(vp.substr(7));fz.pn=ho[mK];fz.Target=kc[mK];fz.Index=mK;cZ.OpenDialog("hyperlink.htm?action=other&returnfieldflag="+mK);}}return C.aR(e);};function wq(){ho[fz.Index]=fz.pn;kc[fz.Index]=fz.Target;};function wz(){var n=ho.length;ho[n]="";kc[n]="";cC.body.innerHTML+="<img id='myIMAGE"+n+"' border=1 src='../sysimage/space.gif' style='position:absolute;zIndex:"+(n+1)+";width:20;height:20;left:0;top:0'>";};function xd(){var b=true;var n=0;var s="";while(b){n++;s="AutoMap"+n;if(wv(s)){b=false;}}return s;};function wv(T){T=T.toUpperCase();for(var i=0;i<nf.length;i++){if(nf[i]==T){return false;}}return true;};function ok(){var b=false;for(var i=0;i<ho.length;i++){var H=cC.getElementById("myIMAGE"+i);if(H){b=true;}}if(b){var html="";for(var i=0;i<ho.length;i++){var H=cC.getElementById("myIMAGE"+i);if(H){var tE=parseInt(H.style.left);var sU=parseInt(H.style.top);var vY=parseInt(H.style.width);var vX=parseInt(H.style.height);var wd=tE+vY;var vV=sU+vX;html+="<area shape='rect' href='"+ho[i]+"' target='"+kc[i]+"' coords='"+tE+","+sU+","+wd+","+vV+"'>";}}if(fU){fU.innerHTML=html;}else{lK=xd();D.useMap="#"+lK;EWIN.appendHTML("<map name='"+lK+"'>"+html+"</map>");}}else{if(fU){C.cW(fU);}D.useMap="";}D.width=cC.getElementById("SOURCEIMAGE").width;D.height=cC.getElementById("SOURCEIMAGE").height;parent.aT();};function ah(){lang.TranslatePage(document);wI();parent.bp(Q);}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=3 align=center width="600" height="400"> <tr><td colspan=2 height="100%"><iframe ID="mapIframe" MARGINHEIGHT="1" MARGINWIDTH="1" width="100%" scrolling="yes" height="100%" src="blank.htm"></iframe></td></tr> <tr><td colspan=2 height=5></td></tr> <tr> <td><input type=button class="dlgBtnCommon dlgBtn" value='' id=btnNew onclick="wz()" lang=DlgMapNew>&nbsp;(<span lang=DlgMapDesc></span>)</td> <td align=right><input type=button class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel></td> </tr> </table> </td></tr></table> </body> </html> 