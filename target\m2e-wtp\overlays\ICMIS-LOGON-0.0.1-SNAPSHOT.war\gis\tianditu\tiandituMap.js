/**
 * @Title: tiandituMap.js
 * @Description: 使用天地图API做地图展示、地址查询、获取经纬度等操作
 * Copyright: Copyright (c) 2015
 * Company:中科软科技股份有限公司信息应用事业部
 * @author: pzx
 * @date: 2015/05/27
 * @version: V1.0
 */

var map;
var zoom = 5;  //设置初始的地图显示级别
var localsearch; //定义搜索对象
var mapclick;  //定义手动标注对象
var markerPoint;  
var loadingGisSearchBox;//遮罩
/**
 * @Description:地图初始化
 * @author:pzx
 * @date:2015/05/27
 */
function onLoad() {
	// 初始化地图对象
	map = new TMap("mapDiv");
	//添加导航控件
	map.addControl(new TNavigationControl());
	//添加地图切换控件
	map.addControl(new TMapTypeControl());
	//添加比例尺控件
	map.addControl(new TScaleControl());
	//添加鹰眼控件
	//map.addControl(new TOverviewMapControl());
	// 设置显示地图的中心点和级别
	map.centerAndZoom(new TLngLat(108.58107, 35.19609), zoom);
	// 允许鼠标滚轮缩放地图
	map.enableHandleMouseScroll();
	// 允许双击地图放大
	map.enableDoubleClickZoom();

	var config = {
		pageCapacity : 10, // 每页显示的数量
		onSearchComplete : localSearchResult
	// 接收数据的回调函数
	};
	// 创建搜索对象
	localsearch = new TLocalSearch(map, config);
}

/**
 * @Description:根据城市和关键字查询地址和经纬度
 * @author:pzx
 * @date:2015/05/27
 */
function searchAddr() {
	removeMapClick();
	loadingGisSearchBox = mini.loading("查询中，请稍后……");
	localsearch.search(mini.get('city').getText()
			+ mini.get('keyWord').getValue(), 7);

}

/**
 * @Description:地址查询的回调函数
 * @author:pzx
 * @date:2015/05/27
 * @param result 查询返回的结果
 */
function localSearchResult(result) {
	// 清空地图及搜索列表
	clearAll();

	// 添加提示词
	prompt(result);

	// 根据返回类型解析搜索结果
	switch (parseInt(result.getResultType())) {
	case 1:
		// 解析点数据结果
		pois(result.getPois());
		break;
	case 2:
		// 解析推荐城市
		statistics(result.getStatistics());
		break;
	case 3:
		// 解析行政区划边界
		area(result.getArea());
		break;
	}
	mini.hideMessageBox(loadingGisSearchBox);
}

// 解析提示词
function prompt(obj) {
	var prompts = obj.getPrompt();
	if (prompts) {
		var promptHtml = "";
		for (var i = 0; i < prompts.length; i++) {
			var prompt = prompts[i];
			var promptType = prompt.type;
			var promptAdmins = prompt.admins;
			var meanprompt = prompt.DidYouMean;
			if (promptType == 1) {
				promptHtml += "<p>您是否要在" + promptAdmins[0].name
						+ "</strong>搜索更多包含<strong>" + obj.getKeyword()
						+ "</strong>的相关内容？<p>";
			} else if (promptType == 2) {
				promptHtml += "<p>在<strong>" + promptAdmins[0].name
						+ "</strong>没有搜索到与<strong>" + obj.getKeyword()
						+ "</strong>相关的结果。<p>";
				if (meanprompt) {
					promptHtml += "<p>您是否要找：<font weight='bold' color='#035fbe'><strong>"
							+ meanprompt + "</strong></font><p>";
				}
			} else if (promptType == 3) {
				promptHtml += "<p style='margin-bottom:3px;'>有以下相关结果，您是否要找：</p>"
				for (i = 0; i < promptAdmins.length; i++) {
					promptHtml += "<p>" + promptAdmins[i].name + "</p>";
				}
			}
		}
		if (promptHtml != "") {
			document.getElementById("promptDiv").style.display = "block";
			document.getElementById("promptDiv").innerHTML = promptHtml;
		}
	}
}

/**
 * @Description:解析点数据结果
 * @author:tianditu
 * @date:2015/05/27
 * @param obj:查询返回的结果数据，包含POI点信息
 */
function pois(obj) {
	if (obj) {
		// 显示搜索列表
		var divMarker = document.createElement("div");
		// 坐标数组，设置最佳比例尺时会用到
		var zoomArr = [];
		for (var i = 0; i < obj.length; i++) {
			// 闭包
			(function(i) {
				// POI点名称
				var name = obj[i].name;
				// 地址
				var address = obj[i].address;
				// 坐标
				var lnglatArr = obj[i].lonlat.split(" ");
				var lnglat = new TLngLat(lnglatArr[0], lnglatArr[1]);
				//需要显示在地图上面的信息，可以是html格式
				var winHtml = "经度：" + lnglatArr[0] + "</br>纬度：" + lnglatArr[1]
						+ " </br>地址:" + address+"</br><input type='button' onClick=returnResult("+lnglatArr[0]+","+lnglatArr[1]+",'"+address+"') value='选取该点'/>";

				// 创建标注对象
				var marker = new TMarker(lnglat);
				// 地图上添加标注点
				map.addOverLay(marker);
				// 注册标注点的点击事件
				TEvent.bind(marker, "click", marker, function() {
					var info = this.openInfoWinHtml(winHtml);
					info.setTitle(name);
				});
				zoomArr.push(lnglat);

				// 在页面上显示搜索的列表
				var a = document.createElement("a");
				a.href = "javascript://";
				a.innerHTML = name;
				//搜索结果的单击事件，单击在地图上定位并显示详细信息
				a.onclick = function() {
					showPosition(marker, name, winHtml);
				}
				//搜索结果的双击事件，双击把经纬度和地址信息返回到页面
				a.ondblclick = function() {
					returnResult(lnglatArr[0],lnglatArr[1], address);
				}
				//拼接查询结果列表
				divMarker.appendChild(document.createTextNode((i + 1) + "."));
				divMarker.appendChild(a);
				divMarker.appendChild(document.createElement("br"));
			})(i);
		}
		// 显示地图的最佳级别
		map.setViewport(zoomArr);
		// 显示搜索结果
		divMarker.appendChild(document.createTextNode('共'
				+ localsearch.getCountNumber() + '条记录，分'
				+ localsearch.getCountPage() + '页,当前第'
				+ localsearch.getPageIndex() + '页'));
		document.getElementById("searchDiv").appendChild(divMarker);
		document.getElementById("resultDiv").style.display = "block";
	}else{
		document.getElementById("searchDiv").innerHTML="<strong>没有搜索到相关结果。</strong>";
		document.getElementById("searchDiv").style.display = "block";
		document.getElementById("resultDiv").style.display = "block";
		
	}
}

/**
 * @Description：显示查询结果定位和详细信息
 * @author:pzx
 * @date:2015/05/27
 * @param:marker 在地图上显示的图像标注
 * @param:title  弹出信息框的标题
 * @param:winHtml 弹出信息框的内容
 */
function showPosition(marker, title, winHtml) {
	var info = marker.openInfoWinHtml(winHtml);
	map.centerAndZoom(marker.getLngLat(), 12);
	info.setTitle(title);
}

/**
 * @Description：把查询结果返回到业务系统
 * @author:pzx
 * @date:2015/05/27
 * @param:marker 在地图上显示的图像标注，包括经纬度信息
 * @param:address  POI点的详细地址
 */
/*function returnResult(lng,lat,address) {
	//目前只是测试代码，可以自己补全如何给业务系统返回值
	alert("经度：" + lng + "</br>纬度："
			+ lat + " </br>地址:" + address);
}*/

/**
 * @Description：返回经纬度结果
 * @author:pzx
 * @date:2015/05/27
 * @param:marker 在地图上显示的图像标注，包括经纬度信息
 * @param:address  POI点的详细地址
 */
/*function returnResultByLntLat(lng, lat) {
	//目前只是测试代码，可以自己补全如何给业务系统返回值
	alert("经度：" + lng + "</br>纬度："+ lat);
}*/

/**
 * @Description：根据经纬度信息在地图上做标注
 * @author:pzx
 * @date:2015/05/27
 * @param:lng 经度
 * @param:lat  纬度
 * @name:POI点名称
 */
function markPoint(lng, lat, name) {
	var lnglat = new TLngLat(lng, lat);
	// 创建标注对象
	markerPoint = new TMarker(lnglat);
	// 地图上添加标注点
	map.addOverLay(markerPoint);
	var winHtml = "经度：" + lng + "，纬度：" + lat;
	// 注册标注点的点击事件
	TEvent.bind(markerPoint, "click", markerPoint, function() {
		var info = this.openInfoWinHtml(winHtml);
		info.setTitle(name);
	});
}

function markPointShowWin(lng, lat, name){
	var lnglat = new TLngLat(lng, lat);
	// 创建标注对象
	markerPoint = new TMarker(lnglat);
	// 地图上添加标注点
	map.addOverLay(markerPoint);
	var winHtml = "经度：" + lng + "，纬度：" + lat;
	// 注册标注点的点击事件
	TEvent.bind(markerPoint, "click", markerPoint, function() {
		var info = this.openInfoWinHtml(winHtml);
		info.setTitle(name);
	});
	var info = markerPoint.openInfoWinHtml(winHtml);
	info.setTitle(name);
	map.centerAndZoom(markerPoint.getLngLat(), 12);
}

// 取消标注的坐标点
function removeMarkPoint() {
	map.removeOverLay(markPoint);
}

// 清除地图上的所有坐标点
function cleanAllMark() {
	clearAll();
	//map.clearOverLays();
}

/**
 * @Description：手动选取坐标
 * @author:pzx
 * @date:2015/05/27
 */
function addMapClick() {
	// 移除地图的点击事件
	removeMapClick();
	// 移除地图上所有标记点和查询结果
	clearAll();

	// 注册地图的点击事件
	mapclick = TEvent.addListener(map, "click", function(p) {
		//每次点击都把地图清空，避免点一直在增长
		map.clearOverLays();
		// 将像素坐标转换成经纬度坐标
		var lnglat = map.fromContainerPixelToLngLat(p);
		var marker = new TMarker(lnglat);
		// 地图上添加标注点
		map.addOverLay(marker);
		var winHtml = "经度：" + lnglat.getLng() + "</br>纬度：" + lnglat.getLat()+"</br><input type='button' onClick=returnResultByLntLat("+lnglat.getLng()+","+lnglat.getLat()+") value='选取该点'/>";
		var info = marker.openInfoWinHtml(winHtml);
		info.setTitle("经纬度信息");
	});
}

//移除地图的点击事件
function removeMapClick() {
	// 移除地图的点击事件
	TEvent.removeListener(mapclick);
}

// 解析推荐城市
function statistics(obj) {
	if (obj) {
		// 坐标数组，设置最佳比例尺时会用到
		var pointsArr = [];
		var priorityCitysHtml = "";
		var allAdminsHtml = "";
		var priorityCitys = obj.priorityCitys;
		if (priorityCitys) {
			// 推荐城市显示
			priorityCitysHtml += "在中国以下城市有结果<ul>";
			for (var i = 0; i < priorityCitys.length; i++) {
				priorityCitysHtml += "<li>" + priorityCitys[i].name + "("
						+ priorityCitys[i].count + ")</li>";
			}
			priorityCitysHtml += "</ul>";
		}

		var allAdmins = obj.allAdmins;
		if (allAdmins) {
			allAdminsHtml += "更多城市<ul>";
			for (var i = 0; i < allAdmins.length; i++) {
				allAdminsHtml += "<li>" + allAdmins[i].name + "("
						+ allAdmins[i].count + ")";
				var childAdmins = allAdmins[i].childAdmins;
				if (childAdmins) {
					for (var m = 0; m < childAdmins.length; m++) {
						allAdminsHtml += "<blockquote>" + childAdmins[m].name
								+ "(" + childAdmins[m].count + ")</blockquote>";
					}
				}
				allAdminsHtml += "</li>"
			}
			allAdminsHtml += "</ul>";
		}
		document.getElementById("statisticsDiv").style.display = "block";
		document.getElementById("statisticsDiv").innerHTML = priorityCitysHtml
				+ allAdminsHtml;
	}
}

// 解析行政区划边界
function area(obj) {
	if (obj) {
		// 坐标数组，设置最佳比例尺时会用到
		var pointsArr = [];
		var points = obj.points;
		for (var i = 0; i < points.length; i++) {
			var regionLngLats = [];
			var regionArr = points[i].region.split(",");
			for (var m = 0; m < regionArr.length; m++) {
				var lnglatArr = regionArr[m].split(" ");
				var lnglat = new TLngLat(lnglatArr[0], lnglatArr[1]);
				regionLngLats.push(lnglat);
				pointsArr.push(lnglat);
			}
			// 创建线对象
			var line = new TPolyline(regionLngLats, {
				strokeColor : "blue",
				strokeWeight : 3,
				strokeOpacity : 1,
				strokeStyle : "dashed"
			});
			// 向地图上添加线
			map.addOverLay(line);
		}
		// 显示最佳比例尺
		map.setViewport(pointsArr);
	}
}

// 清空地图及搜索列表
function clearAll() {
	map.clearOverLays();
	document.getElementById("searchDiv").innerHTML = "";
	document.getElementById("resultDiv").style.display = "none";
	document.getElementById("statisticsDiv").innerHTML = "";
	document.getElementById("statisticsDiv").style.display = "none";
	document.getElementById("promptDiv").innerHTML = "";
	document.getElementById("promptDiv").style.display = "none";
	document.getElementById("suggestsDiv").innerHTML = "";
	document.getElementById("suggestsDiv").style.display = "none";
	document.getElementById("lineDataDiv").innerHTML = "";
	document.getElementById("lineDataDiv").style.display = "none";
}
