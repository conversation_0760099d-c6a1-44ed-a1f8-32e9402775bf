﻿
.mini-mask-msg{	
	border:2px solid #888;
}
/*----------------------------------- tools ----------------------------------*/

.mini-tools-close
{
    background:url(images/tools/close.gif) no-repeat 50% 0px;
}

/*----------------------------------- toolbar ----------------------------------*/

.mini-toolbar
{
    border:solid 1px #8C8C8C;
    padding:5px;
    background:#F3F3F3;
}
.separator
{
    border-left:solid 1px #A8A8A8;    
}

/*----------------------------------- button ----------------------------------*/

.mini-button
{
    border:1px solid #aaa;
    background:#E0DFDF url(images/button/button.gif) repeat-x 0 0;
    color:#000000;
}
body a:hover.mini-button
{
    border: 1px solid #999;
    background:#f2f2f2 url(images/button/hover.gif) repeat-x 0 0;              
}
body .mini-button-pressed, body a:hover.mini-button-pressed,
body .mini-button-checked, body a:hover.mini-button-checked,
body a.mini-button-popup, body a:hover.mini-button-popup
{
    background:#d5d5d5;     
}
body .mini-button-disabled, body a:hover.mini-button-disabled
{
    border: 1px solid #cecec3;
    color:#808080;
    background:#E0DFDF url(images/button/disabled.gif) repeat-x 0 0px;        
}


/*----------------------------------- textbox ----------------------------------*/
.mini-textbox-border
{
    background:white;
	border-color:#a5acb5;        
}
body .mini-textbox-focus .mini-textbox-border
{
    border-color: #7F7F7F;
}


/*----------------------------------- buttonedit ----------------------------------*/

.mini-buttonedit-border
{
    background:white;
	border-color:#a5acb5;      
}
body .mini-buttonedit-focus .mini-buttonedit-border
{
    border-color: #7F7F7F;
}
.mini-buttonedit-button
{
	background:#E6E6E6 url(images/buttonedit/button.gif) repeat-x 50% 50%;		
	border:#a5acb5 1px solid;   
	padding:0;
}
.mini-buttonedit-button-hover,
.mini-buttonedit-hover .mini-buttonedit-button
{
    background:#f2f2f2 url(images/buttonedit/hover.gif) repeat-x 0 0; 
	border-color:#7F7F7F;
}
.mini-buttonedit-button-pressed,
.mini-buttonedit-popup .mini-buttonedit-button
{
	border-color:#7F7F7F;
    background:#d5d5d5;	
}
.mini-popupedit .mini-buttonedit-icon
{
    background:url(images/buttonedit/icon2.gif) no-repeat 60% 30%;
}
.mini-datepicker .mini-buttonedit-icon
{
    background:url(images/buttonedit/icon2.gif) no-repeat 60% 30%;
}



/*------------------------- panel -----------------------*/

.mini-panel-border
{
    border:1px solid #8B8B8B;  
}
.mini-panel-header
{
    height:21px;
    background:#DCDCDC;
}
.mini-panel-header-inner
{
   padding-top:3px;
}
.mini-panel-toolbar
{
    border-bottom:solid 1px #C9C9C9;
    background:#F3F3F3;
}

.mini-panel-footer
{
    border-top:solid 1px #C9C9C9;
    background:#F3F3F3;
}

/*----------------------------- window -------------------------*/
.mini-window .mini-panel-header
{
    background:#DCDCDC;
}
.mini-window .mini-panel-footer
{
    background:#F3F3F3;
}

/*------------------- navbar ------------------*/
.mini-outlookbar-border
{
    border:1px solid #A8A8A8;         
}
.mini-outlookbar-groupHeader
{
    background:#E0E0E0;    
}
/* view2 */
.mini-outlookbar-view2 .mini-outlookbar-groupHeader
{
    border:solid 1px #A8A8A8; 
}
.mini-outlookbar-view2 .mini-outlookbar-groupBody
{    
    background:#efefef;
}
/* view3 */
.mini-outlookbar-view3 .mini-outlookbar-group
{
    border:solid 1px #A8A8A8; 
}


/*----------------------- layout -----------------------*/

.mini-layout-region
{
    border:1px solid #8B8B8B;
}
.mini-layout-region-header
{
    background:#E0E0E0;
    border-bottom:solid 1px #A8A8A8;
}
.mini-layout-proxy
{
    background:#E0E0E0;
}
.mini-layout-proxy-hover
{
    background:#F2F2F2;    
}

/*------------------------- menu --------------------------------*/

.mini-menu
{
    border-color:#8B8B8B;
    background:white url(images/menu/menubg.gif) repeat-y;   
    color:Black;
}
.mini-menuitem
{
    line-height:20px;
}
.mini-menuitem-hover
{
    border-color:#888888;
    background:#f2f2f2;
}
.mini-menu-popup
{
    border-color:#888888; 
    background:#f2f2f2;    
}
.mini-menuitem-selected
{    
    border-color:#888888; 
    background:#cbcbcb;    
}
.mini-menuitem-text, .mini-menuitem-text a
{
    color:Black;
}
/* menu horizontal */
.mini-menu-horizontal .mini-menu-inner
{
    background:#f0f0f0;
}
.mini-menu-horizontal .mini-menuitem-hover
{
    border:solid 1px #888888;
    background:#cbcbcb;
}
.mini-menu-horizontal  .mini-menu-popup
{
    border:solid 1px #888888;
    border-bottom:0px;
    background:#cbcbcb;
}

/*---------------------- listbox -----------------------------*/
.mini-listbox-border
{
    border:solid 1px #9f9f9f;
}
.mini-listbox-item-hover{
	background:#CFCFCF;
}
.mini-listbox-item-selected{
	background:#a5a5a5;
	color:White;
}
.mini-listbox-header
{    
    background:#dcdcdc;
    border-bottom:solid 1px #a0a0a0;
}

/*------------------- treegrid --------------------*/
.mini-treegrid-border
{
    border-color:#9f9f9f;
}

.mini-treegrid-header
{
    border-bottom:none;
}
.mini-treegrid-headerInner
{
    background:#dcdcdc;
}
.mini-treegrid-selectedNode
{
	background:#a5a5a5;
}
.mini-treegrid-hoverNode
{
    background:#CFCFCF;
}

/*---------------------- calendar -----------------------------*/
.mini-calendar
{    
    border:1px solid #9f9f9f;       
}
.mini-calendar-header
{   
    background:#dcdcdc;
    border-bottom:solid 1px #c9c9c9;    
}
.mini-calendar-footer
{
    background:#ededed;
}
.mini-calendar-tadayButton, .mini-calendar-clearButton,
.mini-calendar-okButton, .mini-calendar-cancelButton
{
    border:1px solid #7F7F7F;
    background:#E0DFDF url(images/calendar/button.gif) repeat-x 0 0;
    color:#000000;
}
.mini-calendar-menu-selected, a:hover.mini-calendar-menu-selected
{
    color:White;
    background:#8D8D8D;
    border:solid 1px black;
}
.mini-calendar .mini-calendar-selected
{
    background:#8D8D8D;
    color:White;
    border:solid 1px #606060;
}
.mini-calendar .mini-calendar-today
{
    border:1px solid #C00000;
}


/*---------------------- tabs -----------------------------*/
.mini-tabs-scrollCt
{
    border-color:#a8a8a8;
    background:#F3F3F3;
}
.mini-tabs-leftButton, .mini-tabs-rightButton
{
    border:solid 1px #7F7F7F;
    background-color:#E0E0E0;
}
a:hover.mini-tabs-leftButton,a:hover.mini-tabs-rightButton
{
    background-color:#F2F2F2;
}
/* top */
.mini-tabs-bodys
{
    border:solid 1px #a8a8a8;
    border-top:0;
}
.mini-tabs-space
{
    border-bottom:solid 1px #a8a8a8;
}
.mini-tabs-space2
{
    border-bottom:solid 1px #a8a8a8;
}

.mini-tab
{
    background-color: #E0E0E0;
    border: 1px solid #a8a8a8;
    color:Black;
}
.mini-tab-hover
{        
    background:#F2F2F2;    
}
.mini-tab-active
{
    border-bottom:solid 1px white;
    background:white;    
}

/* bottom */
.mini-tabs-header-bottom .mini-tabs-space,
.mini-tabs-header-bottom .mini-tabs-space2
{
    border:0;
    border-top: 1px solid #a8a8a8;
}
.mini-tabs-header-bottom .mini-tabs-bodys
{    
    border:solid 1px #a8a8a8;
    border-bottom:0;
}
.mini-tabs-header-bottom .mini-tab-active
{
    border-top:solid 1px white;
    border-bottom:solid 1px #a8a8a8;
}
.mini-tabs-body-bottom
{
    border:solid 1px #a8a8a8;
    border-bottom:0;
}
/* left */
.mini-tabs-header-left .mini-tabs-space,
.mini-tabs-header-left .mini-tabs-space2
{
    border:0;
    border-right: 1px solid #a8a8a8;
}
.mini-tabs-header-left .mini-tabs-bodys
{
    border:solid 1px #a8a8a8;
    border-left:0;
}
.mini-tabs-header-left .mini-tab-active
{    
    border:solid 1px #a8a8a8;
    border-right:solid 1px white;
}
.mini-tabs-body-left
{
    border:solid 1px #a8a8a8;
    border-left:0;
}

/* right */
.mini-tabs-header-right .mini-tabs-space,
.mini-tabs-header-right .mini-tabs-space2
{
    border:0;
    border-left: 1px solid #a8a8a8;
}
.mini-tabs-header-right .mini-tabs-bodys
{    
    border:solid 1px #a8a8a8;
    border-right:0;
}
.mini-tabs-header-right .mini-tab-active
{    
    border:solid 1px #a8a8a8;
    border-left:solid 1px white;
}
.mini-tabs-body-right
{
    border:solid 1px #a8a8a8;
    border-right:0;
}


/*---------------------- tree -----------------------------*/

.mini-tree-node-hover .mini-tree-nodeshow
{
    border:1px solid #888888;
    background:#f2f2f2;
}

.mini-tree-selectedNode .mini-tree-nodeshow
{
    border:1px solid #888888;
    background:#d8d8d8;
}

/*---------------------- grid -----------------------------*/
.mini-grid-headerCell, .mini-grid-topRightCell
{
    background:#dcdcdc;
    border-right:#9f9f9f 1px solid;
    border-bottom:#9f9f9f 1px solid;
}
.mini-grid-footer, .mini-grid-pager
{
    background:#f2f2f2;
}
.mini-grid-detailRow
{
    background:#F0F0F0;
}

/*---------------------- progressbar -----------------------------*/
.mini-progressbar
{
    border:1px solid #a5acb5;
}
.mini-progressbar-border
{
    border:1px solid #a5acb5;
}
.mini-progressbar-bar
{
    background:#ababab;
}
.mini-progressbar-text
{
   color:#222;
}