﻿
/*----------------------------------- bgcss ----------------------------------*/
.app-header
{
    background:#dae5bb;
}
.app-toolbar
{
    background:#fff;
}
.mini-modal
{
    background:#fff;    
    opacity: .7;-moz-opacity: .7;filter: alpha(opacity=70);    
}
.mini-mask-background
{
    background:#fff;    
    opacity: 0;-moz-opacity: 0;filter: alpha(opacity=0);    
}
.mini-popup
{
    box-shadow: rgba(0,​ 0,​ 0,​ 0.6) 0px 6px 12px 0px;  
}

/*----------------------------------- tools ----------------------------------*/
/*
.mini-tools .mini-tools-collapse
{
    background:url(images/tools/collapse.gif) no-repeat 50% 50%;
    width:15px;	
}
.mini-tools .mini-tools-expand
{
    background:url(images/tools/expand.gif) no-repeat 50% 50%;
    width:15px;	
}
.mini-tools-close
{
    background:url(images/tools/close.gif) no-repeat 50% 0px;
    width:15px;	
}
*/

/*----------------------------------- toolbar ----------------------------------*/

.mini-toolbar
{
    background:#fff;
    border-color:#c2d88a;
}
.separator
{
    border-left:solid 1px #c2d88a;    
}

/*----------------------------------- button ----------------------------------*/

.mini-button
{
    
    background: #dae5bb;
    border-color: #c2d88a;
    color: #444;
    font-size:13px;
    font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;    
    line-height:24px;
}
.mini-button-text 
{
    line-height:18px;
}
body a:hover.mini-button
{
    background: #e5f0c9;
    border-color:#cccccc;	     
}
body .mini-button-pressed, body a:hover.mini-button-pressed,
body .mini-button-checked, body a:hover.mini-button-checked,
body a.mini-button-popup, body a:hover.mini-button-popup
{
    background: #e5f0c9;
    border-color:#cccccc;
	/*-webkit-box-shadow:inset 0 0 5px 3px #d4d4d4;
	box-shadow:inset 0 0 5px 3px #d4d4d4	*/
}
body a.mini-button-disabled, body a:hover.mini-button-disabled
{
    border-color:#bbb;
    background: transparent;
    opacity: 0.5;
    filter: alpha(opacity=50);	
}


/*----------------------------------- textbox ----------------------------------*/
.mini-textbox
{
    height:25px;
}
.mini-textbox-border
{
    height:23px;
    padding-left:4px;
    padding-right:4px;
    background:white;
	border-color:#cccccc;
}
body .mini-textbox-focus .mini-textbox-border
{
    border-color: #aaa;
    /*background:#e5f0c9;    */
}
.mini-textbox-input
{
    height:23px;
    line-height:23px;
}


body .mini-error .mini-textbox-border,
body .mini-error .mini-buttonedit-border
{
    border-color: #ffa8a8;
    background-color: #fff3f3;
}

/*----------------------------------- buttonedit ----------------------------------*/
.mini-buttonedit
{
    height:25px;
}
.mini-buttonedit-border,
.mini-buttonedit-input
{
    height:23px;line-height:23px;
}
.mini-buttonedit-border
{
	background:white;
	border-color:#cccccc;  
	padding-left:4px;  
}
body .mini-buttonedit-focus .mini-buttonedit-border
{
    border-color: #aaa;
    /*background:#e5f0c9;    */
}
.mini-buttonedit-buttons
{
    height:100%;
}
.mini-buttonedit-button
{
    background:#fff;    
    border-left:solid 1px #cccccc;
    color: #444;
	padding:0;
	margin:0;
	height:100%;
	width:18px;
	text-align:center;
}
.mini-buttonedit-button-hover,
.mini-buttonedit-hover .mini-buttonedit-button
{
	color:#444;		
	border-width:0px;
	border-left-width:1px;
    background:#e5f0c9;    
    border-color:#cccccc;	
}
.mini-buttonedit-button-pressed,
.mini-buttonedit-popup .mini-buttonedit-button
{
	color:#444;
	border-width:0px;
	border-left:solid 1px #adadad;
    background:#e5f0c9;
    border-color:#cccccc;	
}
.mini-buttonedit-icon
{
    margin-top:4px;
    display:inline-block;
}
.mini-popupedit .mini-buttonedit-icon
{
    background:url(images/buttonedit/arrow.gif) no-repeat  1px 2px;
}
.mini-datepicker .mini-buttonedit-icon
{
    background:url(images/buttonedit/date.gif) no-repeat  1px 2px;
}
.mini-buttonedit-up span, .mini-buttonedit-down span
{
    background:url(images/buttonedit/spinner_arrows.png) no-repeat 0 50%; 
}
.mini-buttonedit-down span
{
    background-position:-16px 50%;
}
.mini-buttonedit-close
{
    margin-top:3px;
}

/*------------------------- panel -----------------------*/

.mini-panel-border
{    
    border-color:#c2d88a;     
}
.mini-panel-header
{
    height:32px;
    background:#dae5bb;
    color:#444;
    font-weight:bold;
    border-bottom:solid 1px #c2d88a;
}
.mini-panel-header-inner
{
   padding-top:7px;
}
.mini-panel .mini-tools
{
    top:8px;
    right:6px;
}
.mini-panel-toolbar
{
    border-bottom:solid 1px #ccc;
    background:#fff;
    border-color:#c2d88a;
}

.mini-panel-footer
{
    border-top:solid 1px #ccc;
    background:#fff;
    border-color:#c2d88a;
}




/*----------------------------- window -------------------------*/
.mini-window
{
    box-shadow: rgba(0,​ 0,​ 0,​ 0.6) 0px 6px 12px 0px;  
}
.mini-window .mini-panel-header
{    
    background:#dae5bb;
}
.mini-window .mini-panel-footer
{
    background:#fff;
}
/*.mini-tools-max
{
	background:url(images/tools/max.gif) no-repeat;
}
.mini-tools-restore
{
	background:url(images/tools/restore.gif) no-repeat;
}*/


/*------------------- navbar ------------------*/
.mini-outlookbar-border
{
    border-color:#c2d88a;         
}
.mini-outlookbar .mini-outlookbar-groupHeader
{
  /*  background:#d9e8fb url(images/navbar/header.gif) repeat-x 0 0;    */
  background:#fff;
    border-color:#c2d88a;
}
.mini-outlookbar .mini-outlookbar-groupTitle
{
    font-weight:normal;
}
.mini-outlookbar .mini-outlookbar-group 
{
    border-color:#c2d88a;
}
.mini-outlookbar .mini-outlookbar-groupBody
{    
    border-color:#c2d88a;
}
/* view2 */
.mini-outlookbar-view2 .mini-outlookbar-groupHeader
{
    border:solid 1px #c2d88a; 
}
.mini-outlookbar-view2 .mini-outlookbar-groupBody
{    
    background:#fff;
}
/* view3 */
.mini-outlookbar-view3 .mini-outlookbar-group
{
    border:solid 1px #c2d88a; 
}

.mini-outlookbar .mini-tools-collapse
{
    width:15px;	
}
/*
.mini-outlookbar .mini-outlookbar-expand .mini-tools-collapse
{
    background:url(images/navbar/expand.gif) no-repeat 50% 50%;   
}
.mini-outlookbar .mini-outlookbar-collapse .mini-tools-collapse
{
    background:url(images/navbar/collapse.gif) no-repeat 50% 50%;   
}

*/

.mini-outlookbar-groupTitle
{
    line-height:32px;
}
.mini-outlookbar-groupHeader
{
    height:30px;
}

.mini-outlookbar-groupHeader .mini-tools
{
    top:8px;right:6px;
}
.mini-outlookbar-icon
{
    position:relative;top:4px;
}

.mini-outlookbar .mini-outlookbar-hover
{
    background:#e5f0c9;
    
}
.mini-outlookbar-expand .mini-outlookbar-groupHeader
{
    background:#c2d88a;
}

/*----------------------- splitter -----------------------*/
.mini-splitter-border
{
    border-color: #c2d88a;     
}
.mini-splitter .mini-splitter-pane1{
    border-color:#c2d88a;
}
.mini-splitter .mini-splitter-pane2{
    border-color:#c2d88a;
}

/*----------------------- layout -----------------------*/
.mini-layout-border
{
    border-color:#c2d88a;
}
.mini-layout-region
{
    border-color:#c2d88a;    
}
.mini-layout-region-header
{
    background:#dae5bb;
    border-color:#c2d88a;
    height:32px;
}
.mini-layout-proxy
{
    border-color:#c2d88a;
    background:#dae5bb;
    height:32px;
    width:30px;
}
.mini-layout-proxy-hover
{
    background:#e5f0c9;    
}

.mini-layout-region-header .mini-tools
{
    right:8px;
    top:8px;
}

.mini-layout-proxy .mini-tools
{
    right:8px;
    top:8px;
}

.mini-layout-region-title
{
    line-height:30px;
    color:#444;
}

/*------------------------- menu --------------------------------*/
 .mini-menu-open
 {
     box-shadow:rgba(0,​ 0,​ 0,​ 0.6) 0px 6px 12px 0px;
 }
.mini-menu
{
	background:#fff;		
	border-color:#c2d88a;
    color:#444;    
}
.mini-menu-border
{
    /*border-radius: 2px; */
    border-color:#c2d88a;
}
.mini-menu-inner
{
    padding:2px;
}
.mini-menuitem
{
    line-height:24px;    
}
.mini-menuitem-hover,  .mini-menu-popup
{
    border-color:#cccccc;
	background:#e5f0c9;
}

.mini-menuitem-selected
{
    border-color:#cccccc;
	background:#c2d88a;
}
.mini-menuitem-text, .mini-menuitem-text a
{
    color:#444;
}
.mini-separator
{
    border-top:solid 1px #cccccc;
}

/* menu horizontal */

.mini-menu-horizontal .mini-menu-inner
{
    background:#fff;
    height:auto;
}
.mini-menu-horizontal .mini-menuitem-hover
{

}
.mini-menu-horizontal  .mini-menu-popup
{
}

.mini-menu-horizontal .mini-menuitem-allow
{
    background:url(images/menu/menu_arrows.png) no-repeat 0 50%;
    width:16px;height:16px;
    top:-4px;left:2px;
    position:relative;
}

.mini-menu-horizontal .mini-menuitem-inner
{
    padding-left:8px;
    padding-right:6px;
}

.mini-menu-horizontal .mini-menuitem-icon
{
    margin-top:4px;
}

/*---------------------- listbox -----------------------------*/
.mini-listbox-border
{    
    border:#c2d88a 1px solid;
}
body .mini-listbox-header td
{
    line-height:30px;
    border-color:#c2d88a;
}
.mini-listbox-border td
{
    line-height:24px;       
}
body .mini-listbox .mini-listbox-item td{
	border-color:#ddd;
}
.mini-listbox-item-hover td{
    background:#e5f0c9;
	border-color:#cccccc;
}
.mini-listbox-item-selected td{
	background:#c2d88a;
	border-color:#cccccc;
}
.mini-listbox-header
{
    background:#fff;
    border-bottom:solid 1px #c2d88a;
}

/*---------------------- calendar -----------------------------*/
.mini-calendar
{    
    border-color:#c2d88a;           
}
.mini-calendar-header
{   
    background:#dae5bb;    
    border-color:#c2d88a;    
    height:28px;
}
.mini-calendar-footer
{
    border-top:solid 1px #c2d88a;
    background:#fafafa;  
    padding-left:2px;
    padding-right:2px;  
}
.mini-calendar-tadayButton, .mini-calendar-clearButton,
.mini-calendar-okButton, .mini-calendar-cancelButton
{
    background: #dae5bb;
    border-color: #c2d88a;
    color: #444;
    padding-top:5px;
    padding-bottom:4px;
}
.mini-calendar-menu-selected, a:hover.mini-calendar-menu-selected
{
    color:#444;
    background:#c2d88a;
    border-color:#cccccc;
    
}

.mini-calendar .mini-calendar-selected
{
     color:#444;
    background:#e5f0c9;
    border:solid 1px #cccccc;
}
.mini-calendar .mini-calendar-today
{
    border:1px solid #C00000;
}
.mini-calendar-daysheader td
{border-bottom:solid 1px #cccccc;    
}
.mini-calendar-menu
{
    border-color:#cccccc;
}
.mini-calendar-title
{
    font-size:13px;
    font-weight:bold;
    color:#444;
    line-height:28px;
}
a:hover.mini-calendar-menu-month 
{
    background:#e5f0c9;  
    border-color:#cccccc; 
}
    
/*---------------------- tabs -----------------------------*/

.mini-tabs-scrollCt
{
    border-color:#c2d88a;
    background:#dae5bb;
}

.mini-tabs-leftButton, .mini-tabs-rightButton
{
    border:solid 1px #c2d88a;
    background-color:#fff;
}
a:hover.mini-tabs-leftButton,a:hover.mini-tabs-rightButton
{
    border:solid 1px #c2d88a;
    background-color:#e5f0c9;
}
/* top */
.mini-tabs-bodys
{
    border-color:#c2d88a;
}
.mini-tabs-space
{
    border-color:#c2d88a;
}
.mini-tabs-space2
{
    border-color:#c2d88a;
}

.mini-tab
{
    background: #f4f7eb;
    border-color:#c2d88a;
    color: #666;    
    padding-left:12px;
    padding-right:12px;
    font-size:13px;    
   font-weight:bold;
}
.mini-tab-text
{
    line-height:26px;
}
.mini-tab-hover
{    
    background:#e5f0c9;
}
.mini-tab-active
{
    border-bottom:solid 1px #fff;
    background:#fff;
    color:#000;
}

.mini-tab-close
{
    opacity: .6;-moz-opacity: .6;filter: alpha(opacity=60);   
    /*position:absolute;top:3px;right:3px;*/
}
.mini-tab-close-hover
{
    opacity: 1;-moz-opacity: 1;filter: alpha(opacity=100);   
    background-color:transparent;
}


/* bottom */
.mini-tabs-header-bottom .mini-tabs-space,
.mini-tabs-header-bottom .mini-tabs-space2
{
    border:0;
    border-top: 1px solid #c2d88a;
}
.mini-tabs-header-bottom .mini-tabs-bodys
{    
    border:solid 1px #c2d88a;
    border-bottom:0;
}
.mini-tabs-header-bottom .mini-tab-active
{
    border-top:solid 1px white;
    border-bottom:solid 1px #c2d88a;
}
.mini-tabs-body-bottom
{
    border:solid 1px #c2d88a;
    border-bottom:0;
}
/* left */
.mini-tabs-header-left .mini-tabs-space,
.mini-tabs-header-left .mini-tabs-space2
{
    border:0;
    border-right: 1px solid #c2d88a;
}
.mini-tabs-header-left .mini-tabs-bodys
{
    border:solid 1px #c2d88a;
    border-left:0;
}
.mini-tabs-header-left .mini-tab-active
{    
    border:solid 1px #c2d88a;
    border-right:solid 1px white;
}
.mini-tabs-body-left
{
    border:solid 1px #c2d88a;
    border-left:0;
}


/* right */
.mini-tabs-header-right .mini-tabs-space,
.mini-tabs-header-right .mini-tabs-space2
{
    border:0;
    border-left: 1px solid #c2d88a;
}
.mini-tabs-header-right .mini-tabs-bodys
{    
    border:solid 1px #c2d88a;
    border-right:0;
}
.mini-tabs-header-right .mini-tab-active
{    
    border:solid 1px #c2d88a;
    border-left:solid 1px white;
}
.mini-tabs-body-right
{
    border:solid 1px #c2d88a;
    border-right:0;
}

.mini-tabs-nav
{
    top:8px;
}

/*------------------- grid --------------------*/
.mini-grid-border
{
    border-color:#cccccc;
}
.mini-grid-header
{
    background:#fff;
}
.mini-grid-headerCell, .mini-grid-topRightCell
{
    background:#fff;
    border-right:#c1c1c1 1px solid;
    border-bottom:#c1c1c1 1px solid;    
}
.mini-grid-cell
{
    border-color:#ddd;
    border-bottom-color:#ddd;    
}
.mini-grid-headerCell-inner
{
    line-height:30px;
}
.mini-grid-cell-inner
{
    line-height:22px;
}
.mini-grid-filterRow
{
    background:#fff;
}
.mini-grid-footer, .mini-grid-pager
{
    border-top:solid 1px #cccccc;    
    background:#fff;
}
.mini-grid-columnproxy
{
    background:#fff;
    border:#cccccc 1px solid;    
}


body .mini-grid-row-hover, body .mini-grid-row-hover .mini-grid-frozenCell
{
    background: #e5f0c9;
}
html body .mini-grid-row-selected, body .mini-grid-row-selected .mini-grid-frozenCell
{
    background: #c2d88a;
}

.mini-grid-header-over
{
    background: #e5f0c9;
}

/*---------------------- tree -----------------------------*/


.mini-tree-node-hover .mini-tree-nodeshow
{
    background:#e5f0c9;
	border:solid 1px #cccccc;  
}
.mini-tree-selectedNode .mini-tree-nodeshow
{
    background:#c2d88a;
    border-color:#cccccc;
}
.mini-tree-nodetext
{	
    height:22px;
    line-height:22px;   
    +line-height:23px;   /*ie7*/
}
.mini-tree-nodetitle 
{
    height:24px;
}
.mini-tree-leaf
{
    background-image:url(images/tree/file.gif);
}
.mini-tree-folder
{
    background-image:url(images/tree/folder.gif);
   
}
.mini-tree-expand .mini-tree-folder
{
    background-image:url(images/tree/folder-open.gif);
}

.mini-tree-node-ecicon{
   height:24px
}
/*------------------- pager --------------------*/
.mini-pager
{
    height:auto;
    line-height:30px;
    background:#fff;
    border-color:#cccccc;
}
.mini-pager-left
{
    height:auto;
    line-height:30px;
}
.mini-pager-first
{    
    background:url(images/pager/first.gif) no-repeat;
}
.mini-pager-prev
{
    background-image:url(images/pager/prev.gif);
}
.mini-pager-next
{
    background-image:url(images/pager/next.gif);
}
.mini-pager-last
{
    background-image:url(images/pager/last.gif);
}
.mini-pager-size
{
    position:relative;top:-3px;
}
body .mini-pager-num
{
    height:16px;
}
.mini-pager-right
{
    line-height:32px;
}
.mini-pager .mini-button-iconOnly
{
    padding-top:auto;
    padding-bottom:0;
    height:23px;
}

/* tooltip */
.mini-tooltip-inner {    
    border:solid 1px #aaa;
    border-radius: 0px;    
}
.mini-tooltip .mini-tooltip-arrow 
{
    
}

/*textboxlist*/

.mini-textboxlist-border
{
    height:23px;
    border-style: solid;
    border-width: 1px;
    background:transparent;  
    border-color: #cccccc  ;       
         
    width:100%;
    cursor:text;
    vertical-align:top;
    border-radius:5px;
}
body .mini-textboxlist-focus .mini-textboxlist-border
{
    background:#ffffff;
    border-color:#aaa  ;
}
/*htmlfile*/
.mini-htmlfile .mini-buttonedit-button
{
    font-size:8pt;
    font-size:9pt\9;
    font-family: Tahoma, Verdana;    
    white-space:nowrap;
        
    border:1px solid #cccccc;
    border-top:#ffffff; 
    border-right:#ffffff;
    background:#ffffff;
    color:#444;
    padding:1px;
    width:50px;
    text-align:center;
    line-height:22px;
}
/*---------------------- progressbar -----------------------------*/
.mini-progressbar
{
    border:1px solid #ccc;
}
.mini-progressbar-border
{
    border:1px solid #ccc;
}
.mini-progressbar-bar
{
    background:#dae5bb;
}
.mini-progressbar-text
{ 
    color:#222; 
}
