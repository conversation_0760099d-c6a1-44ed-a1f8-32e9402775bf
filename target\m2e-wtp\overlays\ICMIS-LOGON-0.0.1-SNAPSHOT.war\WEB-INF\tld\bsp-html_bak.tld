<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE taglib PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.1//EN"
                        "http://java.sun.com/j2ee/dtds/web-jsptaglibrary_1_1.dtd">
<taglib>
	<tlibversion>1.0</tlibversion>
	<jspversion>1.1</jspversion>
	<shortname>BspHtml</shortname>
	<tag>
		<name>codeSelect</name>
		<tagclass>ie.bsp.ui.jsptag.CodeSelectTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>dataSource</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>checkFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>code</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>optionList</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramValues</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>alt</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>altKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>disabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>indexed</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiple</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeypress</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousedown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousemove</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseout</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseover</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleId</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tabindex</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>size</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>title</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>titleKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>radio</name>
		<tagclass>ie.bsp.ui.jsptag.BspRadioTag</tagclass>
		<attribute>
			<name>code</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>align</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>size</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramValues</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>accesskey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>alt</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>altKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>disabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>indexed</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeypress</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousedown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousemove</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseout</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseover</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleId</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tabindex</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>title</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>titleKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>dateCtrl</name>
		<tagclass>ie.bsp.ui.jsptag.DateCtrlTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>checkFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>init</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>initDay</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>timeCtl</name>
		<tagclass>ie.bsp.ui.jsptag.TimeCtlTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>init</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>initDay</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>checkFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>day</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>hour</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minute</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>second</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>month</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>zone</name>
		<tagclass>ie.bsp.ui.jsptag.ZoneTag</tagclass>
		<bodycontent>empty</bodycontent>
		<info>闁告牕鎼悡娆戠磽閺嶎偆鍨抽梺顐㈩樇鐎氥劑寮介崶鈺婂姰</info>
		<!-- 闁哄嫷鍨伴幆渚�触椤栨粍鏆忛柟鑸垫尭瑜板洦鎯旈弮锟介敓濮愬劜濠э拷闁哄瀚悺銉╂偩閿燂拷.闁哄牜浜滅槐鎴﹀触椤栨粍鐣遍柣妯垮煐閿熸垝妞掔粭鍛村箮閹惧啿绲块柛銉ㄦ椤斿秶锟借鐭粻鐔兼儍閸曨剚绨氶柡瀣娣囧﹪骞侀敓锟�濮掓稒顭堥缁樼▔缁＄lse -->
		<attribute>
			<name>fetchEmergencyOrg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- 鐟滅増鎸搁幆搴ㄦ偨閵娿儳瀹夐柟顑惧劜濠э拷闁哄瀚慨鍕矗閺嶎偆鎽滈柣锝冨劜濡烇拷,闁哄牜鍓欏顒勫极閺夊灝璁查悗瑙勭煯缁犵喖骞庨幘鍐茬悼闁哄牏鍎ら悗顖炴儍閸曨叀顤嗛柛顭掓嫹.闁稿﹦鍘ч悡娆戯拷鐢垫嚀缁ㄨ尙锟芥稒顨滈崥锟藉ǎ鍥ｅ墲娴煎懏绋夐鐘崇暠闁哄牏鍎ら悗顖滅尵鐠囪尙锟界兘鎯冮崟顓犳そ闁活噯鎷� -->
		<attribute>
			<name>fetchOrgType</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>orgTypeCheckFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgTypeViewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgCheckFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgViewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>orgOnChange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgPeer</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgCaption</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showFormat</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgDisabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneTopLevel</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneBottomLevel</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zonePeer</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneOnChange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneDefault</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgVisible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgTypeVisible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgType</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgBehavior</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>userZoneCode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>userZoneName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>userOrg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>userOrgName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneLevel</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneVisible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addressPeer</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>myOrgFirst</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>otherOrgRule</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ignoreOrgLevel</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>isOrgInitZone</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ignoreApanage</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<!-- 闁哄秴鍢茬槐锛勪沪閻愯揪鎷疯缁绘洟寮甸鍐ㄢ枏闁活枎鎷� -->
			<name>singleLine</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<!--濞戞挸鎼﹢鎾礌濞差亷鎷锋径瀣仴婵℃妫滈鏇狅拷瑙勭閻栵絾锛愬Ο鐑樺�闁挎稑鑻—锟�闁硅翰鍎遍幉锟犲捶閺夊灝闅橀柕鍡曠劍婢у秶浠﹂悙鍙夊嬀闁告牞娅ｉ悺锟� -->
			<name>zoneCaption</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<!--闁革附婢樼亸顖炴焻婢跺顏ユ俊妤�閻栵絾锛愬Ο璇差暡闁革腹鏅睤闁汇劌瀚悧鍗烆嚕閿燂拷 -->
			<name>zoneStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneDisabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>village</name>
		<tagclass>ie.bsp.ui.jsptag.VillageTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<info>闁哄鍨电花鐐烘焻婢跺顏ラ柡宥呮川椤掞拷</info>
		<attribute>
			<name>townCode</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>checkFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>zoneProperty</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>issite</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>type</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>sitetype</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>code</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>optionList</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramValues</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>alt</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>altKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>disabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>indexed</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiple</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeypress</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousedown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousemove</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseout</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseover</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleId</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tabindex</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>size</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>title</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>titleKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>address</name>
		<tagclass>ie.bsp.ui.jsptag.AddressTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>regionProperty</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addrProperty</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textProperty</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>regionCaption</name>
			<rtexprvalue>true</rtexprvalue>
			<!-- 闁绘粓顣︾紞鍥捶閹殿喛顤嗛柛銊ヮ儎缁楀懘骞忔径瀣垫敱闁汇劌瀚悥锝嗭紣濡儤鍊� -->
		</attribute>
		<attribute>
			<name>addrSelCaption</name>
			<rtexprvalue>true</rtexprvalue>
			<!-- 闁搞儲绋愰柌婊呮嫚閿斿墽鐭庨柛锔芥緲濞煎啴鏌呮径瀣仴婵℃妫涘▓鎴﹀冀閸ヮ剦鏆柛姘炬嫹 -->
		</attribute>
		<attribute>
			<name>addrGBCaption</name>
			<rtexprvalue>true</rtexprvalue>
			<!-- 闁绘粓顣︾紞鍥捶閺夋寧绲婚柛銉ㄥГ閻栴枦nput婵℃妫涘▓鎴﹀冀閸ヮ剦鏆柛姘炬嫹 -->
		</attribute>
		<attribute>
			<name>addrDetailCaption</name>
			<rtexprvalue>true</rtexprvalue>
			<!-- 闁绘粓顣︾紞鍥╂嫚閿斿墽鐭庨柛锔芥緲濞煎啯绋夐鐔哥�閺夊牊鎸搁崣鍡楊湜閸℃瑦鐣遍柡宥呮喘椤ｄ粙宕ラ敓锟� -->
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>titleWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>comboWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>opv2js</name>
		<tagclass>ie.bsp.ui.jsptag.Opv2jsTag</tagclass>
		<bodycontent>empty</bodycontent>
		<attribute>
			<name>opvName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>text</name>
		<tagclass>ie.bsp.ui.jsptag.BspTextTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>checkFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>accesskey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>alt</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>altKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>disabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>indexed</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxlength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeypress</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousedown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousemove</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseout</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseover</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>readonly</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>size</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>id</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleId</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tabindex</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>title</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>titleKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<!-- modify by pzx -->
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>inputStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnLeave</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>vtype</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>emailErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>urlErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>floatErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>intErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dateErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rangeLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rangeErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>checkbox</name>
		<tagclass>ie.bsp.ui.jsptag.BspCheckboxTag</tagclass>
		<attribute>
			<name>align</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>checkFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>checkValue</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>unCheckValue</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>checkCaption</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>unCheckCaption</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>checked</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>accesskey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>alt</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>altKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>disabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>indexed</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeypress</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousedown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousemove</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseout</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseover</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleId</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tabindex</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>title</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>titleKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>ZonePopup</name>
		<tagclass>ie.bsp.ui.jsptag.BspZoneTag</tagclass>
		<bodycontent>empty</bodycontent>
		<info>闁告牕鎼悡娆戠磽閺嶎偆鍨抽梺顐㈩樇鐎氥劑寮介崶鈺婂姰(鐎殿喚鎳撻崵锟�</info>
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneTopLevel</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneBottomLevel</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneOnChange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>codeDefault</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rootCode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectFolder</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>info</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>ZoneOrgPopup</name>
		<tagclass>ie.bsp.ui.jsptag.BspZoneOrgTag</tagclass>
		<bodycontent>empty</bodycontent>
		<info>闁告牕鎼悡娆撳嫉閻戞锟筋垶鏌呮径瀣仴闁哄秴娲ㄩ锟�鐎殿喚鎳撻崵锟�</info>
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneTopLevel</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneBottomLevel</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneOnChange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>codeDefault</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectFolder</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rootCode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>info</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>textarea</name>
		<tagclass>ie.bsp.ui.jsptag.BspTextareaTag</tagclass>
		<attribute>
			<name>accesskey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>checkFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>maxlength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>alt</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>altKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cols</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>disabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>indexed</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeypress</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousedown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousemove</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseout</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseover</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>readonly</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rows</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleId</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tabindex</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>title</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>titleKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>multibox</name>
		<tagclass>ie.bsp.ui.jsptag.BspMultiBoxTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>code</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>align</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramValues</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>checked</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>size</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>accesskey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>alt</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>altKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>disabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeypress</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousedown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousemove</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseout</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseover</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleId</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tabindex</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>title</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>titleKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>separate</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<!-- 妤犵偛鐡ㄥ﹢锟介柡鍐﹀劦閿熻棄顦肩�銊╁冀閸モ晩鍔�add by why -->
	<tag>
		<name>DateSelect</name>
		<tagclass>ie.bsp.ui.jsptag.DateSelectTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>checkFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>defaultType</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>year</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>halfyear</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>quarter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>bimonthly</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>month</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ten</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>week</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>day</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>random</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ranMonth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>sortBy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>isShow</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>doubleselect</name>
		<tagclass>ie.bsp.ui.jsptag.DoubleSelectTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>listCode</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>listParams</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>doubleCode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>doubleParams</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onDoubleChange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>defaultVal</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>doubleDefaultVal</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>doubleName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>label</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>isShow</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectAll</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>multiBox</name>
		<tagclass>ie.bsp.ui.jsptag.BspMultiBoxTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>code</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>align</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramValues</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>checked</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>size</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>accesskey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>alt</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>altKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>disabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeypress</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousedown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousemove</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseout</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseover</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleId</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tabindex</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>title</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>titleKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>subList</name>
		<tagclass>ie.bsp.ui.jsptag.SubTabListTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>delOnclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showRowNo</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>attrList</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>multilevelSelect</name>
		<tagclass>ie.bsp.ui.jsptag.MultilevelSelectTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>code</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>optionList</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramValues</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>alt</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>altKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>disabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>indexed</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiple</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeypress</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousedown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmousemove</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseout</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseover</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onmouseup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleClass</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>styleId</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tabindex</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>size</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>title</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>titleKey</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onlySubNodSelected</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>treeDlgData</name>
		<tagclass>ie.bsp.ui.jsptag.FrameTreeDlgDataTag</tagclass>
		<attribute>
			<name>code</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramValues</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>jsVarName</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>level</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nodeTree</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showCaption</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>treeMultiSelDlg</name>
		<tagclass>ie.bsp.ui.jsptag.FrameTreeDlgSelTag</tagclass>
		<attribute>
			<name>code</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>checkFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<!-- zhan -->
		<attribute>
			<name>paramValues</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>paramName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>scope</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewProperty</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>caption</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>title</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>jsVarName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>single</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>GzAddress</name>
		<tagclass>ie.gzcdcII.taglib.GzAddressTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>regionProperty</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addrProperty</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textProperty</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>regionCaption</name>
			<rtexprvalue>true</rtexprvalue>
			<!-- 闁绘粓顣︾紞鍥捶閹殿喛顤嗛柛銊ヮ儎缁楀懘骞忔径瀣垫敱闁汇劌瀚悥锝嗭紣濡儤鍊� -->
		</attribute>
		<attribute>
			<name>addrSelCaption</name>
			<rtexprvalue>true</rtexprvalue>
			<!-- 闁搞儲绋愰柌婊呮嫚閿斿墽鐭庨柛锔芥緲濞煎啴鏌呮径瀣仴婵℃妫涘▓鎴﹀冀閸ヮ剦鏆柛姘炬嫹 -->
		</attribute>
		<attribute>
			<name>addrGBCaption</name>
			<rtexprvalue>true</rtexprvalue>
			<!-- 闁绘粓顣︾紞鍥捶閺夋寧绲婚柛銉ㄥГ閻栴枦nput婵℃妫涘▓鎴﹀冀閸ヮ剦鏆柛姘炬嫹 -->
		</attribute>
		<attribute>
			<name>addrDetailCaption</name>
			<rtexprvalue>true</rtexprvalue>
			<!-- 闁绘粓顣︾紞鍥╂嫚閿斿墽鐭庨柛锔芥緲濞煎啯绋夐鐔哥�閺夊牊鎸搁崣鍡楊湜閸℃瑦鐣遍柡宥呮喘椤ｄ粙宕ラ敓锟� -->
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>titleWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>comboWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>zonecode</name>
		<tagclass>ie.bsp.ui.jsptag.ZoneCodeTag</tagclass>
		<bodycontent>empty</bodycontent>
		<attribute>
			<name>checkFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>topLevel</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>userType</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zoneDefault</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>org</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>orgcode</name>
		<tagclass>ie.bsp.ui.jsptag.OrgCodeTag</tagclass>
		<bodycontent>empty</bodycontent>
		<attribute>
			<name>checkFlag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewMsg</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>name</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgDefault</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>userType</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
</taglib>
