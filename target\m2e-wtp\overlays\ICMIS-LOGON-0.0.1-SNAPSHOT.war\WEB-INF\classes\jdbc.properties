#数据库驱动
jdbc.driverClassName=oracle.jdbc.driver.OracleDriver
#数据库链接
#jdbc.url=jdbc\:oracle\:thin\:@(DESCRIPTION\=(ADDRESS_LIST\=(ADDRESS\=(PROTOCOL\=TCP)(HOST\=**********)(PORT\=1521))(ADDRESS\=(PROTOCOL\=TCP)(HOST\=**********)(PORT\=1521)))(FAILOVER\=on)(LOAD_BALANCE\=on)(CONNECT_DATA\=(SERVER\=DEDICATED)(SERVICE_NAME\=orcl)(FAILOVER_MODE\=(TYPE\=SELECT)(METHOD\=BASIC)(RETIRES\=20)(DELAY\=15)))) 
#jdbc.url=*****************************************
jdbc.url=*******************************************
#用户名
jdbc.username=icmis
#密码
jdbc.password=embed
#最少保持的空闲连接数 （默认2个）
jdbc.prototypeCount=2
#最大活动时间(超过此时间线程将被kill,默认为30秒)
jdbc.maximumActiveTime=30000
#最大连接数
jdbc.maximumConnectionCount=50
#最小连接数
jdbc.minimumConnectionCount=2
#测试的SQL执行语句 
jdbc.houseKeepingTestSql=select * from dual
#同时最大连接数
jdbc.simultaneousBuildThrottle=50
#Hibernate查询翻译器
#3.0
hibernate.query.factory_class=org.hibernate.hql.ast.ASTQueryTranslatorFactory
#2.1
#hibernate.query.factory_class=org.hibernate.hql.classic.ClassicQueryTranslatorFactory

