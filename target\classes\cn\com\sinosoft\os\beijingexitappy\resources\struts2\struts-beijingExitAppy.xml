<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="beijingExitAppy" extends="bsp-default" namespace="/cn/com/sinosoft/os/beijingexitappy">
		<!-- 查询 -->
		<action name="qryBeijingExitAppy" class="beijingExitAppyAction" method="qryParentInput">
			<param name="sessionGroup">beijingExitAppy</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/os/beijingexitappy/qryBeijingExitAppy.jsp
			</result>
		</action>
		<action name="qryBeijingExitAppyList" class="commonQueryAction">
			<param name="sessionGroup">beijingExitAppy</param>
			<param name="queryCode">QRY_OS_BEIJING_EXIT_APPY</param>
			<param name="resultName">qryList</param>
		</action>
		<!-- 查看 -->
		<action name="beijingExitAppy_viewParent" class="beijingExitAppyAction" method="viewParent">
			<param name="sessionGroup">beijingExitAppy</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/os/beijingexitappy/edtBeijingExitAppy.jsp
			</result>
		</action>
		<!-- 添加 -->
		<action name="beijingExitAppy_addParentInput" class="beijingExitAppyAction" method="addParentInput">
			<param name="sessionGroup">beijingExitAppy</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/os/beijingexitappy/edtBeijingExitAppy.jsp
			</result>
		</action>
		<action name="beijingExitAppy_addParentSubmit" class="beijingExitAppyAction" method="addParentSubmit">
			<param name="sessionGroup">beijingExitAppy</param>
		</action>
		<!-- 修改 -->
		<action name="beijingExitAppy_edtParentInput" class="beijingExitAppyAction" method="edtParentInput">
			<param name="sessionGroup">beijingExitAppy</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/os/beijingexitappy/edtBeijingExitAppy.jsp
			</result>
		</action>
		<action name="beijingExitAppy_edtParentSubmit" class="beijingExitAppyAction" method="edtParentSubmit">
			<param name="sessionGroup">beijingExitAppy</param>
		</action>
		<!-- 删除 -->
		<action name="beijingExitAppy_delParentSubmit" class="beijingExitAppyAction" method="delParentSubmit">
			<param name="sessionGroup">beijingExitAppy</param>
		</action>
	</package>
</struts>