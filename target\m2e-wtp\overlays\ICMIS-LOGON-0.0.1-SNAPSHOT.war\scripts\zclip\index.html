<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
<title>演示：复制页面内容到剪贴板兼容各浏览器</title>
<link rel="stylesheet" type="text/css" href="../css/main.css" />
<style type="text/css">
.demo{width:760px; margin:40px auto 0 auto; min-height:150px;}
textarea{width:100%; height:80px; border:1px solid #ddd; color:#666}
#para{line-height:24px; background:#f7f7f7; padding:10px}
.copy{line-height:32px}
#msg{margin-left:10px; color:green; border:1px solid #3c3; background:url(checkmark.png) no-repeat 2px 3px; padding:3px 6px 3px 20px}
</style>
<script type="text/javascript" src="../jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="js/jquery.zclip.js"></script>
<script type="text/javascript">
$(function(){
	$('#copy_input').zclip({
		path: 'js/ZeroClipboard.swf',
		copy: function(){
			return $('#mytext').val();
		},
		afterCopy: function(){
			$("<span id='msg'/>").insertAfter($('#copy_input')).text('复制成功').fadeOut(2000);
		}
	});
	$("#copy_p").zclip({
		path: 'js/ZeroClipboard.swf',
		copy: $('#para').text()+"Helloweba.com",
		afterCopy: function(){
			$("#para").css("background-color",'#cff');
			$("<span id='msg'/>").insertAfter($('#copy_p')).text('复制成功').fadeOut(2000);
		}
	});
	$('#mytext').focus(function(){
		var txt = $(this).val();
		if(txt=='请输入内容'){
			$(this).val('');
		}
	});
});
</script>
</head>

<body>
<div id="header">
   <div id="logo"><h1><a href="http://www.helloweba.com" title="返回helloweba首页">helloweba</a></h1></div>
</div>

<div id="main">
   <h2 class="top_title"><a href="http://www.helloweba.com/view-blog-221.html">Zclip：复制页面内容到剪贴板兼容各浏览器</a></h2>
   <div class="demo">
   		<textarea id="mytext">请输入内容</textarea><br/>
       <input type="button" id="copy_input" value="复制内容" />
   </div>
   <div class="demo">
   		<p id="para">zclip是一个轻量级基于JQuery“复制到剪贴板”插件。</p>
   		
        <a href="#" id="copy_p" class="copy">复制内容</a>
   </div>

   <br/><div class="ad_76090"><script src="/js/ad_js/bd_76090.js" type="text/javascript"></script></div><br/>
</div>


<div id="footer">
    <p>Powered by helloweba.com  允许转载、修改和使用本站的DEMO，但请注明出处：<a href="http://www.helloweba.com">www.helloweba.com</a></p>
</div>
<p id="stat"><script type="text/javascript" src="/js/tongji.js"></script></p>
</body>
</html>