<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"> <html xmlns="http://www.w3.org/1999/xhtml"> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"/> <script type="text/javascript"> function uz(){return frameElement.kw;};function dP(dialog){return dialog?dialog.la:frameElement.la;};var EWIN=frameElement.kw.aR;var EWEB=EWIN.EWEB;var ec=EWIN.ec;var R=EWIN.R;var F=EWIN.F;var C=EWIN.C;var lang=EWIN.lang;var config=EWIN.config;document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");document.write("<title>eWebEditor</title>");if(F.as&& !F.gB){document.write('<'+'script type="text/javascript" src="dialog_ie6.js"><'+'\/script>');}R.lE(window);var lj=function(){var kE=[];var jm;var lX;var yY=function(){for(var i=0;i<kE.length;i++){R.hf(kE[i].document,'mousemove',xQ);R.hf(kE[i].document,'mouseup',yC);}};var xQ=function(evt){if(!jm){return;}if(!evt){evt=R.dV(this).parentWindow.event;}var si={x:evt.screenX,y:evt.screenY};lX={x:lX.x+(si.x-jm.x),y:lX.y+(si.y-jm.y)};jm=si;frameElement.style.left=lX.x+'px';frameElement.style.top=lX.y+'px';if(evt.preventDefault){evt.preventDefault();}else{evt.returnValue=false;}};var yC=function(evt){if(!jm){return;}if(!evt){evt=R.dV(this).parentWindow.event;}yY();jm=null;};return{zM:function(evt){var view=null;if(!evt){view=R.dV(this).parentWindow;evt=view.event;}else{view=evt.view;}var target=evt.srcElement||evt.target;if(target.id=='TitleCloseButton'){return;}jm={x:evt.screenX,y:evt.screenY};lX={x:parseInt(R.ce(frameElement,'left'),10),y:parseInt(R.ce(frameElement,'top'),10)};for(var i=0;i<kE.length;i++){R.az(kE[i].document,'mousemove',xQ);R.az(kE[i].document,'mouseup',yC);}if(evt.preventDefault){evt.preventDefault();}else{evt.returnValue=false;}},nv:function(w){kE.push(w);}}}();var Selection={Az:function(){window.focus();$('TitleCloseButton').focus();C.Restore();}};var xu=function(){if(this.readyState!='complete'){return;}lj.nv(this.contentWindow);};(function(){var CD=function(fT){fT.onkeydown=function(e){e=e||event||this.parentWindow.event;switch(e.keyCode){case 13:var zY=e.srcElement||e.target;if(zY.tagName=='TEXTAREA')return true;bV();return false;case 27:bn();return false;}return true;}};var Ap=function(e){e=e||window.event||$('frmMain').contentWindow.event;var dZ=e.target||e.srcElement;var mL=dZ.tagName;if(!((mL=="INPUT"&&dZ.type=="text")||mL=="TEXTAREA")){return R.aw(e);}};var wE=function(fT){R.az(fT,'contextmenu',Ap);};var uR=function(){var frmMain=$('frmMain');var hB=frmMain.contentWindow.document;var ye=R.fR(hB);var innerWidth=ye?hB.documentElement.scrollWidth:hB.body.scrollWidth;var innerHeight=ye?hB.documentElement.scrollHeight:hB.body.scrollHeight;var wY=0;var vI=0;var vQ=hB.getElementById("tabDialogSize");if(vQ){wY=vQ.offsetWidth;vI=vQ.offsetHeight;}innerWidth=Math.max(innerWidth,wY);innerHeight=Math.max(innerHeight,vI);var ve=R.eF(frmMain.contentWindow);if(innerWidth==0||ve.Width==0||frameElement.offsetWidth==0||frameElement.offsetHeight==0){window.setTimeout(uR,1);return;}frmMain.style.width=innerWidth;frmMain.style.height=innerHeight;var yM=innerWidth-ve.Width;var yO=innerHeight-ve.Height;if(yM<=0&&yO<=0){return;}window.frameElement.style.width=($("ml").offsetWidth+$("mr").offsetWidth+innerWidth)+"px";window.frameElement.style.height=($("TitleArea").offsetHeight+$("bc").offsetHeight+innerHeight)+"px";if(typeof window.jX=='function'){window.xy();}else{ec.oP(frameElement);}};window.Init=function(){if(typeof window.jX=='function'){if(!window.jX()){window.setTimeout(Init,1);return;}}$("TitleCaption").innerHTML=lang["DlgDlgTitle"];Au();R.ko(document.body);var yv=$('TitleArea');yv.onmousedown=lj.zM;lj.nv(window);lj.nv(uz().vz);if(F.as&& !F.gB){var uj=ec.yH().firstChild;if(uj.readyState=='complete'){lj.nv(uj.contentWindow);}else{uj.onreadystatechange=xu;}}wE(document);};window.Au=function(){$('ContentArea').innerHTML='<iframe id="frmMain" src="'+uz().Page+'" name="frmMain" frameborder="0" width="100%" height="100%" scrolling="auto" style="visibility: hidden;" allowtransparency="true"><\/iframe>';};window.ar=function(AR){if(!frameElement.parentNode){return null;}var frmMain=$('frmMain');var pH=frmMain.contentWindow;var hB=pH.document;window.yu=pH;if(uz().Hide){return;}$("TitleCaption").innerHTML=AR;$("LoadingArea").style.display="none";frmMain.style.visibility='';uR();wE(hB);lj.nv(pH);pH.focus();R.ko(hB.body);};window.pM=function(w,h){if(!w){uR();return;}var frmMain=$('frmMain');frmMain.style.width=w;frmMain.style.height=h;window.frameElement.style.width=$("ml").offsetWidth+$("mr").offsetWidth+w+"px";window.frameElement.style.height=$("TitleArea").offsetHeight+$("bc").offsetHeight+h+"px";if(typeof window.jX=='function'){window.jX();}};window.bV=function(kv){uh(true,kv);};window.bn=function(){Selection.Az();uh(false);};window.uh=function(Eh,kv){if($('frmMain')){$('frmMain').src=R.iO();}var qx=dP();if(qx){qx=qx.contentWindow.yu;}try{qx.uc();}catch(e){}ec.wH(window,Eh,kv);};})(); </script> </head> <body onload="Init();" class="PopupBody"> <div class="tl" id="tl"></div> <div class="tc" id="TitleArea"> <div id="TitleCaption" class="TitleCaption"></div> <div id="TitleCloseButton" class="TitleCloseButton" onclick="bn();"></div> </div> <div class="tr" id="tr"></div> <div class="ml" id="ml"></div> <div class="mc" id="ContentArea"></div> <div class="mr" id="mr"></div> <div class="bl" id="bl"></div> <div class="bc" id="bc"></div> <div class="br" id="br"></div> <div class="mc" id="LoadingArea"> <table border="0" cellpadding="0" cellspacing="0" width="100%" height="100%"> <tr><td align="center" valign="middle"><img src="images/loading.gif" border="0"/></td></tr> </table> </div> </body> </html>