<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="cn.com.sinosoft.os.newsmanage.model.NewsManage" table="OS_NEWS_MANAGE">
		<id name="id" column="ID" type="java.lang.String">
			<generator class="uuid"></generator>
		</id>
		<property name="newName" column="NEW_NAME" type="java.lang.String"/>
		<property name="newType" column="NEW_TYPE" type="java.lang.String"/>
		<property name="applyUser" column="APPLY_USER" type="java.lang.String"/>
		<property name="applyType" column="APPLY_TYPE" type="java.lang.String"/>
		<property name="blackTel" column="BLACK_TEL" type="java.lang.String"/>
		<property name="otherType" column="OTHER_TYPE" type="java.lang.String"/>
		<property name="auditState" column="AUDIT_STATE" type="java.lang.String"/>
		<property name="piId" column="PI_ID" type="java.lang.String"/>
		<property name="executeState" column="EXECUTE_STATE" type="java.lang.String"/>
		<property name="tmpModelId" column="TMPMODELID" type="java.lang.String"/>
		<property name="addZone" column="ADD_ZONE" type="java.lang.String"/>
		<property name="addOrg" column="ADD_ORG" type="java.lang.String"/>
		<property name="addDep" column="ADD_DEP" type="java.lang.String"/>
		<property name="addUser" column="ADD_USER" type="java.lang.String"/>
		<property name="addTime" column="ADD_TIME" type="java.util.Date"/>
		<property name="modyZone" column="MODY_ZONE" type="java.lang.String"/>
		<property name="modyOrg" column="MODY_ORG" type="java.lang.String"/>
		<property name="modyDep" column="MODY_DEP" type="java.lang.String"/>
		<property name="modyUser" column="MODY_USER" type="java.lang.String"/>
		<property name="modyTime" column="MODY_TIME" type="java.util.Date"/>
		<property name="state" column="STATE" type="java.lang.String"/>
		<property name="competentDep" column="COMPETENTDEP" type="java.lang.String"/>
		<property name="dataSource" column="DATA_SOURCE" type="java.lang.String"/>
		<property name="dataModyTime" column="DATA_MODY_TIME" type="java.util.Date"/>
		<property name="publishPath" column="PUBLISH_PATH" type="java.lang.String"/>
	</class>
</hibernate-mapping>