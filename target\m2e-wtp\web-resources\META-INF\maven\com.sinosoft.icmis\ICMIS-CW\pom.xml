<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.sinosoft.icmis</groupId>
		<artifactId>ICMIS</artifactId>
		<relativePath>../ICMIS</relativePath>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>ICMIS-CW</artifactId>
	<packaging>war</packaging>
	<name>ICMIS-CW Maven Webapp</name>
	<url>http://maven.apache.org</url>
	<build>
		<finalName>ICMIS-CW</finalName>
	</build>
	<dependencies>
		<dependency>
			<groupId>com.sinosoft.icmis</groupId>
			<artifactId>ICMIS-LOGON</artifactId>
			<version>${project.version}</version>
			<type>war</type>
		</dependency>
		<dependency>
			<groupId>com.sinosoft.icmis</groupId>
			<artifactId>ICMIS-LOGON</artifactId>
			<version>${project.version}</version>
			<type>jar</type>
			<classifier>classes</classifier>
			<scope>provided</scope>
		</dependency>
		
		<dependency>
			<groupId>com.sinosoft.whjk</groupId>
			<artifactId>base</artifactId>
			<type>jar</type>
			<classifier>classes</classifier>
			<scope>provided</scope>
		</dependency>
		
		<dependency>
			<groupId>com.sinosoft.core</groupId>
			<artifactId>sinosoft-util</artifactId>
			<type>jar</type>
			<classifier>classes</classifier>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.sinosoft.core</groupId>
			<artifactId>sinosoft-cas</artifactId>
			<type>jar</type>
		</dependency>

	</dependencies>
</project>