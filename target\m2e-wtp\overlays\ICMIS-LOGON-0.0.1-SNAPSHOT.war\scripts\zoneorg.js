﻿/*****************************************************************************
  用 途        : 地区、机构、科室 控件脚本
  编写人员     : lfw
  编写时间     : 2014-12-15
******************************************************************************/
	/**
	 * 
	 * @param e 机构控件
	 * @param zonecodeRoot 显示的根地区
	 * @param orgtype 机构类型
	 * @param zs 操作级别
	 * @prarm showUnknown 是否显示不详 
	 */
	function tag_onzonebeforeload(e,zonecodeRoot,zonecodeTo,orgtype,zs,showUnknown,showTop) {
		var p=e.params;
	    if(p.id!=undefined)
	    	zonecodeRoot=p.id; 
	    else{
	    	p.loadtozone=zonecodeTo;
	    }
	    p.zonecode=zonecodeRoot;
	    p.orgtype=orgtype;
	    p.zs=zs;
	    p.showUnknown=showUnknown;
	    p.showTop=showTop;
	}
		/**
		 * 
		 * @param id
		 * @param org
		 * @param orgtype
		 */
		function tag_onzonechanged(id,org,orgtype,isFirst){
			var t= mini.get(id);
			//if(t.getValue().split('_').length==2){
			//	loadZoneName(t.getValue(),t);
				//return;
			//} 
			//加载机构
			if(org!=""){
				var d =mini.get(org);
				var v = d.getValue();
				if(isFirst==false){
					d.setValue("");
					d.setText("");
				}
		    	if(t.getValue()!=""){ 
		    		d.load("${ctx}/dictQuery.ac?queryFlag=mini-combobox&code=BASE_TAG_ORGCODE&param="+t.getValue()+"|"+t.getValue());
		    		if(v!=""){
		    			var data = d.getData();
		    			for(var i=0;i<data.length;i++){
		    				if(data[i].ID==v){
		    					d.setValue(v);
		    					break;
		    				}
		    			}
						if(d.department!=undefined){
				    		tag_onorgchanged(d.id,d.department,isFirst);
						}
					}
		    	}else{
		    		d.setData([]);
		    	}
	    	}
		 
		}
		
		function tag_onorgchanged(id,department,isFirst){
			var t =mini.get(id);
			if(department!=""){
				t.set({department:department});
				var d = mini.get(department);
				var v = d.getValue();
				if(isFirst==false){
					d.setValue(''); 
					d.setText(''); 
				}
				if(t.getValue()!=""){ 
					d.load("${ctx}/dictQuery.ac?queryFlag=mini-treeselect&code=BASE_TAG_DEPARTMENT&param="+t.getValue());
					var data = d.getList();
					var arr =v.split(",");
					for(var j=0;j<arr.length;j++){
		    			a:for(var i=0;i<data.length;i++){
		    				if(data[i].ID==arr[j]){
		    					d.setValue(v);
		    					break a;
		    				}
		    			}
					}
				}else{
					d.setData([]);
				}
			}
		}
		
		/**
		 * 清空机构和科室控件的值
		 * @param orgName 机构控件名
		 * @param depName 科室控件名
		 */
		function tag_clearOrgAndDep(orgName,depName){
			if(orgName != "" && orgName != null){
			    var org = mini.get("session_ORGCODE");
				org.setValue("");
				org.setText("");
				org.setData([]);
			}
			if(depName!="" && depName != null){
				var dep = mini.get("session_DEPARTMENT");
				dep.setValue("");
				dep.setText("");
				dep.setData([]);
			}
		} 
