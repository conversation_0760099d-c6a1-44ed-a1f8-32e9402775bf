﻿
/*----------------------------------- bgcss ----------------------------------*/
body
{
    background:#f3f3f3;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 13px;
    
}

.app-header
{
    background:#efefef;
}
.app-toolbar
{
    background:#efefef;
}
.mini-modal
{
    background:#fff;    
    opacity: .7;-moz-opacity: .7;filter: alpha(opacity=70);    
}
.mini-mask-background
{
    background:#fff;    
    opacity: 0;-moz-opacity: 0;filter: alpha(opacity=0);    
}
.mini-popup
{
    box-shadow: rgba(0,​ 0,​ 0,​ 0.6) 0px 6px 12px 0px;  
    background-color:#f3f3f3;
}

/*----------------------------------- tools ----------------------------------*/
/*
.mini-tools .mini-tools-collapse
{
    background:url(images/tools/collapse.gif) no-repeat 50% 50%;
    width:15px;	
}
.mini-tools .mini-tools-expand
{
    background:url(images/tools/expand.gif) no-repeat 50% 50%;
    width:15px;	
}
.mini-tools-close
{
    background:url(images/tools/close.gif) no-repeat 50% 0px;
    width:15px;	
}
*/

/*----------------------------------- toolbar ----------------------------------*/

.mini-toolbar
{
    background: #efefef;
    border-color:#cccccc;
}
.separator
{
    border-left:solid 1px #cccccc;    
}

/*----------------------------------- button ----------------------------------*/

.mini-button
{
    
    background:#1484e6 url(images/button/button.png) repeat-x 0 50%;
    border-color: #ffffff;
    font-size:13px;
    font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;    
    line-height:24px;
    border-radius: 5px; 
}
.mini-button-text 
{
    line-height:18px;
    color:#ffffff;
}
body a:hover.mini-button
{
    background:url(images/button/hover.png) repeat-x 50% 50% #2293f7;
    border-color:#2293f7  ;
}
body a:hover.mini-button .mini-button-text
{
	color:#444444;
}
body .mini-button-pressed, body a:hover.mini-button-pressed,
body .mini-button-checked, body a:hover.mini-button-checked,
body a.mini-button-popup, body a:hover.mini-button-popup
{
    background:url(images/button/pressed.png) repeat-x 50% 50% #e69700;
    border-color:#e69700;
    color:#734d99;
	/*-webkit-box-shadow:inset 0 0 5px 3px #d4d4d4;
	box-shadow:inset 0 0 5px 3px #d4d4d4	*/
}
body .mini-button-pressed .mini-button-text
{
   color:#ffffff;
}
body a.mini-button-disabled, body a:hover.mini-button-disabled
{
    background:#81b9ea;
    border-color: #ffffff;
    opacity: 0.5;
    filter: alpha(opacity=50);	
}
.mini-button-plain .mini-button-text
{
    color:#444;	
}
.mini-button-plain .mini-button-text
{
    color:#444;	
}

/*----------------------------------- textbox ----------------------------------*/
.mini-required .mini-textbox-border, .mini-required .mini-buttonedit-border
{
	background-color:#f3f3f3;
}
.mini-textbox-border, .mini-buttonedit-border
{
	background-color:#f3f3f3;
}
.mini-textbox
{
    height:25px;
}
.mini-textbox-border
{
    height:23px;
    padding-left:4px;
    padding-right:4px;
    background-color:#f3f3f3;
	border-color:#cccccc;
	border-radius: 5px; 
}
body .mini-textbox-focus .mini-textbox-border
{
    background:url(images/button/hover.png) repeat-x 50% 50% #2293f7;
    border-color:#2293f7;
}
.mini-textbox-input
{
    height:23px;line-height:23px;
    color:black;
}
body .mini-textbox-focus .mini-textbox-input
{
    color:#ffffff;
}

body .mini-error .mini-textbox-border,
body .mini-error .mini-buttonedit-border,
body .mini-error .mini-textboxlist-border
{
    border-color: #e69700;
    background: #e69700 url(images/errorback.png) repeat-x 0 50%;
}
body .mini-error .mini-textbox-input,
body .mini-error .mini-buttonedit-input
{
   color:#ffffff;	
}

/*----------------------------------- buttonedit ----------------------------------*/
.mini-buttonedit
{
    height:25px;
}
.mini-buttonedit-border,
.mini-buttonedit-input
{
    height:23px;line-height:23px;
    color:black;
}
.mini-buttonedit-border
{
	border-color:#cccccc;  
	padding-left:4px;  
	border-radius: 5px; 
}

body .mini-buttonedit-focus .mini-buttonedit-border
{
    background:url(images/button/hover.png) repeat-x 50% 50% #2293f7;
    border-color:#cccccc;
}
body .mini-buttonedit-focus .mini-buttonedit-input
{
    color:#ffffff;
}
.mini-buttonedit-buttons
{
    height:100%;
}
.mini-buttonedit-button
{
   
    border-left:solid 1px #cccccc;
    background-color:#f3f3f3;
    color: #444;
	padding:0;
	margin:0;
	height:100%;
	width:18px;
	text-align:center;
}
.mini-buttonedit-button-hover,
.mini-buttonedit-hover .mini-buttonedit-button,
.mini-buttonedit-button-pressed,
.mini-buttonedit-popup .mini-buttonedit-button
{
	color:#444;		
	border-width:0px;
	border-left:solid 1px #cccccc;
    background:#2293f7;  
}
.mini-buttonedit-icon
{
    margin-top:4px;
    display:inline-block;
}
.mini-popupedit .mini-buttonedit-icon
{
    background:url(images/buttonedit/arrow.gif) no-repeat  1px 2px;
}
.mini-datepicker .mini-buttonedit-icon
{
    background:url(images/buttonedit/date.gif) no-repeat  1px 2px;
}
.mini-buttonedit-up span, .mini-buttonedit-down span
{
    background:url(images/buttonedit/spinner_arrows.png) no-repeat 0 50%; 
}
.mini-buttonedit-down span
{
    background-position:-16px 50%;
}
.mini-buttonedit-close
{
    margin-top:3px;
}

/*------------------------- panel -----------------------*/

.mini-panel-border
{    
    border-color:#aaaaaa; 
        
}
.mini-panel-header
{
    height:32px;
    background:url(images/header.png) repeat 50% 50%;
    color:#e69700;
    font-weight:bold;
    border-bottom:solid 1px #cccccc;
}
.mini-panel-header-inner
{
   padding-top:7px;
}
.mini-panel .mini-tools
{
    top:8px;
    right:6px;
}
.mini-panel-toolbar
{  
    background-color:#f3f3f3;
    border-color:#aaaaaa;
    border-bottom:solid 1px #cccccc;
}

.mini-panel-footer
{
    background-color:#efefef;
    border-color:#aaaaaa;
    border-top:solid 1px #cccccc;
}
.mini-panel-body
{
    background-color:#f3f3f3;
    color:black;  
}




/*----------------------------- window -------------------------*/
.mini-window
{
    box-shadow: rgba(0,​ 0,​ 0,​ 0.6) 0px 6px 12px 0px;  
}
.mini-window .mini-panel-header
{    
    background:#efefef;
}
.mini-window .mini-panel-footer
{
    background:#efefef;
}
/*.mini-tools-max
{
	background:url(images/tools/max.gif) no-repeat;
}
.mini-tools-restore
{
	background:url(images/tools/restore.gif) no-repeat;
}*/


/*------------------- navbar ------------------*/
.mini-outlookbar-border
{
    border-color:#cccccc;         
}
.mini-outlookbar .mini-outlookbar-groupHeader
{
  /*  background:#d9e8fb url(images/navbar/header.gif) repeat-x 0 0;    */
    background:url(images/button/button.png) repeat-x 50% 50% #1484e6         ;
    border:0;
    color:#ffffff;
}
.mini-outlookbar .mini-outlookbar-groupTitle
{
    font-weight:normal;
}
.mini-outlookbar .mini-outlookbar-group 
{
    border-color:#aaaaaa;
}
.mini-outlookbar .mini-outlookbar-groupBody
{    
    border-color:#aaaaaa;
}
/* view2 */
.mini-outlookbar-view2 .mini-outlookbar-groupHeader
{
    border:0; 
}
.mini-outlookbar-view2 .mini-outlookbar-groupBody
{    
    background:#fff;
}
/* view3 */
.mini-outlookbar-view3 .mini-outlookbar-group
{
    border:0; 
}

.mini-outlookbar .mini-tools-collapse
{
    width:15px;	
}
/*
.mini-outlookbar .mini-outlookbar-expand .mini-tools-collapse
{
    background:url(images/navbar/expand.gif) no-repeat 50% 50%;   
}
.mini-outlookbar .mini-outlookbar-collapse .mini-tools-collapse
{
    background:url(images/navbar/collapse.gif) no-repeat 50% 50%;   
}

*/

.mini-outlookbar-groupTitle
{
    line-height:32px;
}
.mini-outlookbar-groupHeader
{
    height:30px;
}

.mini-outlookbar-groupHeader .mini-tools
{
    top:8px;right:6px;
}
.mini-outlookbar-icon
{
    position:relative;top:4px;
}

.mini-outlookbar .mini-outlookbar-hover
{
    background:url(images/button/hover.png) repeat-x 50% 50% #2293f7;
    border-color:#2293f7;
    
}
.mini-outlookbar-expand .mini-outlookbar-groupHeader
{
    background:url(images/button/pressed.png) repeat-x 50% 50% #e69700 ;
    border-color:#e69700 ;
}

/*----------------------- splitter -----------------------*/
.mini-splitter-border
{
    border-color: #aaaaaa;     
}
.mini-splitter .mini-splitter-pane1{
    border-color:#aaaaaa;
}
.mini-splitter .mini-splitter-pane2{
    border-color:#aaaaaa;
}

/*----------------------- layout -----------------------*/
.mini-layout-border
{
    border-color:#aaaaaa;
}
.mini-layout-region
{
    border-color:#aaaaaa;    
}
.mini-layout-region-header
{
    background:url(images/header.png) repeat 50% 50%;
    border-color:#aaaaaa;
    height:32px;
}
.mini-layout-proxy
{
    border-color:#aaaaaa;
    background:url(images/header.png) repeat 50% 50%;
    height:32px;
    width:30px;
}
.mini-layout-proxy-hover
{
    background:url(images/header.png) repeat 50% 50%; 
}

.mini-layout-region-header .mini-tools
{
    right:8px;
    top:8px;
}

.mini-layout-proxy .mini-tools
{
    right:8px;
    top:8px;
}

.mini-layout-region-title
{
    line-height:30px;
    color:#e69700;
}

/*------------------------- menu --------------------------------*/
 .mini-menu-open
 {
     box-shadow:rgba(0,​ 0,​ 0,​ 0.6) 0px 6px 12px 0px;
 }
.mini-menu
{	
	border-color:#aaaaaa;  
}
.mini-menu-border
{
    /*border-radius: 2px; */
    border-color:#aaaaaa;
    background-color:#f3f3f3;
}
.mini-menu-inner
{
    padding:2px;	
}
.mini-menuitem
{  
    line-height:24px;    
    border-radius: 5px; 
}
.mini-menuitem-hover,  .mini-menu-popup
{
    background:url(images/button/hover.png) repeat-x 50% 50% #2293f7 ;
    border-color:#2293f7 ;
}

.mini-menuitem-selected
{
    border-color:#cccccc;
	background:#cccccc;
}
.mini-menuitem-text, .mini-menuitem-text a
{
    color:black;
}
.mini-menuitem-hover .mini-menuitem-text,.mini-menu-popup .mini-menuitem-text
{
    color:#ffffff;
}
.mini-separator
{
    border-top:solid 1px #cccccc;
}

/* menu horizontal */

.mini-menu-horizontal .mini-menu-inner
{
    height:auto;
    background:#f3f3f3;
}
.mini-menu-horizontal .mini-menuitem-hover
{

}
.mini-menu-horizontal  .mini-menu-popup
{
}

.mini-menu-horizontal .mini-menuitem-allow
{
    background:url(images/menu/menu_arrows.png) no-repeat 0 50%;
    width:16px;height:16px;
    top:-4px;left:2px;
    position:relative;
}

.mini-menu-horizontal .mini-menuitem-inner
{
    padding-left:8px;
    padding-right:6px;
}

.mini-menu-horizontal .mini-menuitem-icon
{
    margin-top:4px;
}

/*---------------------- listbox -----------------------------*/
.mini-listbox
{
    background:transparent;
}
.mini-listbox-border
{    
    border:#cccccc 1px solid;    
}
body .mini-listbox-header td
{
    line-height:30px;
    border-color:#cccccc;
    background:#1484e6 url(images/button/button.png) repeat-x 0 50%;
    border-color: #ffffff;    
}
.mini-listbox-border td
{
    line-height:24px;       
}
body .mini-listbox .mini-listbox-item td{
	border-color:#cccccc;
}
.mini-listbox-item-hover td{
    background:url(images/button/hover.png) repeat-x 50% 50% #2293f7;
    color:#ffffff;
}
.mini-listbox-item-selected td{
	background:url(images/button/pressed.png) repeat-x 50% 50% #e69700;
	border-color:#cccccc;
	color:#ffffff;
	
}
.mini-listbox-header
{
    background:#fff;
    border-bottom:solid 1px #cccccc;
    color:#ffffff;
}

/*---------------------- calendar -----------------------------*/
.mini-calendar
{    
    border-color:#cccccc;           
}
.mini-calendar-header
{   
    background:url(images/header.png) repeat 50% 50%;
    border-color:#cccccc;    
    height:28px;
    border-bottom:#cccccc;
}
.mini-calendar-footer
{
    border-top:solid 1px #cccccc;
    background:#f3f3f3;  
    padding-left:2px;
    padding-right:2px;  
}
.mini-calendar-tadayButton, .mini-calendar-clearButton,
.mini-calendar-okButton, .mini-calendar-cancelButton
{
    background:#1484e6   url(images/button/button.png) repeat-x 0 50%;
    border-color: #cccccc;
    color: #ffffff;
    padding-top:5px;
    padding-bottom:4px;
}
body .mini-calendar-menu-selected, body a:hover.mini-calendar-menu-selected
{
    color:#444;
    background:url(images/button/pressed.png) repeat-x 50% 50% #e69700  ;    
}
.mini-calendar-date 
{
    border-color:#f3f3f3;
    background:#1484e6   url(images/button/button.png) repeat-x 0 50%;
    color:#ffffff;
}
.mini-calendar .mini-calendar-selected
{
    color:#ffffff;
    background:url(images/button/pressed.png) repeat-x 50% 50% #e69700;
    border-color:#e69700;
}
.mini-calendar .mini-calendar-today
{
    border:1px solid #ffffff;
    background:#cce1fc;
}
.mini-calendar-daysheader td
{
  border-bottom :solid 1px #cccccc;    
}
.mini-calendar-menu
{
    border-color:#cccccc;
}
.mini-calendar-title
{
    font-size:13px;
    font-weight:bold;
    color:#444;
    line-height:28px;
}
.mini-calendar-menu-month,.mini-calendar-menu-year
{
    background:url(images/button/button.png) repeat-x 50% 50% #1484e6 ;
    color:#ffffff;
}
a:hover.mini-calendar-menu-month,
a:hover.mini-calendar-menu-year
{
    background:url(images/button/pressed.png) repeat-x 50% 50% #e69700;
    border-color:#e69700;
}
.mini-calendar-menu
{
   background:#f3f3f3;
}
.mini-calendar-menu-prevYear,
.mini-calendar-menu-nextYear
{
   width:8px;
}
.mini-calendar .mini-calendar-othermonth
 {
    color:#b6bd8b;	
 }
    
/*---------------------- tabs -----------------------------*/

.mini-tabs-scrollCt
{
    border-color:#cccccc;
    background: #efefef ;
}

.mini-tabs-leftButton, .mini-tabs-rightButton
{
    border:solid 1px #cccccc;
    background-color:#f3f3f3;
}
a:hover.mini-tabs-leftButton,a:hover.mini-tabs-rightButton
{
    background-color:#2293f7;
    border-color: #2293f7;
}
/* top */
.mini-tabs-bodys
{
    border-color:#aaaaaa;
    background-color:transparent;
}
.mini-tabs-space
{
    border-color:#cccccc;
}
.mini-tabs-space2
{
    border-color:#cccccc;
}

.mini-tab
{
    background:url(images/header.png) repeat 50% 50%;
    border-color: #aaaaaa;
    color: #444444;    
    padding-left:12px;
    padding-right:12px;
    font-size:13px;    
    font-weight:bold;
}
.mini-tab-text
{
    line-height:26px;
}
.mini-tab-hover
{    
    background:url(images/button/hover.png) repeat-x 50% 50% #2293f7;
    border-color:#cccccc;
    border-bottom-color:#cccccc;
}
.mini-tab-active
{
    border-bottom:solid 1px #f3f3f3;
    background:#f3f3f3;
    color:black;
}
.mini-tab-active .mini-tab-hover
{
    border-color:#cccccc;
}
.mini-tab-close
{
    opacity: .6;-moz-opacity: .6;filter: alpha(opacity=60);   
    /*position:absolute;top:3px;right:3px;*/
}
.mini-tab-close-hover
{
    opacity: 1;-moz-opacity: 1;filter: alpha(opacity=100);   
    background-color:transparent;
}

.mini-tab
{
    border-radius:5px;   
}
.mini-tabs-header-right .mini-tab
{
    border-top-left-radius:0px;
    border-bottom-left-radius:0px;     
}

.mini-tabs-header-left .mini-tab
{
    border-top-right-radius:0px;
    border-bottom-right-radius:0px;     
}
.mini-tabs-header-bottom .mini-tab
{
    border-top-right-radius:0px;
    border-top-left-radius:0px;     
}
.mini-tabs-header-top .mini-tab
{
    border-bottom-right-radius:0px;
    border-bottom-left-radius:0px; 
}

/* bottom */
.mini-tabs-header-bottom .mini-tabs-space,
.mini-tabs-header-bottom .mini-tabs-space2
{
    border:0;
    border-top: 1px solid #aaaaaa;
}
.mini-tabs-header-bottom .mini-tabs-bodys
{    
    border:solid 1px #aaaaaa;
    border-bottom:0;
}
.mini-tabs-header-bottom .mini-tab-active
{
    border-top:solid 1px #f3f3f3;
    border-bottom:solid 1px #aaaaaa;
}
.mini-tabs-body-bottom
{
    border:solid 1px #aaaaaa;
    border-bottom:0;
}
/* left */
.mini-tabs-header-left .mini-tabs-space,
.mini-tabs-header-left .mini-tabs-space2
{
    border:0;
    border-right: 1px solid #aaaaaa;
}
.mini-tabs-header-left .mini-tabs-bodys
{
    border:solid 1px #aaaaaa;
    border-left:0;
}
.mini-tabs-header-left .mini-tab-active
{    
    border:solid 1px #aaaaaa;
    border-right:solid 1px #f3f3f3;
}
.mini-tabs-body-left
{
    border:solid 1px #aaaaaa;
    border-left:0;
}

/* right */
.mini-tabs-header-right .mini-tabs-space,
.mini-tabs-header-right .mini-tabs-space2
{
    border:0;
    border-left: 1px solid #aaaaaa;
}
.mini-tabs-header-right .mini-tabs-bodys
{    
    border:solid 1px #aaaaaa;
    border-right:0;
}
.mini-tabs-header-right .mini-tab-active
{    
    border:solid 1px #aaaaaa;
    border-left:solid 1px #f3f3f3;
}
.mini-tabs-body-right
{
    border:solid 1px #aaaaaa;
    border-right:0;
}
.mini-tabs-nav
{
    top:8px;
}

/*------------------- grid --------------------*/
.mini-grid-viewport
{
    background:transparent;
    
}
.mini-grid-border
{
    border-color:#aaaaaa;
}
.mini-grid-header
{
    background:transparent;
}
.mini-grid-headerCell, .mini-grid-topRightCell
{
    background:#1484e6 url(images/button/button.png) repeat 50% 50%;    
    border-right:#ffffff 1px solid;
    border-bottom:#ffffff 1px solid;  
    color:#ffffff;  
}
.mini-grid-cell
{
    border-color:#ffffff;
    border-bottom-color:#cccccc;    
}
.mini-grid-headerCell-inner
{
    line-height:30px;
}
.mini-grid-cell-inner
{
    line-height:22px;
}
.mini-grid-filterRow
{
    background:transparent;
}
.mini-grid-footer, .mini-grid-pager
{
    border-top:solid 1px #cccccc;    
    background:transparent;
}
.mini-grid-columnproxy
{
    background:#fff;
    border:#cccccc 1px solid;    
}


body .mini-grid-row-hover, body .mini-grid-row-hover .mini-grid-frozenCell
{
    background:url(images/button/hover.png) repeat 50% 50% #2293f7 ;
    border-color:#ffffff;
    color:#ffffff;
}
html body .mini-grid-row-selected, body .mini-grid-row-selected .mini-grid-frozenCell
{
    background:url(images/button/pressed.png) repeat 50% 50% #e69700 ;
	border-color:#cccccc;
	color:#ffffff;
}

.mini-grid-header-over
{
    background:url(images/button/hover.png) repeat 50% 50% #2293f7;
    border-color:#ffffff;
}
html body .mini-grid .mini-grid-cell-selected
{
    background:#91d2f4;	
}
.mini-grid-summaryRow 
{
   background-color:#f3f3f3;
}
.mini-grid-detailRow
{
   background-color:#f3f3f3;
}
.mini-grid-groupCell
{
    background-color:#f3f3f3;
}
/*---------------------- tree -----------------------------*/


.mini-tree-node-hover .mini-tree-nodeshow
{
    background:url(images/button/hover.png) repeat-x 50% 50% #2293f7;
    color:#ffffff;
    border-color:#2293f7;
}
.mini-tree-node-hover .mini-tree-nodeshow .mini-tree-nodetext a
{
    color:#ffffff;
}
.mini-tree-nodetext a
{
    color:black;
}
.mini-tree-selectedNode .mini-tree-nodeshow
{
    background:url(images/button/pressed.png) repeat-x 50% 50% #e69700;
    border-color:#e69700;
    color:#ffffff;
}
.mini-tree-selectedNode .mini-tree-nodeshow .mini-tree-nodetext a
{
    color:#ffffff;
}
.mini-tree-nodetext
{	
    height:22px;
    line-height:22px;   
    +line-height:23px;   /*ie7*/
}
.mini-tree-nodetitle 
{
    height:24px;
}
.mini-tree-leaf
{
    background-image:url(images/tree/file.gif);
}
.mini-tree-folder
{
    background-image:url(images/tree/folder.gif);
   
}
.mini-tree-expand .mini-tree-folder
{
    background-image:url(images/tree/folder-open.gif);
}

.mini-tree-node-ecicon{
   height:24px
}
/*------------------- pager --------------------*/
.mini-pager
{
    height:auto;
    line-height:30px;
    background:transparent;
    border-color:#cccccc;
}
.mini-pager-left
{
    height:auto;
    line-height:30px;
}
.mini-pager-first
{    
    background:url(images/pager/first.gif) no-repeat;
}
.mini-pager-prev
{
    background-image:url(images/pager/prev.gif);
}
.mini-pager-next
{
    background-image:url(images/pager/next.gif);
}
.mini-pager-last
{
    background-image:url(images/pager/last.gif);
}
.mini-pager-size
{
    position:relative;top:-3px;
}
body .mini-pager-num
{
    height:16px;
}
.mini-pager-right
{
    line-height:32px;
}
.mini-pager .mini-button-iconOnly
{
    padding-top:auto;
    padding-bottom:0;
    height:23px;
}

body .mini-pager-size .mini-buttonedit .mini-buttonedit-border 
{
    border-color:#cccccc;
    background:#f3f3f3;
}
body .mini-pager-size .mini-buttonedit .mini-buttonedit-input
{
   color:#444444;
}

/* tooltip */
.mini-tooltip-inner {    
    border:solid 1px #cccccc;
    border-radius: 0px;    
}
.mini-tooltip .mini-tooltip-arrow 
{
    
}
/*textboxlist*/
.mini-textboxlist-border
{
    height:23px;
    border-style: solid;
    border-width: 1px;
    background:#f3f3f3;    
    border-color: #cccccc  ;       
         
    width:100%;
    cursor:text;
    vertical-align:top;
    border-radius:5px;
}
body .mini-textboxlist-focus .mini-textboxlist-border
{
    background:url(images/button/hover.png) repeat-x 50% 50% #1484e6;
    border-color:#2293f7  ;
}

.mini-textboxlist .mini-textboxlist-input
{
    border: 0; padding: 0; font: 9pt "Lucida Grande", Verdana;
    outline:none;
    width:20px;
    height: 16px;
    margin-top:2px;
    *+margin-top:0px;/*ie7*/ 
    background: transparent  ;  
    color:#ffffff;
}


.mini-textboxlist .mini-textboxlist-item
{
    position: relative; padding: 0 6px; -moz-border-radius: 9px; -webkit-border-radius: 9px; border-radius: 9px;
    border: 1px solid #ffde2e; background: #ffeb80; cursor: default;
    padding-right: 15px;
    height:16px;
    line-height: 16px;
    margin-bottom:2px;
    white-space:nowrap;
}
.mini-textboxlist .mini-textboxlist-item-hover
{
    background:url(images/button/hover.png) repeat-x 50% 50% #eeeeee;
    border-color:#f3f3f3;
    color:#ffffff;
}
.mini-textboxlist .mini-textboxlist-item-selected
{
    border-color: #598BEC; background: #598BEC; color: #fff;
}


.mini-textboxlist-popup-loading
{
    background:url(images/textboxlist/loading.gif) no-repeat 0 5px;
    padding-left:20px;
    line-height:25px;
    display:block;
}
.mini-textboxlist-popup-error
{
    background:url(images/textboxlist/error.gif) no-repeat 0 5px;
    padding-left:20px;
    line-height:25px;
    display:block;
}
.mini-textboxlist-popup-noresult
{
    padding-left:20px;
    line-height:25px;
    display:block;
}

.mini-textboxlist-popup .mini-listbox-item td
{
    white-space:nowrap;
}
/*htmlfile*/
.mini-htmlfile .mini-buttonedit-button
{
    font-size:8pt;
    font-size:9pt\9;
    font-family: Tahoma, Verdana;    
    white-space:nowrap;
        
    border:1px solid #cccccc;
    border-top:#ffffff; 
    border-right:#ffffff;
    background:#f3f3f3;
    color:#444;
    padding:1px;
    width:50px;
    text-align:center;
    line-height:22px;
}

/*---------------------- progressbar -----------------------------*/
.mini-progressbar
{
    border:1px solid #ccc;
    border-radius: 4px; 
}
.mini-progressbar-border
{
    border:1px solid #ccc;
    border-radius: 4px; 
}
.mini-progressbar-bar
{
    background: url("images/button/button.png") repeat-x scroll 0 50% #1484e6;
}
.mini-progressbar-text
{ 
    color:#222; 
}