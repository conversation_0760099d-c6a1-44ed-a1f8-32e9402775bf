/* 
* cardShow 0.1 
* Date: 2010-09-03 
* ���ڿ�Ƭʽ��ʾ
* ��Ҫ����iframe��src���� 
*/ 


(function($){ 
	$.fn.cardShow = function(options){ 
		var defaults = {
					
			}
	//var options = $.extend(defaults, options); 
		$("a[id^=plb_]").click(function(){
		$("a[id^=plb_][class=on]").removeClass();
		$(this).addClass("on");
		var aName = $(this).attr("id");
		var pos = aName.substring(4);	
		$("#myIframe").attr("src",options[parseInt(pos)-1]+'&date='+new Date());
		});
	}; 
	})(jQuery); 