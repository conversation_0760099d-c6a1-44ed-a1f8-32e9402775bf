<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.sinosoft.icmis</groupId>
		<artifactId>ICMIS</artifactId>
		<relativePath>../ICMIS</relativePath>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>ICMIS-LOGON</artifactId>
	<packaging>war</packaging>
	<name>ICMIS-LOGON Maven Webapp</name>
	<url>http://maven.apache.org</url>
	<build>
		<finalName>ICMIS-LOGON</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<configuration>
					<attachClasses>false</attachClasses><!-- 把class打包jar作为附件 -->
					<archiveClasses>false</archiveClasses><!-- 把class打包jar -->
					<failOnMissingWebXml>false</failOnMissingWebXml>
					<includeEmptyDirectories>true</includeEmptyDirectories>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>class</nonFilteredFileExtension>
						<nonFilteredFileExtension>java</nonFilteredFileExtension>
						<nonFilteredFileExtension>jsp</nonFilteredFileExtension>
						<nonFilteredFileExtension>html</nonFilteredFileExtension>
						<nonFilteredFileExtension>htm</nonFilteredFileExtension>
						<nonFilteredFileExtension>xml</nonFilteredFileExtension>
						<nonFilteredFileExtension>properties</nonFilteredFileExtension>
						<nonFilteredFileExtension>js</nonFilteredFileExtension>
						<nonFilteredFileExtension>css</nonFilteredFileExtension>
						<nonFilteredFileExtension>xls</nonFilteredFileExtension>
						<nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
						<nonFilteredFileExtension>doc</nonFilteredFileExtension>
						<nonFilteredFileExtension>docx</nonFilteredFileExtension>
						<nonFilteredFileExtension>ftl</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
					<resourceEncoding>GBK</resourceEncoding>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<dependencies>
		<dependency>
			<groupId>com.sinosoft.whjk</groupId>
			<artifactId>base</artifactId>
			<type>war</type>
		</dependency>

		
		<dependency>
			<groupId>com.sinosoft.whjk</groupId>
			<artifactId>base</artifactId>
			<type>jar</type>
			<classifier>classes</classifier>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.sinosoft.core</groupId>
			<artifactId>sinosoft-util</artifactId>
			<type>war</type>
		</dependency>
		<dependency>
			<groupId>com.sinosoft.core</groupId>
			<artifactId>sinosoft-util</artifactId>
			<type>jar</type>
			<classifier>classes</classifier>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.sinosoft.core</groupId>
			<artifactId>sinosoft-cas</artifactId>
			<type>jar</type>
		</dependency>
	</dependencies>
</project>
