<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="cn.com.sinosoft.os.sealuseapply.model.SealUseApply" table="OS_SEAL_USE_APPLY">
		<id name="id" column="ID" type="java.lang.String">
		</id>
		<property name="taskName" column="TASK_NAME" type="java.lang.String"/>
		<property name="urgencyDegree" column="URGENCY_DEGREE" type="java.lang.String"/>
		<property name="useSealDesc" column="USE_SEAL_DESC" type="java.lang.String"/>
		<property name="useSealNum" column="USE_SEAL_NUM" type="java.lang.String"/>
		<property name="sendToUnit" column="SEND_TO_UNIT" type="java.lang.String"/>
		<property name="piId" column="PI_ID" type="java.lang.String"/>
		<property name="auditState" column="AUDIT_STATE" type="java.lang.String"/>
		<property name="addZone" column="ADD_ZONE" type="java.lang.String"/>
		<property name="addOrg" column="ADD_ORG" type="java.lang.String"/>
		<property name="addDep" column="ADD_DEP" type="java.lang.String"/>
		<property name="addUser" column="ADD_USER" type="java.lang.String"/>
		<property name="addTime" column="ADD_TIME" type="java.util.Date"/>
		<property name="modyZone" column="MODY_ZONE" type="java.lang.String"/>
		<property name="modyOrg" column="MODY_ORG" type="java.lang.String"/>
		<property name="modyDep" column="MODY_DEP" type="java.lang.String"/>
		<property name="modyUser" column="MODY_USER" type="java.lang.String"/>
		<property name="modyTime" column="MODY_TIME" type="java.util.Date"/>
		<property name="state" column="STATE" type="java.lang.String"/>
		<property name="appPerson" column="APP_PERSON" type="java.lang.String"/>
		<property name="dataSource" column="DATA_SOURCE" type="java.lang.String"/>
		<property name="dataModyTime" column="DATA_MODY_TIME" type="java.util.Date"/>
		<property name="sealType" column="SEAL_TYPE" type="java.lang.String"/>
		<property name="tmpModelId" column="TMPMODELID" type="java.lang.String"/>
		<property name="competentDep" column="COMPETENTDEP" type="java.lang.String"/>
		<property name="isSeal" column="IS_SEAL" type="java.lang.String"/>
	</class>
</hibernate-mapping>