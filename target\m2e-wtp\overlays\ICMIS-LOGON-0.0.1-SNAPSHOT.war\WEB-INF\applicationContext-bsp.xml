<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<!-- Only System Bean Definition   -->
	
	<!--pzx 继承HibernateDaoSupport，封装hibernate的增删改查操作-->
	<bean id="referenceDao" class="ie.bsp.modules.reference.dao.ReferenceDao">
		<property name="sessionFactory" ref="sessionFactory" />
	</bean>

	<!--pzx 封装session,包括session_开头的属性或者sessionGroup的处理-->
	<bean id="sessionedParamsInterceptor" class="ie.bsp.webapp.interceptor.SessionedParamsInterceptor"></bean>

	<!--pzx 封装对数据库三张基本表的sql操作，根据code值取出sql，然后把sql拼接好-->
	<bean id="referenceManager" class="ie.bsp.modules.reference.service.ReferenceManager">
		<property name="dataSource" ref="dataSource" />
		<property name="refDao" ref="referenceDao" />

	</bean>
	
	<bean id="baseDAO" class="ie.bsp.frame.dao.hib.BaseDAOHib">
		<property name="sessionFactory">
			<ref bean="sessionFactory"></ref>
		</property>
	</bean>	
	
	<bean id="commonBaseDaoHib" class="ie.bsp.frame.dao.hib.CommonBaseDaoHib" scope="prototype">
		<property name="sessionFactory">
			<ref bean="sessionFactory"></ref>
		</property>
	</bean>	
<!--	导入Excel常用的方法-->
	<bean id="commonBaseExcelDaoHib" class="ie.bsp.frame.dao.hib.CommonBaseExcelDaoHib" scope="prototype">
		<property name="sessionFactory">
			<ref bean="sessionFactory"></ref>
		</property>
	</bean>	
	<!--GIs 返回数据方法-->
	<bean id="commonBaseGisDaoHib" class="ie.bsp.frame.dao.hib.CommonBaseGisDaoHib" scope="prototype">
		<property name="sessionFactory">
			<ref bean="sessionFactory"></ref>
		</property>
	</bean>	
	
	
	
	<bean id="baseService" 
		class="ie.bsp.frame.service.impl.BaseServiceImpl">
		<property name="dao">
			<ref bean="baseDAO" />
		</property>
	</bean>	
	<!--查询的基类，负责查询结果的显示-->
	<bean id="commonQueryAction" class="ie.bsp.modules.commonquery.webaction.CommonQueryAction" scope="prototype">
		<property name="queryMgr" ref="commonQueryMgr" />
	</bean>
	<bean id="dictQueryAction" class="ie.bsp.modules.commonquery.webaction.DictQueryAction" scope="prototype">
		<property name="queryMgr" ref="commonQueryMgr" />
	</bean>
 	<!--封装了查询的sql拼接 -->
	<bean id="commonQueryMgr" class="ie.bsp.modules.commonquery.service.ComQueryManager">
		<property name="dataSource" ref="dataSource" />
		<property name="dao" ref="commonQueryDao" />
		<property name="dictDao" ref="commonDictDao" />
		<property name="pagedSqlParser" ref="pagedSqlParser" />
	</bean>

	
	<!--查询前n条记录,分页的sql-->
	<bean id="pagedSqlParser" class="ie.bsp.modules.commonquery.service.OraclePagedSqlParser" />
	<!-- pzx 局部的查询，没有使用到
	<bean id="busiCustomQueryAction" class="ie.bsp.modules.commonquery.webaction.CommonQueryAction" scope="prototype">
		<property name="queryMgr" ref="busiCustomQueryMgr" />
	</bean>
	
	<bean id="busiCustomQueryMgr" class="ie.bsp.modules.commonquery.service.BusiCustomQueryManager">
		<property name="dataSource" ref="dataSource" />
		<property name="dao" ref="commonQueryDao" />
		<property name="pagedSqlParser" ref="pagedSqlParser" />
	</bean>
	-->
	<bean id="commonQueryDao" class="ie.bsp.modules.commonquery.dao.ComQueryDao">
		<property name="sessionFactory" ref="sessionFactory" />
	</bean>
	<bean id="commonDictDao" class="ie.bsp.modules.commonquery.dao.ComDictDao">
		<property name="sessionFactory" ref="sessionFactory" />
	</bean>
    <bean id="reportAction" class="ie.bsp.modules.report.action.ReportAction" scope="prototype">
		<property name="adapter" ref="dbAdapter" />
	</bean>
	<bean id="reportChartAction" class="ie.bsp.modules.report.action.ReportChartAction" scope="prototype">
		<property name="service">
			<ref bean="reportervice" />
		</property>
	</bean>
	<bean id="reportTestAction" class="ie.bsp.modules.report.action.ReportTestAction" scope="prototype">
		<property name="adapter" ref="dbAdapter" />
	</bean>
	<bean id="commonReportService" class="ie.bsp.modules.report.service.CommonReportService" scope="prototype">
		<property name="adapter" ref="dbAdapter" />
	</bean>

	<bean id="dbAdapter" class="ie.bsp.modules.report.util.DBAdapter" scope="prototype">
		<property name="dataSource" ref="dataSource" />
	</bean>

	<bean id="zoneManager" class="ie.bsp.modules.commcomponent.service.ZoneManager"  scope="prototype">
		<property name="sessionFactory" ref="sessionFactory" />
	</bean>
	
	<bean id="organiseManager" class="ie.bsp.modules.commcomponent.service.OrganiseManager"  scope="prototype">
		<property name="sessionFactory" ref="sessionFactory" />
	</bean>
	<bean id="clientQueryService" class="ie.bsp.ui.jsptag.util.ClientQueryService" scope="prototype">
		<property name="dataSource" ref="dataSource" />
	</bean>
	<bean id="daoQryHelpService" class="ie.bsp.ui.service.DaoQryHelper" scope="prototype">
		<property name="dataSource" ref="dataSource" />
	</bean>	
	<!--用于jqueryEasyUi左菜单传值辅助  -->
	<bean id="jqueryEasyUiHelpAction" class="ie.bsp.frame.action.JqueryEasyUiHelpAction" scope="prototype">
	</bean>
</beans>

