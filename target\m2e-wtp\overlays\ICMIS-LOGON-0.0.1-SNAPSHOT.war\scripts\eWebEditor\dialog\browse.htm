<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var fC=bV["type"];var tu=bV["returnfieldflag"];var ak="";var lQ="";var pJ=new Object();var ni="";var hs="nameasc";var tJ="thumbnail";var dg="";var gL="";var oE="";var fQ=1;switch(fC){case "image":ak=lang["DlgBrowseImage"];lQ=lang["DlgBrowseUploadImage"];break;case "flash":ak=lang["DlgBrowseFlash"];lQ=lang["DlgBrowseUploadFlash"];break;case "media":ak=lang["DlgBrowseMedia"];lQ=lang["DlgBrowseUploadMedia"];break;default:fC="file";ak=lang["DlgBrowseFile"];lQ=lang["DlgBrowseUploadFile"];break;}var Q=lang["DlgBrowse"]+"("+ak+")";document.write("<title>"+Q+"</title>");var iQ=null;function xY(cb,fF,jI,tS){if(iQ==tS){return;}fQ=1;var jb;if(iQ){jb=iQ.childNodes[0];jb.src=jb.src.replace(/diropen/g,"dirclose");iQ.childNodes[1].className="nodetext";}iQ=tS;jb=iQ.childNodes[0];jb.src=jb.src.replace(/dirclose/g,"diropen");iQ.childNodes[1].className="nodetextselected";if(pJ[jI]){ni=jI;rG(cb,fF);ix();}else{$("myIframe").contentWindow.location.replace("../"+config.ServerExt+"/browse."+config.ServerExt+"?action=file&type="+fC+"&dir="+fF+"&style="+ag.StyleName+"&cusdir="+ag.CusDir+"&skey="+ag.SKey+"&foldertype="+cb+"&returnflag="+jI);}};function wH(){$("myIframe").contentWindow.location.replace("../"+config.ServerExt+"/browse."+config.ServerExt+"?action=folder&type="+fC+"&style="+ag.StyleName+"&cusdir="+ag.CusDir+"&skey="+ag.SKey);};function setFolderList(uZ,sP,sM,sO,uW){var html;html="<div class=node><img border=0 src='images/tree/root.gif' align=absmiddle><span class=nodetext>"+ak+"</span></div>";html+=hx(uZ,false,"upload",lQ);switch(fC){case "image":html+=hx(sP,true,"shareimage",lang["DlgBrowseShareImage"]);break;case "flash":html+=hx(sM,true,"shareflash",lang["DlgBrowseShareFlash"]);break;case "media":html+=hx(sO,true,"sharemedia",lang["DlgBrowseShareMedia"]);break;default:html+=hx(sP,false,"shareimage",lang["DlgBrowseShareImage"]);html+=hx(sM,false,"shareflash",lang["DlgBrowseShareFlash"]);html+=hx(sO,false,"sharemedia",lang["DlgBrowseShareMedia"]);html+=hx(uW,true,"shareother",lang["DlgBrowseShareFile"]);break;}$("divFolder").innerHTML=html;};function hx(aN,qW,cb,da){var ny=new Array();var qh=new Array();var html="";var mZ,oj,fM,tF,nn,jh,fF,ga;if(qW){ga="blank.gif";}else{ga="line.gif";}ny[0]="<img border=0 src='images/tree/"+ga+"' align=absmiddle>";qh[0]="";mZ=aN.length;if(mZ>0){if(qW){ga="plus2.gif";}else{ga="plus1.gif";}html+="<div class=node><img border=0 src='images/tree/"+ga+"' align=absmiddle onclick=\"xV('nodediv_"+cb+"',this)\"><span onclick=\"xY('"+cb+"','','span_"+cb+"',this)\"><img border=0 src='images/tree/dirclose.gif' align=absmiddle><span class=nodetext>"+da+"</span></span></div>";html+="<div class=nodediv id='nodediv_"+cb+"' style='display:none'>";for(var i=0;i<mZ;i++){oj=aN[i][0];fM=aN[i][1];tF=aN[i][2];nn=false;if((i+1)<mZ){if(aN[i+1][1]>fM){nn=true;}}if(i>0){if(fM>aN[i-1][1]){html+="<div class=nodediv id='nodediv_"+cb+"_"+(i-1)+"' style='display:none'>";}jh=aN[i-1][1]-fM;if(jh>0){for(var j=0;j<jh;j++){html+="</div>";}}}html+="<div class=node>";for(var j=0;j<fM;j++){html+=ny[j];}if(tF==0){ny[fM]="<img border=0 src='images/tree/line.gif' align=absmiddle>";if(nn){html+="<img border=0 src='images/tree/plus1.gif' align=absmiddle onclick=\"xV('nodediv_"+cb+"_"+i+"',this)\">";}else{html+="<img border=0 src='images/tree/branch1.gif' align=absmiddle>";}}else{ny[fM]="<img border=0 src='images/tree/blank.gif' align=absmiddle>";if(nn){html+="<img border=0 src='images/tree/plus2.gif' align=absmiddle onclick=\"xV('nodediv_"+cb+"_"+i+"',this)\">";}else{html+="<img border=0 src='images/tree/branch2.gif' align=absmiddle>";}}qh[fM]=oj+"/";fF="";for(var j=0;j<fM;j++){fF+=qh[j+1];}html+="<span class=nodetext onclick=\"xY('"+cb+"','"+fF+"','span_"+cb+"_"+i+"',this)\"><img border=0 src='images/tree/dirclose.gif' align=absmiddle><span class=nodetext>"+oj+"</span></span>";html+="</div>";}jh=fM-1;if(jh>0){for(var j=0;j<jh;j++){html+="</div>";}}html+="</div>";}else{if(qW){ga="branch2.gif";}else{ga="branch1.gif";}html+="<div class=node><img border=0 src='images/tree/"+ga+"' align=absmiddle onclick=\"xV('nodediv_"+cb+"',this)\"><span onclick=\"xY('"+cb+"','','span_"+cb+"',this)\"><img border=0 src='images/tree/dirclose.gif' align=absmiddle><span class=nodetext>"+da+"</span></span></div>";html+="<div class=nodediv id='nodediv_"+cb+"' style='display:none'>";html+="</div>";}return html;};function setFileList(jI,cb,fF,vF){pJ[jI]=vF;ni=jI;rG(cb,fF);ix();};function ix(){gL="";dg="";if(ni==""){return;}var aN=pJ[ni];var l=aN.length;if(l==0){$("divFile").innerHTML="";return;}switch(hs){case "nameasc":aN.sort(function(x,y){return x[0].localeCompare(y[0])});break;case "namedesc":aN.sort(function(x,y){return y[0].localeCompare(x[0])});break;case "sizeasc":aN.sort(function(x,y){return parseFloat(x[1])-parseFloat(y[1])});break;case "sizedesc":aN.sort(function(x,y){return parseFloat(y[1])-parseFloat(x[1])});break;case "typeasc":aN.sort(function(x,y){var aL=x[0].substr(x[0].lastIndexOf(".")+1);aL=aL+"          ";aL=aL.substr(0,10);aL=aL+x[0];var bq=y[0].substr(y[0].lastIndexOf(".")+1);bq=bq+"          ";bq=bq.substr(0,10);bq=bq+y[0];return aL.localeCompare(bq)});break;case "typedesc":aN.sort(function(x,y){var aL=y[0].substr(y[0].lastIndexOf(".")+1);aL=aL+"          ";aL=aL.substr(0,10);aL=aL+y[0];var bq=x[0].substr(x[0].lastIndexOf(".")+1);bq=bq+"          ";bq=bq.substr(0,10);bq=bq+x[0];return aL.localeCompare(bq)});break;case "timeasc":aN.sort(function(x,y){return x[2].localeCompare(y[2])});break;case "timedesc":aN.sort(function(x,y){return y[2].localeCompare(x[2])});break;}var gS=1;var rU=$("d_perpagenum").options[$("d_perpagenum").selectedIndex].value;var jU;var jl=0;var eE=l;if(rU!=""){jU=parseInt(rU);if(l%jU==0){gS=l/jU;}else{gS=parseInt(l/jU+1);}if(fQ>gS){fQ=1;}if(fQ>1){jl=(fQ-1)*jU;}if(fQ<gS){eE=fQ*jU;}}var html="";var m,n;switch(tJ){case "detail":var hu,mp;if(hs){hu=hs.substr(0,4);mp=hs.substr(4);}else{hu="name";mp="asc";}var ht="";var sC="";var sB="";var sf="";var sD="";if(mp=="asc"){ht="images/tree/arrowup.gif";}else{ht="images/tree/arrowdown.gif";}ht="&nbsp;<img border=0 align=absmiddle src='"+ht+"'>";if(hu=="name"){sC=ht}if(hu=="size"){sB=ht}if(hu=="type"){sf=ht}if(hu=="time"){sD=ht}html="<table border=0 cellpadding=1 cellspacing=1 width='100%'>";html+="<tr style='BACKGROUND-COLOR: #f0f0f0'>"+"<td width='20' align=center>&nbsp;</td>"+"<td width='185' align=left onclick=\"nF('name')\">"+lang["DlgBrowseSortName"]+sC+"</td>"+"<td width='75' align=left onclick=\"nF('type')\">"+lang["DlgBrowseSortType"]+sf+"</td>"+"<td width='130' align=center onclick=\"nF('time')\">"+lang["DlgBrowseSortTime"]+sD+"</td>"+"<td width='85' align=right onclick=\"nF('size')\">"+lang["DlgBrowseSortSize"]+sB+"</td>"+"</tr>";for(var i=jl;i<eE;i++){html+="<tr align=center id='item_tr_"+i+"' onclick=\"wM('"+i+"')\" onmouseover=\"xg('"+i+"')\" onmouseout=\"xh('"+i+"')\">"+"<td><img border=0 src='../sysimage/icon16/"+ha(aN[i][0])+"' align=absmiddle></td>"+"<td align=left id='item_filename_"+i+"'>"+aN[i][0]+"</td>"+"<td align=left>"+aN[i][0].substr(aN[i][0].lastIndexOf(".")+1)+"</td>"+"<td align=center>"+aN[i][2]+"</td>"+"<td align=right>"+aN[i][1]+"</td>"+"</tr>";}html+="</table>";break;case "thumbnail":html="<table border=0 cellpadding=0 cellspacing=0 width='100%'>";for(var i=jl;i<eE;i++){m=(i+1)%4;if(m==1){html+="<tr>";}html+="<td align=center valign=top width='25%'>"+"<table border=0 cellpadding=0 cellspacing=0 onclick=\"wE('"+i+"')\" style='table-layout:fixed;word-wrap:break-word;'><tr><td>"+"<table border=0 cellpadding=1 cellspacing=3 id='item_table_"+i+"'><tr><td bgcolor=#ffffff>"+"<table border=0 cellspacing=1 cellpadding=0 width=120 height=120 style='border:1px solid #808080; table-layout:fixed;word-wrap:break-word;'><tr><td align=center valign=middle>"+vH(aN[i][0])+"</td></tr></table>"+"</td></tr></table>"+"</td></tr>"+"<tr><td align=center><span id='item_span_"+i+"'>"+aN[i][0]+"</span></td></tr>"+"</table>"+"</td>";if(m==0){html+="</tr>";}}if(m!=0){for(var i=0;i<(4-m);i++){html+="<td width='25%'></td>";}html+="</tr>";}html+="</table>";break;case "icon":html="<table border=0 cellpadding=0 cellspacing=5 width='100%'>";for(var i=jl;i<eE;i++){m=(i+1)%5;if(m==1){html+="<tr>";}html+="<td valign=top width='20%'>"+"<table border=0 cellpadding=0 cellspacing=1 onclick=\"wN('"+i+"')\" style='table-layout:fixed;'>"+"<tr><td align=center><img id='item_img_"+i+"' border=0 align=absmiddle src='../sysimage/icon32/"+ha(aN[i][0])+"'></td></tr>"+"<tr><td align=center height=30 valign=top id='item_td_"+i+"' style='word-wrap:break-word;line-height:1'>"+aN[i][0]+"</td></tr>"+"</table>"+"</td>";if(m==0){html+="</tr>";}}if(m!=0){for(var i=0;i<(5-m);i++){html+="<td width='20%'></td>";}html+="</tr>";}html+="</table>";break;case "list":html="<table border=0 cellpadding=0 cellspacing=3 width='100%'>";for(var i=jl;i<eE;i++){m=(i-jl+1)%3;if(m==1){html+="<tr>";}html+="<td valign=top width='33%' onclick=\"wW('"+i+"')\">"+"<table border=0 cellpadding=0 cellspacing=0 id='item_table_"+i+"'><tr><td><img border=0 align=absmiddle src='../sysimage/icon16/"+ha(aN[i][0])+"'></td><td width=2></td><td id='item_td_"+i+"'>"+aN[i][0]+"</td></tr></table>"+"</td>";if(m==0){html+="</tr>";}}if(m!=0){for(var i=0;i<(3-m);i++){html+="<td width='33%'></td>";}html+="</tr>";}html+="</table>";break;}$("divFile").innerHTML=html;$("d_pagenav").innerHTML=vZ(gS);};function xT(n){fQ=parseInt(n);ix();};function uH(){np("BrowsePerPageNum",$("d_perpagenum").options[$("d_perpagenum").selectedIndex].value);fQ=1;ix();};function vZ(gS){var lv=fQ;if($("d_perpagenum").options[$("d_perpagenum").selectedIndex].value==""){return "";}var html="<table border=0 cellpadding=0 cellspacing=0><tr><td>";if(lv>1){html+="<span style='cursor:hand;color:#0000ff;text-decoration:underline' onclick='xT(1)' title='"+lang["DlgBrowsePageFirst"]+"'>"+lang["DlgBrowsePageFirst"]+"</span> ";html+="<span style='cursor:hand;color:#0000ff;text-decoration:underline' onclick='xT("+(lv-1)+")' title='"+lang["DlgBrowsePagePre"]+"'>"+lang["DlgBrowsePagePre"]+"</span> ";}else{html+="<span style='' title='"+lang["DlgBrowsePageFirst"]+"'>"+lang["DlgBrowsePageFirst"]+"</span> ";html+="<span style='' title='"+lang["DlgBrowsePagePre"]+"'>"+lang["DlgBrowsePagePre"]+"</span> ";}if(lv<gS){html+="<span style='cursor:hand;color:#0000ff;text-decoration:underline' onclick='xT("+(lv+1)+")' title='"+lang["DlgBrowsePageNext"]+"'>"+lang["DlgBrowsePageNext"]+"</span> ";html+="<span style='cursor:hand;color:#0000ff;text-decoration:underline' onclick='xT("+gS+")' title='"+lang["DlgBrowsePageLast"]+"'>"+lang["DlgBrowsePageLast"]+"</span> ";}else{html+="<span style='' title='"+lang["DlgBrowsePageNext"]+"'>"+lang["DlgBrowsePageNext"]+"</span> ";html+="<span style='' title='"+lang["DlgBrowsePageLast"]+"'>"+lang["DlgBrowsePageLast"]+"</span> ";}html+="</td><td>";html+="&nbsp;<select size=1 id=d_pageoption onchange='xT(this.options[this.selectedIndex].value)'>";for(var i=1;i<=gS;i++){if(i==lv){html+="<option value='"+i+"' selected>"+i+"</option>";}else{html+="<option value='"+i+"'>"+i+"</option>";}}html+="</select>";html+="</td></tr></table>";return html;};function rG(cb,fF){var fW="";cb=cb.toLowerCase();switch(cb){case "upload":fW=config.UploadUrl;if(ag.CusDir){fW+=ag.CusDir;if(fW.substr(fW.length-1)!="/"){fW+="/";}}break;case "shareimage":fW="sharefile/image/";break;case "shareflash":fW="sharefile/flash/";break;case "sharemedia":fW="sharefile/media/";break;case "shareother":fW="sharefile/other/";break;}oE=fW+fF;$("d_folderpath").innerHTML="/"+fF;};function wM(ci){var gW,tj;if(dg!=""){gW=document.getElementById("item_tr_"+dg);gW.className="detailout";}gW=document.getElementById("item_tr_"+ci);gW.className="detailselected";tj=document.getElementById("item_filename_"+ci);gL=tj.innerHTML;dg=ci;};function xg(ci){if(dg==ci){return;}else{gW=document.getElementById("item_tr_"+ci);gW.className="detailover";}};function xh(ci){if(dg==ci){return;}else{gW=$("item_tr_"+ci);gW.className="detailout";}};function wN(ci){var hd,na;if(dg!=""){hd=$("item_td_"+dg);hd.className="iconitem";na=$("item_img_"+dg);na.style.filter="";}hd=$("item_td_"+ci);hd.className="iconitemselected";na=$("item_img_"+ci);na.style.filter="gray";gL=hd.innerText;dg=ci;};function wW(ci){var gz,hd;if(dg!=""){gz=$("item_table_"+dg);gz.className="listitem";}gz=$("item_table_"+ci);gz.className="listitemselected";hd=$("item_td_"+ci);gL=hd.innerText;dg=ci;};function wE(ci){var gz,le;if(dg!=""){gz=$("item_table_"+dg);gz.className="thumbnailitem";le=$("item_span_"+dg);le.className="thumbnailitem";}gz=$("item_table_"+ci);gz.className="thumbnailitemselected";le=$("item_span_"+ci);le.className="thumbnailitemselected";gL=le.innerHTML;dg=ci;};function vH(mU){var iR=oE;if(config.BaseUrl!="3"){if(iR.substr(0,1)!="/"){iR="../"+iR;}}else{if(iR.substring(0,5)=="share"){iR="../"+iR;}}var uy="../sysimage/icon16/"+ha(mU);var cy=mU.substr(mU.lastIndexOf(".")+1).toLowerCase();var html="";var sI=iR+mU;if((cy=="gif")||(cy=="jpg")||(cy=="jpeg")||(cy=="bmp")||(cy=="png")){html="<img border=0 src='"+sI+"' onload='ym(this)'>";}else if(cy=="swf"){html=sm(sI,115,115);}else{html="<img border=0 src='"+uy+"' style='position:relative;left:50px;top:50px'>";}return html};function sm(url,w,h){var T='<object width="'+w+'" height="'+h+'" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,0,0">'+'<param name="movie" value="'+url+'">'+'<param name="wmode" value="opaque">'+'<param name="quality" value="autohigh">'+'<embed width="'+w+'" height="'+h+'" src="'+url+'" quality="autohigh" wmode="opaque" type="application/x-shockwave-flash" plugspace="http://www.macromedia.com/shockwave/download/index.cgi?P1_Prod_Version=ShockwaveFlash"></embed>'+'</object>';return T;};function ym(t){var w=t.width;var h=t.height;var ft=115;var fu=115;var sw,dS;if((w>ft)||(h>fu)){var nw=ft/w;var gZ=fu/h;if(nw>gZ){dS=fu;sw=w*gZ;}else{sw=ft;dS=h*nw;}}else{sw=w;dS=h;}t.style.width=sw;t.style.height=dS;};function uL(){var v=$("d_view").options[$("d_view").selectedIndex].value;np("BrowseView",v);tJ=v;ix();};function sx(){var sz=$("d_sort").options[$("d_sort").selectedIndex].value;var gT=$("d_sortward").options[$("d_sortward").selectedIndex].value;np("BrowseSort",sz);np("BrowseSortWard",gT);hs=sz+gT;ix();};function nF(flag){var hu=hs.substr(0,4);var mp=hs.substr(4);var sr=flag;var gT="";if(flag==hu){if(mp=="desc"){gT="asc";}else{gT="desc";}}else{gT="asc"}bb($("d_sort"),sr);bb($("d_sortward"),gT);hs=sr+gT;ix();};function xV(s,o){var t=$(s);if(t.style.display=="none"){t.style.display="";o.src=o.src.replace(/plus/g,"minus");}else{t.style.display="none";o.src=o.src.replace(/minus/g,"plus");}};function ha(url){var gl;gl=url.substr(url.lastIndexOf(".")+1);gl=gl.toUpperCase();var ar;switch(gl){case "TXT":ar="txt.gif";break;case "CHM":ar="chm.gif";break;case "HLP":ar="hlp.gif";break;case "DOC":case "DOCX":ar="doc.gif";break;case "PDF":ar="pdf.gif";break;case "MDB":ar="mdb.gif";break;case "GIF":ar="gif.gif";break;case "JPG":case "JPEG":ar="jpg.gif";break;case "BMP":ar="bmp.gif";break;case "PNG":ar="png.gif";break;case "ICO":ar="ico.gif";break;case "ASP":case "JSP":case "PHP":case "PHP3":ar="code.gif";break;case "JS":case "VBS":ar="js.gif";break;case "ASPX":ar="aspx.gif";break;case "XML":ar="xml.gif";break;case "HTM":case "HTML":case "SHTML":ar="htm.gif";break;case "ZIP":ar="zip.gif";break;case "RAR":ar="rar.gif";break;case "EXE":ar="exe.gif";break;case "AVI":ar="avi.gif";break;case "MPG":case "MPEG":case "ASF":ar="mp.gif";break;case "RA":case "RM":ar="rm.gif";break;case "MP3":ar="mp3.gif";break;case "MID":case "MIDI":ar="mid.gif";break;case "WAV":ar="audio.gif";break;case "XLS":ar="xls.gif";break;case "PPT":case "PPS":ar="ppt.gif";break;case "SWF":ar="swf.gif";break;default:ar="unknow.gif";break;}return ar;};function ok(){if(!gL){alert(lang["DlgBrowseNoSelected"]);return;}var url=oE+gL;if(config.BaseUrl!="3"){url=fH(url);}else{if(url.substring(0,5)=="share"){url=fH(url);}}if(tu){ck.$("d_"+tu).value=url;try{ck.xN()}catch(e){}}else{var html;switch(fC){case "image":html="<img id='eWebEditor_TempElement_Img' border=0 src='"+url+"'>";EWIN.insertHTML(html);var mB=A.F.getElementById("eWebEditor_TempElement_Img");mB.src=url;mB.removeAttribute("id");break;case "flash":html=sm(url,200,200);EWIN.insertHTML(html);break;case "media":html='<EMBED src="'+url+'" width="200" height="200" type="audio/x-pn-realaudio-plugin" autostart="true" controls="IMAGEWINDOW,ControlPanel,StatusBar" console="Clip1"></EMBED>';EWIN.insertHTML(html);break;default:var lL=ha(gL);var rT=fH("sysimage/icon16/"+lL);EWIN.insertHTML("<img id=eWebEditor_TempElement_Img border=0 src='"+rT+"'><a id=eWebEditor_TempElement_Href href='"+url+"' target=_blank>"+gL+"</a>");var fY=A.F.getElementById("eWebEditor_TempElement_Img");fY.src=rT;fY.removeAttribute("id");fY=A.F.getElementById("eWebEditor_TempElement_Href");fY.href=url;fY.removeAttribute("id");break;}}parent.aT();};function ah(){lang.TranslatePage(document);bb($("d_perpagenum"),nr("BrowsePerPageNum"));bb($("d_sort"),nr("BrowseSort"));bb($("d_sortward"),nr("BrowseSortWard"));bb($("d_view"),nr("BrowseView"));parent.bp(Q);wH();}</script> <style>

div.nodediv{padding:0px;margin:0px;WHITE-SPACE:nowrap;}div.node{padding:0px;margin:0px;WHITE-SPACE:nowrap;}.nodetext{color:#000000;background-color:#ffffff}.nodetextselected{color:#ffffff;background-color:#0A246A}.thumbnailitem{color:#000000;background-color:#ffffff}.thumbnailitemselected{color:#ffffff;background-color:#0A246A}.listitem{color:#000000;background-color:#ffffff}.listitemselected{color:#ffffff;background-color:#0A246A}.iconitem{color:#000000;background-color:#ffffff}.iconitemselected{color:#ffffff;background-color:#0A246A}.detailout{}.detailover{background-color:#eeeeee}.detailover td{color:#000000;}.detailselected{background-color:#0A246A}.detailselected td{color:#ffffff;}</style> </HEAD> <BODY onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 width="100%" align=center> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="" align=center> <tr> <td noWrap width="20px"><IMG src="images/tree/folderopen.gif" border=0></td> <td noWrap width="100%"><SPAN id=d_folderpath>/</SPAN></td> <td noWrap width="" align="right"> <table border=0 cellpadding=0 cellspacing=0> <tr> <td noWrap><span lang="DlgBrowsePage"></span>:</td> <td><select id=d_perpagenum size=1 style="width:50px" onchange="uH()"><option value="" lang="DlgBrowsePageAll"></option><option value="20" selected>20</option><option value="40">40</option><option value="60">60</option><option value="80">80</option><option value="100">100</option></select></td> <td>&nbsp;</td> <td noWrap><span lang="DlgBrowseSort"></span>:</td> <td><select id=d_sort size=1 style="width:80px" onchange="sx()"><option value="name" lang="DlgBrowseSortName"><option value="size" lang="DlgBrowseSortSize"><option value="type" lang="DlgBrowseSortType"><option value="time" lang="DlgBrowseSortTime"></select></td> <td><select id=d_sortward size=1 style="width:50px" onchange="sx()"><option value="asc" lang="DlgBrowseSortASC"><option value="desc" lang="DlgBrowseSortDESC"></select></td> <td>&nbsp;</td> <td noWrap><span lang="DlgBrowseView"></span>:</td> <td><select id=d_view size=1 style="width:80px" onchange="uL()"><option value="detail" lang="DlgBrowseViewDetails"><option value="thumbnail" lang="DlgBrowseViewThumbnails" selected><option value="icon" lang="DlgBrowseViewIcons"><option value="list" lang="DlgBrowseViewList"></select></td> </table> </td> </tr> </table> </td></tr> <tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td vAlign=top noWrap> <DIV id=divFolder style="BORDER-RIGHT:1.5pt inset;PADDING-RIGHT:0px;BORDER-TOP:1.5pt inset;PADDING-LEFT:0px;PADDING-BOTTOM:0px;OVERFLOW:auto;BORDER-LEFT:1.5pt inset;WIDTH:150px;PADDING-TOP:0px;BORDER-BOTTOM:1.5pt inset;HEIGHT:350px;BACKGROUND-COLOR:white"> </DIV> </td> <td width=10>&nbsp; </td> <td vAlign=top> <DIV id=divFile style="BORDER-RIGHT:1.5pt inset;PADDING-RIGHT:0px;BORDER-TOP:1.5pt inset;PADDING-LEFT:0px;PADDING-BOTTOM:0px;VERTICAL-ALIGN:top;OVERFLOW:auto;BORDER-LEFT:1.5pt inset;WIDTH:540px;PADDING-TOP:0px;BORDER-BOTTOM:1.5pt inset;HEIGHT:350px;BACKGROUND-COLOR:white"> </DIV> </td> </tr> <tr><td colspan=3 height=5></td></tr> <tr> <td></td> <td></td> <td> <table border=0 cellpadding=0 cellspacing=0 width="100%"> <tr> <td id=d_pagenav></td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel></td> </tr> </table> </td> </tr> </table> </td></tr> </table> </td></tr></table> <iframe id=myIframe src="blank.htm" width="0" height="0" frameborder=0></iframe> </body></html>