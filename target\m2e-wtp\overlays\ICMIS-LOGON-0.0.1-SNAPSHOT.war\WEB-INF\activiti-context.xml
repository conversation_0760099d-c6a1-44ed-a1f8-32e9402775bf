<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd  
                           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd  
                           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">


<!-- 	<bean id="transactionManager" -->
<!-- 		class="org.springframework.jdbc.datasource.DataSourceTransactionManager"> -->
<!-- 		<property name="dataSource" ref="dataSource" /> -->
<!-- 	</bean> -->
	<!-- spring负责创建流程引擎的配置文件 -->
	<bean id="processEngineConfiguration"
		class="ie.bsp.MySpringProcessEngineConfiguration">
		<!-- 数据源 -->
		<property name="dataSource1" ref="dataSource" />
		<!-- 配置事务管理器，统一事务 -->
		<property name="transactionManager" ref="txManager" />

	</bean>
	<!-- 创建流程引擎对象 -->
	<bean id="processEngine" class="org.activiti.spring.ProcessEngineFactoryBean">
		<property name="processEngineConfiguration" ref="processEngineConfiguration" />
	</bean>

	<!-- 相当于下面的代码 RepositoryServicie repositoryService = processEngine.getRepositoryService(); 
		RuntimeServicie repositoryService = processEngine.getRuntimeServicie(); TaskServicie 
		taskServicie = processEngine.getTaskServicie(); HistoryServicie historyServicie 
		= processEngine.getHistoryServicie(); -->
	<!-- 由流程引擎对象，提供的方法，创建项目中使用的Activiti工作流的Service -->
	<bean id="repositoryService" factory-bean="processEngine"
		factory-method="getRepositoryService" />
	<bean id="runtimeService" factory-bean="processEngine"
		factory-method="getRuntimeService" />
	<bean id="taskService" factory-bean="processEngine"
		factory-method="getTaskService" />
	<bean id="historyService" factory-bean="processEngine"
		factory-method="getHistoryService" />
	<bean id="formService" factory-bean="processEngine"
		factory-method="getFormService" />
</beans>  