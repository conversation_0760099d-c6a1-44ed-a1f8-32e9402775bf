config.FixWidth = "";					//限宽模式宽度
config.UploadUrl = "../uploads/";		//上传目录
config.InitMode = "EDIT";				//默认设计 [编辑模式EDIT,文本模式TEXT,预览模式VIEW]
config.AutoDetectPaste = "1";			//高级粘贴自动检测 [启用强制纯文本粘贴2,启用高级粘贴1,不启用0]
config.BaseUrl = "1";					//路径模式 [相对路径0,绝对根路径1,绝对全路径2]
config.BaseHref = "";					//显示路径 [显示内容页所存放路径，必须以/开头]
config.AutoRemote = "1";				//远程文件 [自动上传1,不自动上传0]
config.ShowBorder = "0";				//显示表格虚框 [默认显示1,默认不显示0]
config.StateFlag = "1";					//显示状态栏及按钮 [状态栏1]
config.SBCode = "1";					//显示状态栏及按钮 [代码1]
config.SBEdit = "1";					//显示状态栏及按钮 [编辑1]
config.SBText = "1";					//显示状态栏及按钮 [文本1]
config.SBView = "1";					//显示状态栏及按钮 [浏览1]
config.SBSize = "1";					//显示状态栏及按钮 [缩放1]
config.EnterMode = "1";					//回车换行模式 [Enter输入<P>Shift+Enter输入<BR>1,Enter输入<BR>Shift+Enter输入<P>2]
config.Skin = "office2003";				//界面皮肤目录 [office2003]
config.AutoDetectLanguage = "1";		//自动语言检测 [自动检测1,不自动检测0]
config.DefaultLanguage = "zh-cn";		//默认语言 [简体中文zh-cn,繁体中文zh-tw,英文en,日语ja,西班牙语es,俄语ru,德语de,法语fr,意大利语it,瑞典语sv,荷兰语nl,丹麦语da]
config.AllowBrowse = "1";				//上传文件浏览 [开启1,关闭0]
config.AllowImageSize = "100";			//上传图片大小 [数字型，单位KB]
config.AllowFlashSize = "100";			//上传FLASH大小 [数字型，单位KB]
config.AllowMediaSize = "100";			//上传Media大小 [数字型，单位KB]
config.AllowFileSize = "500";			//上传File大小 [数字型，单位KB]
config.AllowRemoteSize = "100";			//上传远程限制 [数字型，单位KB]
config.AllowLocalSize = "100";			//自动上传本地文件	 [数字型，单位KB]
config.AllowImageExt = "gif|jpg|jpeg|bmp|png";		//图片类型上传允可
config.AllowFlashExt = "swf";						//动画类型上传允可
config.AllowMediaExt = "rm|flv|wmv|asf|mov|mpg|mpeg|avi|mp3|wav|mid|midi|ra|wma";				//视频类型上传允可
config.AllowFileExt = "rar|zip|pdf|doc|xls|ppt|docx|xlsx|pptx|chm|hlp";										//文件类型上传允可
config.AllowRemoteExt = "gif|jpg|bmp";															//远程类型上传允可
config.AllowLocalExt = "gif|jpg|bmp|wmz|png";													//自动上传允可
config.AreaCssMode = "0";				//编辑区CSS模式 [常规模式0,Word导入模式1]
config.SLTFlag = "0";					//缩略图使用状态 [不使用0,使用1,模拟使用,不生成小图,改大图显示宽高2]
config.SLTMinSize = "500";				//大于 [图形的长度只有达到此最小长度要求时才会生成缩略图，数字型]
config.SLTOkSize = "300";				//缩略图生成长度 [生成的缩略图长度值，数字型]
config.SLTMode = "0";					//缩略图生成模式 [大小图:显示小图,链到大图0,大小图:显示大图1,只生成小图2]
config.SLTCheckFlag = "0";				//缩略图长度条件 [宽0,高1,宽或高2]
config.SYWZFlag = "0";					//文字水印使用状态 不使用0,使用1,前台用户控制2
config.SYTPFlag = "0";					//图片水印使用状态 [不使用0,使用1,前台用户控制2]
config.FileNameMode = "0";				//
config.PaginationMode = "1";			//分页符模式 [不启用0,启用：标准分页符1,启用：自定义分页符2
config.PaginationKey = "{page}";		//自定义分页符关键字 [{page}]
config.PaginationAutoFlag = "0";		//提交内容自动分页 [不启用0,部分启用,内容中已有分页时不启用1,完全启用,内容中已有的分页会被替换2]
config.PaginationAutoNum = "2000";		//自动分页字数 [当启用自动分页时，将依此值进行自动分页2000]
config.SParams = "";					//
config.SpaceSize = "";					//总上传空间限制 [数字型，单位MB，不限制请留空]
config.MFUBlockSize = "100";			//批量上传分块大小 [数字型，单位KB]
config.MFUEnable = "0";					//批量上传 [开启1,关闭0]
config.CodeFormat = "2";				//代码格式化 [关闭0,启用:缩进1空格1启用:缩进2空格2,...]
config.TB2Flag = "1";					//非编辑模式工具栏 [工具栏1]
config.TB2Mode = "1";					//非编辑模式工具栏 [编辑模式转换按钮组1]
config.TB2Max = "1";					//非编辑模式工具栏 [最大化1]
config.ShowBlock = "0";					//显示区块 [默认显示1,默认不显示0]
config.L = "ok";
config.ServerExt = "asp";

config.Toolbars = [
	["TBHandle", "FormatBlock", "FontName", "FontSize", "Cut", "Copy", "Paste", "PasteText", /*"PasteWord",*/ "TBSep", "Delete", "RemoveFormat", "TBSep", "FindReplace", "TBSep", "UnDo", "ReDo", "TBSep", "SelectAll", "UnSelect"],
	["TBHandle", "Bold", "Italic", "UnderLine", "StrikeThrough", "SuperScript", "SubScript", "TBSep", "JustifyLeft", "JustifyCenter", "JustifyRight", "JustifyFull","TBSep", "OrderedList", "UnOrderedList", "Indent", "Outdent", "TBSep", "ForeColor", "BackColor", "BgColor", "BackImage", "TBSep", "Fieldset", "HorizontalRule", "Marquee", "TBSep", "CreateLink", "Unlink", "Map", "Anchor"],
	["TBHandle", "Code", "EQ", "TBSep", "Big", "Small", "TBSep", "Image", "Flash", "Media", "File",/* "GalleryMenu", "TBSep", "RemoteUpload", "LocalUpload","ImportWord", "ImportExcel", "Capture", */ "TBSep", "TableMenu", "FormMenu", "TBSep", "QuickFormat", "TBSep", "Template","Symbol", "Emot", "Art", "Excel", "PrintBreak", "NowDate", "NowTime", "TBSep", "Quote", "ShowBorders", "TBSep", "Pagination", "PaginationInsert"],
	["TBHandle", "absolutePosition", "zIndexBackward", "zIndexForward", "TBSep", "Iframe", "TBSep", "BR", "Paragraph", "ParagraphAttr", "TBSep", "UpperCase", "LowerCase", "TBSep", "Print", "TBSep", "ZoomMenu", "Maximize", "TBSep", "About"]
];