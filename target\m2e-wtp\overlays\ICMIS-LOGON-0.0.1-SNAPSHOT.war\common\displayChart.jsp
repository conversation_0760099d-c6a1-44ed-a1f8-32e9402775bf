<%@ page import="java.util.ArrayList"%>
<%@ page import="org.jfree.chart.ChartUtilities"%>
<%@ page import="org.jfree.chart.JFreeChart"%>
<%
response.reset();
response.setContentType("image/jpeg");
ServletOutputStream outStream = response.getOutputStream();
ArrayList dataList = (ArrayList)session.getAttribute("chartlist");
System.out.println("dataList======"+dataList.size());
JFreeChart chartObj = (JFreeChart)dataList.get(0);
ChartUtilities.writeChartAsJPEG(outStream,chartObj,800,600);
%>