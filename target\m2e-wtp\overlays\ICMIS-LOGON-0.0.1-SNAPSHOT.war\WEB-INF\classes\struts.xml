<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
    "http://struts.apache.org/dtds/struts-2.0.dtd">

<struts>
	<!-- Constants -->
	<constant name="struts.devMode" value="false" />
	<constant name="struts.i18n.encoding" value="GBK" />
	<constant name="struts.action.extension" value="ac" />
	<constant name="struts.objectFactory" value="spring" />
	<constant name="struts.custom.i18n.resources" value="ApplicationResources*,errors" />
	<constant name="struts.codebehind.pathPrefix" value="/WEB-INF/pages/" />
	<constant name="struts.enable.SlashesInActionNames" value="true" />
	<constant name="struts.enable.DynamicMethodInvocation" value="false" />
	<constant name="struts.objectFactory.spring.autoWire" value="name" />
	<constant name="struts.ui.theme" value="css_xhtml" />
	<constant name="struts.multipart.maxSize" value="40000000000000" />
	<constant name="struts.multipart.saveDir" value="/tmp" />
	<constant name="struts.multipart.parser" value="ie.bsp.util.progress.RefactorMultiPartRequest" />
	<constant name="struts.multipart.handler" value="ie.bsp.util.progress.RefactorMultiPartRequest" />
	<include file="struts-default.xml" />
	<package name="bsp-default" extends="struts-default">
		<interceptors>
			<!-- Interceptor to handle allowing only admins to certain actions -->
			<interceptor name="sessionedParams"
				class="ie.bsp.webapp.interceptor.SessionedParamsInterceptor" />
			<!-- Copied from struts-default.xml and changed validation exclude methods -->
			<interceptor-stack name="defaultStack">
				<interceptor-ref name="exception" />
				<interceptor-ref name="alias" />
				<interceptor-ref name="servletConfig" />
				<interceptor-ref name="params" />
				<interceptor-ref name="prepare" />
				<interceptor-ref name="i18n" />
				<interceptor-ref name="chain" />
				<interceptor-ref name="debugging" />
				<interceptor-ref name="profiling" />
				<interceptor-ref name="scopedModelDriven" />
				<interceptor-ref name="modelDriven" />
				<interceptor-ref name="fileUpload" />
				<interceptor-ref name="checkbox" />
				<interceptor-ref name="staticParams" />
				<interceptor-ref name="params">
					<param name="excludeParams">dojo/..*</param>
				</interceptor-ref>
				<interceptor-ref name="conversionError" />
				<interceptor-ref name="validation">
					<param name="excludeMethods">
						cancel,execute,delete,edit,list
					</param>
				</interceptor-ref>
				<interceptor-ref name="workflow">
					<param name="excludeMethods">
						input,back,cancel,browse
					</param>
				</interceptor-ref>

				<interceptor-ref name="sessionedParams" />
				<interceptor-ref name="token">
					<param name="includeMethods">
						addParentSubmit,edtParentSubmit,audit
					</param>
				</interceptor-ref>
			</interceptor-stack>
		</interceptors>
		<global-results>
			<result name="logon" type="redirect">/login/login.jsp</result>
			<result name="logout" type="redirect">/login/logout.jsp</result>
			<result name="commonErrorPage">
				/common/commonError.jsp
			</result>
			<result name="invalid.token">
				/common/resubmitError.jsp
			</result>
			<result name="echarsSuccess">
				/common/echarts.jsp
			</result>
		</global-results>
		<global-exception-mappings>
			<exception-mapping exception="ie.bsp.frame.exception.GeneralException"
				result="commonErrorPage" />

		</global-exception-mappings>
		<action name="logon" class="cn.com.sinosoft.login.action.LogonAction">
			<result name="failure">/random</result>
			<result name="success" type="redirect">/index.jsp</result>
		</action>
		<action name="main" class="cn.com.sinosoft.login.action.MainAction">
			<result name="failure">/random</result>
		</action>
		<action name="attention" class="cn.com.sinosoft.login.action.AttentionAction">
			<result name="failure">/random</result>
		</action>
		<action name="dictQuery" class="dictQueryAction"></action>
		<action name="jqueryEasyUiHelpAction" class="jqueryEasyUiHelpAction"
			method="menuHelper"></action>
	</package>
	<!-- 从sinosoft-core包里引入的xml -->
	<include file="struts-sinosoft-core-include.xml" />
 	<include file="struts-base-include.xml" /> 
</struts>
