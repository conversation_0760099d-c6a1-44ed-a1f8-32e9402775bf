<ehcache>
    
    <!-- Sets the path to the directory where cache .data files are created.
    
    If the path is a Java System Property it is replaced by
    its value in the running VM.
    
    The following properties are translated:
    user.home - User's home directory
    user.dir - User's current working directory
    java.io.tmpdir - Default temp file path -->
    <diskStore path="java.io.tmpdir"/>
    
    
    <!--Default Cache configuration. These will applied to caches programmatically created through
    the CacheManager.
    
    The following attributes are required:
    
    maxElementsInMemory            - Sets the maximum number of objects that will be created in memory
    eternal                        - Sets whether elements are eternal. If eternal,  timeouts are ignored and the
    element is never expired.
    overflowToDisk                 - Sets whether elements can overflow to disk when the in-memory cache
    has reached the maxInMemory limit.
    
    The following attributes are optional:
    timeToIdleSeconds              - Sets the time to idle for an element before it expires.
    i.e. The maximum amount of time between accesses before an element expires
    Is only used if the element is not eternal.
    Optional attribute. A value of 0 means that an Element can idle for infinity.
    The default value is 0.
    timeToLiveSeconds              - Sets the time to live for an element before it expires.
    i.e. The maximum time between creation time and when an element expires.
    Is only used if the element is not eternal.
    Optional attribute. A value of 0 means that and <PERSON><PERSON> can live for infinity.
    The default value is 0.
    diskPersistent                 - Whether the disk store persists between restarts of the Virtual Machine.
    The default value is false.
    diskExpiryThreadIntervalSeconds- The number of seconds between runs of the disk expiry thread. The default value
    is 120 seconds.
    -->
    
    <defaultCache
     maxElementsInMemory="10000"
     eternal="false"
     overflowToDisk="true"
     timeToIdleSeconds="120"
     timeToLiveSeconds="120"
     diskPersistent="false"
     diskExpiryThreadIntervalSeconds="120"/>
    <cache name="org.hibernate.cache.StandardQueryCache"
     maxElementsInMemory="10000"
     eternal="false"
     timeToIdleSeconds="300"
     timeToLiveSeconds="4200"
     overflowToDisk="true"
    />
    
    <!-- Sample cache named sampleCache2
    This cache contains 1000 elements. Elements will always be held in memory.
    They are not expired. -->
    
    <cache name="org.hibernate.cache.UpdateTimestampsCache"
     maxElementsInMemory="5000"
     eternal="true"
     timeToIdleSeconds="0"
     timeToLiveSeconds="0"
     overflowToDisk="false"
    />
    <!-- See http://ehcache.sourceforge.net/documentation/#mozTocId258426 for how to configure caching for your objects -->
</ehcache>