<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ=cc["action"];var bm=lang["DlgPasteGecko"];if(aJ=="text"){bm+="("+lang["DlgPasteGeckoText"]+")";}else if(aJ=="word"){bm+="(Word)";}document.write("<title>"+bm+"</title>");function ok(){var V="";if(aJ=="text"){V=fn($("d_text").value);}else{V=$("d_iframe").contentWindow.document.body.innerHTML;if(aJ=="word"){V=BF(V);}}EWIN.insertHTML(V);parent.bV();};function BF(V){V=V.replace(/<!--\[if\s(gte mso [0-9]+|gte vml [0-9]+)\]>[\s\S]*?<!\[endif\]-->/gi,"");//-->
V=V.replace(/<!--\[if\s!\w[^\]]*?\]-->([\s\S]*?)<!--\[endif\]-->/gi,"$1");V=V.replace(/<(\w[^>]*?)\sclass\s*=\s*\"[^>]*?\"([^>]*)>/gi,"<$1$2>");V=V.replace(/<(\w[^>]*?)\sclass\s*=\s*\'[^>]*?\'([^>]*)>/gi,"<$1$2>");V=V.replace(/<(\w[^>]*?)\sclass\s*=\s*[^\s>\'\"]*?([^>]*)>/gi,"<$1$2>");V=V.replace(/<(\w[^>]*?)\slang\s*=\s*[^\s>]*([^>]*)>/gi,"<$1$2>");V=V.replace(/<\\?\?xml[^>]*>/gi,"");V=V.replace(/<\/?\w+:[^>]*>/gi,"");V=Ea(V,"<span(?=[\\s>])[^>]*>\s*</span>","");//-->
V=Ea(V,"(<\\w[^>]*?style\\s*=\\s*\"[^\\\"]*?)&quot;([^>]*?>)","$1'$2");//-->
V=Ea(V,"(<\\w[^>]*?style\\s*=\\s*\"[^\\\"]*?)(mso-[a-zA-Z\-]+?:[^;\\\">]+)([^>]*?>)","$1$3");//-->
V=Ea(V,"(<\\w[^>]*?style\\s*=\\s*\"[^\\\"]*?)[\\s\\;]+\\;\\s*([^>]*?>)","$1;$2");//-->
V=V.replace(/(<\w[^>]*?style\s*=\s*\")[\s;]+([^>]*?>)/gi,"$1$2");V=V.replace(/(<\w[^>]*?)style\s*=\s*\"\s*\"([^>]*?>)/gi,"$1$2");return V;};function Ea(BE,Cg,kK){var um=new RegExp(Cg,'gi');var aI="";while(true){aI=BE.replace(um,kK);if(aI==BE){break;}else{BE=aI;}}return aI;};function fn(V){if(V==null){return "";}V=V.replace(/&/gi,"&amp;");V=V.replace(/\"/gi,"&quot;");V=V.replace(/</gi,"&lt;");V=V.replace(/>/gi,"&gt;");V=V.replace(/ (?= )/gi,"&nbsp;");V=V.replace(/\n/gi,"<br>");return V;};function aq(){lang.ag(document);parent.ar(bm);if(aJ=="text"){$("d_text").focus();}else{var vY=$("d_iframe").contentWindow;vY.document.designMode='on';vY.focus();}} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <table border=0 cellpadding=0 cellspacing=3> <script type="text/javascript"> if(aJ=="text"){document.write("<tr><td>"+lang["DlgPasteGeckoMsg"]+"</td></tr>");document.write("<tr><td><textarea id=d_text style='width:400px;height:240px;'></textarea></td></tr>");}else{document.write("<tr><td>"+lang["DlgPasteGeckoSecurity"]+"<br>"+lang["DlgPasteGeckoMsg"]+"</td></tr>");document.write("<tr><td><iframe id=d_iframe style='width:400px;height:240px;'></iframe></td></tr>");} </script> </table> </tr> <tr><td height=5></td></tr> <tr> <td> <table border=0 cellpadding=0 cellspacing=0 width="100%"> <tr> <td noWrap align=right>&nbsp; <input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel> </tr> </table> </td> </tr> </table> </td></tr></table> </body> </html> 