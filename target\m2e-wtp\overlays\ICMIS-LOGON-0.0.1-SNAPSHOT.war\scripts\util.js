﻿// Copyright 2014 Sinosoft Inc. All Rights Reserved.

/**
 * @fileoverview 公共类,供其他页面重复调用。依赖于 /scripts/miniui/miniui.js
 * <AUTHOR> (lfw)
 */

/**
 * 定义命名空间
 * 
 * @param {name}
 *            命名空间名称，例如 com.sinosoft.util
 */
function DefineNamespace(name) {
	if (!name) {
		return null;
	}
	var level = name.split(".");
	var floder = window[level[0]] = window[level[0]] || {};
	for (var i = 1; i < level.length; i++) {
		floder = floder[level[i]] = floder[level[i]] || {};
	}
}

DefineNamespace("com.sinosoft.util"); // 自定义命名空间 com.sinosoft.util

/**
 * 一个测试的Util函数
 */
com.sinosoft.util.TestUtil = function() {
	alert("test");
}
// 附件对象
var attchmentUtil = {
	fileMaxSize : 10485760, // 限制单个上传文件大小：10MB
	fileFiter : [], // 限制上传文件类型
	minFileCount : 2,// 初始化显示的文件上传空间数量
	chooseFileName : "",//当前选择的文件名
	change : function(){} //附件选择后触发事件
}
var util_this_page_flag = "util.js";

// 关闭弹出窗口
function CloseWindow(action) {
	if (window.CloseOwnerWindow) {
		return window.CloseOwnerWindow(action);
	} else {
		window.close();
	}
}

// 控件只读模式
function labelModelSingle(c) {
	if (c.setReadOnly) {
		c.setReadOnly(true);
	}// 只读
	if (c.addCls) {
		c.addCls("asLabel");
	} // 增加asLabel外观
}
// 控件正常模式
function normalModelSingle(c) {
	if (c.setReadOnly) {
		c.setReadOnly(false);
	}
	if (c.removeCls) {
		c.removeCls("asLabel");
	}
}

/**
 * 将form表单中的所有控件都设置为只读，通常在查看页面中使用此函数
 * 
 * @param {string}
 *            f 表单名称
 * @return
 */
function labelModel(/* String */f) {
	var form = new mini.Form(f); // 获取表单
	var fields = form.getFields(); // 获取表单中字段集合
	for (var i = 0, l = fields.length; i < l; i++) { // 遍历fields
		var c = fields[i]; // 单个fields对象
		if (c.setReadOnly) { // 判断该字段是否支持 readOnly
			c.setReadOnly(true);// 设置为只读
		}
		if (c.setIsValid) {// 判断该字段是否支持 isValid
			c.setIsValid(true); // 设置为验证通过（因为已经是只读，所以去除所有验证，即去除错误提示）
		}
		if (c.addCls) {// 判断该字段是否支持 addCls
			c.addCls("asLabel"); // 增加asLabel外观（只读外观），asLabel样式位于：
			// /css/tab-1.css
		}
	}
}
// textboxlist在输入数据时对key值进行url转码
function TEXTBOXLIST_onbeforeload(e) {
	if ($.trim(e.params.key) != "") {
		e.params.key = encodeURIComponent($.trim(e.params.key));
	}
}
var util_waitClick_messageid;
// 加载时显示loading
function searching() {
	util_waitClick_messageid = mini
			.loading("\u67e5\u8be2\u4e2d\uff0c\u8bf7\u7a0d\u540e\u2026\u2026");
}
// 提交表单时显示遮罩
function waitClick() {
	util_waitClick_messageid = mini.loading(
			"\u4fdd\u5b58\u4e2d\uff0c\u8bf7\u7a0d\u540e\u2026\u2026",
			"\u4fdd\u5b58");
}
// 提交带附件的表单时显示遮罩和进度条
function waitClickForUpload() {
	mini.open({
		title : '上传进度',
		url : bootPATH.substring(0, bootPATH.length - 8)
				+ 'common/progress.jsp',
		showModal : true,
		showCloseButton : false,
		allowResize : false,
		showCollapseButton : false,
		collapseOnTitleClick : false,
		width : 260,
		height : 170,
		allowDrag : false,
		onload: function () {//弹出页面加载完成
           var iframe = this.getIFrameEl(); 
           iframe.showFileUpLoadProgress();
        },
		ondestroy : function(action) {
			if (action == "ok") {
				waitClick();
			}
		}
	});
}
// 提交失败后清除loading,提示错误
function clearWait() {
	if (util_waitClick_messageid) {
		mini.hideMessageBox(util_waitClick_messageid);
	}
	location.href = bootPATH.substring(0, bootPATH.length - 8)
			+ 'common/commonError.jsp';
}
// 提交成功后清除loading
function clearWaitSuccess() {
	if (util_waitClick_messageid) {
		mini.hideMessageBox(util_waitClick_messageid);
	}

}
/* 是否英文 */
function isEnglish(v) {
	if (v == "") {
		return true;
	}
	var re = new RegExp("^[a-zA-Z_]+$");
	if (re.test(v)) {
		return true;
	}
	return false;
}
/* 是否英文+数字 */
function isEnglishAndNumber(v) {
	if (v == "") {
		return true;
	}
	var re = new RegExp("^[0-9a-zA-Z_-]+$");
	if (re.test(v)) {
		return true;
	}
	return false;
}
/* 是否汉字 */
function isChinese(v) {
	if (v == "") {
		return true;
	}
	var re = new RegExp("^[\u4e00-\u9fa5]+$");
	if (re.test(v)) {
		return true;
	}
	return false;
}
/* 是否手机号码 */
function isHandSet(v) {
	if (v == "") {
		return true;
	}
	// if (/^1[3|4|5|8][0-9]\d{8}$/.test(v)) {
	if (/^[0-9]\d{10}$/.test(v)) {
		return true;
	}
	return false;
}
/* 是否电话号码 */
function isTelPhone(v) {
	if (v == "") {
		return true;
	}

	// if (/(\(\d{3,4}\)|(\d{3,4}-))\d{7,14}$/.test(v)) {

	/*
	 * 电话号码正则表达式（支持手机号码，3-4位区号，7-8位直播号码，1－4位分机号） 匹配格式： 11位手机号码
	 * 3-4位区号，7-8位直播号码，1－4位分机号 如：12345678901、1234-12345678-1234
	 * if(/((\d{11})|^((\d{7,8})|(\d{4}|\d{3})-(\d{7,8})|(\d{4}|\d{3})-(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d{1})|(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d{1}))$)/.test(v)){
	 * return true; }
	 */
	if (/(^((\d{4}|\d{3})-(\d{7,8})|(\d{4}|\d{3})-(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d{1}))$)/
			.test(v)) {
		return true;
	}
	return false;
}
/* 自定义vtype */
mini.VTypes["englishErrorText"] = "\u8bf7\u8f93\u5165\u82f1\u6587";
mini.VTypes["english"] = function(v) {
	if (v == "") {
		return true;
	}
	var re = new RegExp("^[a-zA-Z_]+$");
	if (re.test(v)) {
		return true;
	}
	return false;
};
/* 必须输入英文 */
function onEnglishValidation(e, name) {
	if (e.isValid) {
		if (isEnglish(e.value) == false) {
			e.errorText = "[" + name + "] \u5fc5\u987b\u8f93\u5165\u82f1\u6587";
			e.isValid = false;
		}
	}
}
/* 手机号码或者座机号码验证 */
function onHandSetOrTelPhoneValidation(e, name) {
	if (e.isValid) {
		if (!isHandSet(e.value) && !isTelPhone(e.value)) {
			e.errorText = "[" + name
					+ "] 电话号码格式不正确，手机号应为11位手机号码，座机应为[区号-直播号]或[区号-直播号-分机号]";
			e.isValid = false;
		}
	}
}

/* 手机号码验证 */
function onHandSetValidation(e, name) {
	if (e.isValid) {
		if (isHandSet(e.value) == false) {
			e.errorText = "[" + name
					+ "] \u8bf7\u8f93\u5165\u624b\u673a\u53f7\u7801";
			e.isValid = false;
		}
	}
}
/* 电话号码验证 */
function onTelPhoneValidation(e, name) {
	if (e.isValid) {
		if (isTelPhone(e.value) == false) {
			e.errorText = "[" + name + "] 电话号码格式不正确，应为[区号-直播号]或[区号-直播号-分机号]";
			e.isValid = false;
		}
	}
}
/* 必须输入英文+数字 */
function onEnglishAndNumberValidation(e, name) {
	if (e.isValid) {
		if (isEnglishAndNumber(e.value) == false) {
			e.errorText = "[" + name
					+ "] 只能输入英文、数字或下划线";
			e.isValid = false;
		}
	}
}
/* 必须输入中文 */
function onChineseValidation(e, name) {
	if (e.isValid) {
		if (isChinese(e.value) == false) {
			e.errorText = "[" + name + "] \u5fc5\u987b\u8f93\u5165\u4e2d\u6587";
			e.isValid = false;
		}
	}
}
/* 必须输入15~18位数字 */
function onIDCardsValidation(e, name) {
	if (e.isValid) {
		if (e.value.length == 0) {
			return true;
		}
		var pattern = /\d*/;
		if (e.value.length < 15 || e.value.length > 18
				|| pattern.test(e.value) == false) {
			e.errorText = "[" + name
					+ "] \u5fc5\u987b\u8f93\u516515~18\u4f4d\u6570\u5b57";
			e.isValid = false;
		}
	}
}
// 身份证号验证
function idnumberFn(e, name) {
	if (e.isValid) {
		var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
		if (reg.test(e.value) === false) {
			e.errorText = "["
			name + "] 为15位或者18位，15位时全为数字，18位前17位为数字，" + "最后一位是校验位，可能为数字或字符X";
			e.isValid = false;
		}

	}
}

/**
 * 密码强度验证：<br />
 * 低：6-24位 <br />
 * 中：必须包含数字、字母，8-24位<br />
 * 高：必须包含数字、字母、特殊字符，9-24位
 * 
 * @param value
 *            密码
 * @param lv
 *            密码级别
 * @returns {Boolean}
 */
function isPassword(value, lv) {
	var result = false;
	if (value == "") {// 证书登录的时候无密码，直接返回true
		result = true;
	} else {
		if (lv == "low") {
			if (/^.{6,24}$/.test(value)) {
				result = true;
			} else {
				result = false;
			}
		} else if (lv == "mid") {
			if (/^.*?[0-9]+.*$/.test(value) && /^.*?[A-Za-z]/.test(value)
					&& /^.{8,24}$/.test(value)) {
				result = true;
			} else {
				result = false;
			}
		} else if (lv == "high") {
			var containSpecial = RegExp(/[(\ )(\`)(\~)(\!)(\@)(\#)(\$)(\%)(\^)(\&)(\*)(\()(\))(\-)(\_)(\+)(\=)(\[)(\])(\{)(\})(\|)(\\)(\;)(\:)(\')(\")(\,)(\.)(\/)(\<)(\>)(\?)(\)]+/);
			if (/^.*?[0-9]+.*$/.test(value) && /^.*?[A-Za-z]/.test(value)
					&& containSpecial.test(value) && /^.{9,24}$/.test(value)) {
				result = true;
			} else {
				result = false;
			}
		}
	}
	return result;
}

function onPwdValidation(e, name, lv) {
	if (e.isValid) {
		if (isPassword(e.value, lv) == false) {
			e.errorText = "[" + name + "] 密码强度太低";
			e.isValid = false;
		}
	}
}

/* 根据sql连接数据库判断结果是否唯一，sql的返回值必须是数值 */
function onUniqueForDBValidation(e, sql, path, name) {
	if (e.isValid) {
		if (e.value.length == 0) {
			return true;
		}
		$.ajax({
			url : path + "/util/util_queryCount.ac",
			type : "post",
			async : false,
			data : {
				sql : encodeURIComponent(sql)
			},
			success : function(result) {
				if (result > 0) {
					e.errorText = "[" + name
							+ "] \u8be5\u503c\u5df2\u5b58\u5728";
					e.isValid = false;
				}
			}
		});
	}
}
// 查询数据库，返回数量值，大于0则返回true，反之则返回false
function getQrueryCount(tablename, sqlwhere, path) {
	var i = false;// 默认false
	var sql = "select count(1) from " + tablename + " where " + sqlwhere;
	$.ajax({
		url : path + "/util/util_queryCount.ac",
		type : "post",
		async : false,
		data : {
			sql : encodeURIComponent(sql)
		},
		success : function(result2) {
			result2 = eval(result2);
			if (result2 > 0) {
				i = true;// 找到匹配，返回true
			} else {
				i = false;// 无匹配，返回false
			}
		}
	});
	return i;
}

// 返回系统访问地址，需要传递id系统标识符
function util_querySystemAddr(id, path) {
	var result;
	$.ajax({
		url : path + "/util/util_querySystemAddr.ac",
		type : "post",
		async : false,
		data : {
			id : encodeURIComponent(id)
		},
		success : function(result2) {
			result = result2;
		}
	});
	return result;
}

// 返回系统名称，需要传递id系统标识符
function util_querySystemName(id, path) {
	var result;
	$.ajax({
		url : path + "/util/util_querySystemName.ac",
		type : "post",
		async : false,
		data : {
			id : encodeURIComponent(id)
		},
		success : function(result2) {
			result = result2;
		}
	});
	return result;
}
// 判断用户是否有操作权限的专用函数
function isOper(tablename, sqlwhere, path) {
	var i = false;// 默认false
	var sql = "select count(1) from " + tablename + " where " + sqlwhere;
	$.ajax({
		url : path + "/util/util_isSuperRole.ac",
		type : "post",
		async : false,
		success : function(result1) {
			if (result1 == "true") {
				i = true;// 超级管理员和系统管理员通过权限认证，返回true。
			} else {
				// 其他角色进行权限认证（检测该数据的负责人）
				$.ajax({
					url : path + "/util/util_queryCount.ac",
					type : "post",
					async : false,
					data : {
						sql : encodeURIComponent(sql)
					},
					success : function(result2) {
						result2 = eval(result2);
						if (result2 > 0) {
							i = true;// 找到匹配，返回true
						} else {
							i = false;// 无匹配，返回false
						}
					}
				});
			}
		}
	});
	return i;
}
/* null值转换为空字符串 */
function nulltostr(o) {
	if (o == null) {
		return "";
	}
	return o;
}
/* 判断iframe是否加载完成 */
function iframeReadyLoad(iframeId) {
	var f = false;
	var iframe = document.iframeId;
	if (iframe.attachevent) {
		iframe.attachevent("onload", function() {
			f = true;
		});
	} else {
		iframe.onload = function() {
			f = true;
		};
	}
	return f;
}
/**
 * 获取地理位置
 * @param longitude 经度控件property值
 * @param latitude 纬度控件property值
 * @param address 地址控件property值
 * @param name 预留参数，赋值为null即可
 * @param path 项目根目录,使用EL表达式 ${ctx}
 * @param type 地图类型，通过 Util.readInitXmlByElement(session, "gisType") 获取
 * @param pageState 页面视图，通过EL表达式${pageState }获取
 */
function showXY(longitude, latitude, address, name, path, type, pageState) {
	if (pageState == "edit") {
		pageState = "add";
	}
	//打开地图页
	mini.open({
		url : path + '/gis/gis_' + type + '.jsp?pageState='
				+ pageState,
		title : "地理位置",
		width : 800,
		height : 400,
		showMaxButton : true,
		onload : function() { // 弹出页面加载完成
			var iframe = this.getIFrameEl();
			// 这个name不一定是要传的,因为有的时候要这个name没有意义（name有可能是标题神马的）
			var dataName = (name == '' || name == null) ? ''
					: mini.get(name).getValue();
			//传递参数给地图页面
			var data = {
				name : dataName,
				address : mini.get(address).getValue(),
				longitude : mini.get(longitude).getValue(),
				latitude : mini.get(latitude).getValue()
			};
			// 调用弹出页面方法进行初始化
			iframe.contentWindow.SetData(data);
		},
		ondestroy : function(action) {
			var iframe = this.getIFrameEl();
			var data = iframe.contentWindow.GetData();
			data = mini.clone(data); // 必须。克隆数据。
			
			//将选取的数据赋值给相应的地址、经度、纬度控件。
			if (action == "success") {
				if (data.longitude != ''
						&& data.longitude != null)
					mini.get(longitude)
							.setValue(data.longitude);
				if (data.latitude != ''
						&& data.latitude != null)
					mini.get(latitude).setValue(data.latitude);
				if (data.address != '' && data.address != null
						&& $.trim(mini.get(address).getValue()) == ""){
					mini.get(address).setValue(data.address);
				}
				if (name != '' && name != null
						&& $.trim(mini.get(name).getValue()) == "") {
					if (data.name != '' && data.name != null)
						mini.get(name).setValue(data.name);
				}
			}

		}
	}).max();//最大化
}

function getColumns(columns) {
	columns = columns.clone();
	for (var i = columns.length - 1; i >= 0; i--) {
		var column = columns[i];
		if (!column.field) {
			columns.removeAt(i);
		} else {
			var c = {
				header : column.header,
				field : column.field
			};
			columns[i] = c;
		}
	}
	return columns;
}

var export_excel_loading_id;

function exportError1() {
	if (export_excel_loading_id)
		mini.hideMessageBox(export_excel_loading_id);
	mini.alert("导出数据过多（超过65535行），请减少查询范围后导出。");

}
function exportSuccess(p) {
	if (export_excel_loading_id)
		mini.hideMessageBox(export_excel_loading_id);
	location.href = p + "/exportExcel.jsp";
}
// 导出excel
function exportExcel(grid) {

	/*
	 * var c = grid.getBottomColumns(); var columns = getColumns(c); var json =
	 * mini.encode(columns);
	 */

	// var json =mini.encode(grid.el.innerHTML);
	// document.getElementById("excelData").value = json;
	/*
	 * document.getElementById("headData").value =
	 * grid._headerInnerEl.innerHTML; document.getElementById("bodyData").value =
	 * grid._bodyInnerEl.innerHTML;
	 */

	if (grid.data.length > 0) {
		if (grid.totalCount > 65535) {
			mini.alert('导出数据过多（超过65535行），请减少查询范围后导出。');
			return;
		} else {
			var _columns = grid.columns.clone();

			for (var i = 0; i < _columns.length; i++) {
				if (_columns[i]["__editor"] != undefined) {
					_columns[i]["__editor"] = null;
				}
				if (_columns[i]["_valueMaps"] != undefined) {
					_columns[i]["_valueMaps"] = null;
				}
				if (_columns[i]["editor"] != undefined) {
					_columns[i]["editor"] = null;
				}
			}

			var url = (grid.url);
			if (url.indexOf('?') > -1)
				url = url + "&";
			else
				url = url + "?";
			url += "export=true";

			document.getElementById("headData").value = mini.encode(_columns);
			var excelForm = document.getElementById("excelForm");
			excelForm.action = url;
			excelForm.target = "_blank";
			excelForm.submit();
			// export_excel_loading();

			// if (grid.totalPage == 1) { // 只有1页的情况下，直接导出
			//
			// document.getElementById("headData").value = mini
			// .encode(_columns);
			// document.getElementById("bodyData").value = mini
			// .encode(grid.data);
			// var excelForm = document.getElementById("excelForm");
			// excelForm.submit();
			// } else {// 多页，进行提示
			// mini
			// .showMessageBox({
			// title : "导出当前页？",
			// iconCls : "mini-messagebox-question",
			// buttons : [ "ok", "no", "cancel" ],
			// message : "点击[确定]导出当前页，点击[否]导出所有页，点击[取消]撤销当前操作。",
			// callback : function(action) {
			// if (action == 'ok') {
			// export_excel_loading();
			// document.getElementById("headData").value = mini
			// .encode(_columns);
			// document.getElementById("bodyData").value = mini
			// .encode(grid.data);
			// var excelForm = document
			// .getElementById("excelForm");
			// excelForm.submit();
			// } else if (action == 'no') {
			// if (grid.totalCount > 65535) {
			// mini.alert('行数过多，最多允许导出65535行。');
			// } else {
			// grid.setPageSize(grid.totalCount);
			// grid
			// .load(
			// null,
			// function() {
			// export_excel_loading();
			// document
			// .getElementById("headData").value = mini
			// .encode(_columns);
			// document
			// .getElementById("bodyData").value = mini
			// .encode(grid.data);
			// var excelForm = document
			// .getElementById("excelForm");
			// excelForm.submit();
			// });
			// }
			//
			// } else {
			// return;
			// }
			// }
			// });
			// }
		}
	} else {
		mini.alert("没有需要导出的数据");
	}
}

function export_excel_loading() {
	export_excel_loading_id = mini.loading(
			"正在执行导出Excel操作……<br />若长时间没反应，请右键刷新页面。", "导出Excel");
	setTimeout(function() {
		mini.hideMessageBox(export_excel_loading_id);
	}, 3000);
}
function util_treeSelect_onCloseClick(e) {
	var obj = e.sender;

	obj.setText("");
	obj.setValue("");
}
function util_treeSelect_onCloseClick_search(e) {
	var obj = e.sender;

	obj.setText("");
	obj.setValue("");

	search();
}

/**
 * 时间比较
 * 
 * @param e
 *            开始时间控件
 * @param sname
 *            开始时间的名称
 * @param ename
 *            结束时间的名称
 * @param stime
 *            开始时间属性字段property
 * @param etime
 *            结束时间属性字段property
 */
function util_onTimeValidation(e, sname, ename, stime, etime) {
	if (e.isValid) {
		if (checkTime(stime, etime) == false) {
			e.errorText = "[" + sname + "] 不能大于 " + "[" + ename + "]";
			e.isValid = false;
			var flag = stime;
			if (e.sender.id == stime) {
				flag = etime;
			}
			mini.get(flag).setErrorText(
					"[" + sname + "] 不能大于 " + "[" + ename + "]");
			mini.get(flag).setIsValid(false);

		} else {
			mini.get(stime).setIsValid(true);
			mini.get(etime).setIsValid(true);

		}
	}
}
// 比较时间大小
function checkTime(stime, etime) {
	try {
		var startTime = mini.get(stime).getValue();
		var endTime = mini.get(etime).getValue();

		if (endTime == "" || startTime == "") {
			return true;
		}
		if (mini.parseDate(endTime).getTime() < mini.parseDate(startTime)
				.getTime()) {
			return false;
		}
		return true;
	} catch (e) {
		return false;
	}
}

/**
 * 两个时间之间比较大小
 * 
 * @param begin
 *            起始时间控件mini.get("begin")
 * @param end
 *            终止时间控件mini.get("end")
 * @param beginName
 *            起始名称
 * @param endName
 *            终止名称
 */
function util_comparDate(begin, end, beginName, endName) {
	if (begin.isValid && end.isValid) {
		if (begin.getValue() != "" && end.getValue() != "") {
			if (mini.parseDate(begin.getValue()).getTime() > mini.parseDate(
					end.getValue()).getTime()) {
				begin
						.setErrorText("[" + beginName + "] 不能大于 [" + endName
								+ "]");
				begin.setIsValid(false);
			}
		}
	}
}
/**
 * 两个数值之间比较大小
 * 
 * @param begin
 *            起始值控件mini.get("begin")
 * @param end
 *            终止值控件mini.get("end")
 * @param beginName
 *            起始名称
 * @param endName
 *            终止名称
 */
function util_comparValue(begin, end, beginName, endName) {
	if (begin.isValid && end.isValid) {

		if (parseInt(begin.getValue()) > parseInt(end.getValue())) {
			begin.setErrorText("[" + beginName + "] 不能大于 [" + endName + "]");
			begin.setIsValid(false);
		}
	}
}

String.prototype.endWith = function(s) {
	if (s == null || s == "" || this.length == 0 || s.length > this.length)
		return false;
	if (this.substring(this.length - s.length) == s)
		return true;
	else
		return false;
	return true;
}

String.prototype.startWith = function(s) {
	if (s == null || s == "" || this.length == 0 || s.length > this.length)
		return false;
	if (this.substr(0, s.length) == s)
		return true;
	else
		return false;
	return true;
}

// 判断地区级别
function zoneLevel(zonecode) {
	if (zonecode.endWith("00000000"))
		return 0;
	else if (zonecode.endWith("000000"))
		return 1;
	else if (zonecode.endWith("0000"))
		return 2;
	else if (zonecode.endWith("00"))
		return 3;
	else
		return 4;
}

// 获取地区控件中直属机构的机构级别
function zonecodeSplit(zonecode) {
	var arr = zonecode.split('_');
	if (arr.length == 2)
		return arr[1];
	return '';
}

function camelToUnderline(param) {
	var len = param.length;
	var sb = "";
	var re = new RegExp("^[A-Z]+$");
	for (var i = 0; i < len; i++) {
		var c = param.charAt(i);
		if (re.test(c)) {
			sb += "_";
			sb += c.toLowerCase();
		} else {
			sb += c;
		}
	}
	return sb;
}
//不允许使用oracle关键字
function oracleKeyWordValidation(e, path, name) {

	if (e.isValid) {
		var v = false;
		var arr = [ "id", "name", "type", "content" ];// 忽略的字符串
		for (var i = 0; i < arr.length; i++) {
			if (arr[i] == $.trim(e.value).toLowerCase()) {
				v = true;
				break;
			}
		}
		//不可用的自定义关键字
		var arr2 = ["adduser","adddep","addorg","addzone","addtime","modyuser","modydep","modyorg","modyzone","modytime","state"];
		for (var i = 0; i < arr2.length; i++) {
			if (arr2[i] == $.trim(e.value).toLowerCase()) {
				e.errorText = "[" + name + "] 不可使用系统关键字。";
				e.isValid = false;
				return;
			}
		}
		//从数据库匹配关键字
		if (!v) {
			if (true == getQrueryCount("v$reserved_words", "lower(keyword)='"
					+ camelToUnderline($.trim(e.value)).toLowerCase() + "'",
					path)) {
				e.errorText = "[" + name + "] 不可使用oracle关键字。";
				e.isValid = false;
			}
		}
	}
}
/**
 * 展示miniUI form表单提交错误信息提示
 * @param errors 错误信息，通过 mini.Form("form1").getErrorTexts() 获取
 * @param width 提示框宽度
 * @param height 提示宽高度
 */
function showFormErrorTexts(errors, width, height) {
	var s = errors.join("</li><li>");
	s = "<li>" + s + "</li>";
	s = "<ul>" + s + "</ul>";
	mini.open({
		// &imgstr= 不能省，而且必须写在最后
		url : bootPATH.substring(0, bootPATH.length - 8)
				+ "common/errorInfo.jsp",
		title : "验证信息",
		width : width==undefined?400:width,
		height : height==undefined?300:height,
		showMaxButton : false,
		showCloseButton : true,
		onload: function () {//弹出页面加载完成
           var iframe = this.getIFrameEl(); 
           iframe.contentWindow.setData(s); 
        }
	});
	//mini.alert(s);
}

// 获取项目地址
function getRootPath() {
	// 获取当前网址，如： http://localhost:8083/proj/meun.jsp
	var curWwwPath = window.document.location.href;
	// 获取主机地址之后的目录，如： proj/meun.jsp
	var pathName = window.document.location.pathname;
	var pos = curWwwPath.indexOf(pathName);
	// 获取主机地址，如： http://localhost:8083
	var localhostPath = curWwwPath.substring(0, pos);
	// 获取带"/"的项目名，如：/proj
	var projectName = pathName
			.substring(0, pathName.substr(1).indexOf('/') + 1);
	return (localhostPath + projectName);
}
// 获取地址栏参数
function getQueryString(name) {
	var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
	var r = window.location.search.substr(1).match(reg);
	if (r != null) {
		return unescape(r[2]);
	}
	return null;
}

/**
 * 图片裁剪
 * 
 * @param img
 *            img标签id，用于查看图片
 * @param imgValue
 *            保存图片名称的隐藏标签id
 * @param width
 *            [int] 裁剪的宽度
 * @param height
 *            [int] 裁剪的高度
 * @param maxSize
 *            [int] 上传文件的大小限制（MB）
 */
function util_changePhoto(img, imgValue, width, height, maxSize) {
	if ($("#" + imgValue).val() != "") {
		// 删除未保存的图片
		$.ajax({
			url : bootPATH.substring(0, bootPATH.length - 8)
					+ 'delPhotoServlet?imgstr=' + $("#" + imgValue).val()
		});
	}
	mini.open({
		// &imgstr= 不能省，而且必须写在最后
		url : bootPATH.substring(0, bootPATH.length - 8)
				+ "common/changePhoto.jsp?w=" + width + "&h=" + height
				+ "&maxSize=" + maxSize + "&imgstr=",
		title : "图片上传",
		width : 800,
		height : 450,
		showMaxButton : false,
		ondestroy : function(action) {
			var iframe = this.getIFrameEl();
			var data = iframe.contentWindow.getImage();// 获取裁剪后的图片名
			data = mini.clone(data); // 必须。克隆数据。
			if (data) {
				if (action == "ok") {
					var src = bootPATH
							.substring(0, bootPATH.length - 8)
							+ "photoTemp/" + data;
					$("#" + img).attr("src", src);
					$("#" + imgValue).val(data);
				} else {
					// 删除未保存的图片
					$.ajax({
						url : bootPATH
								.substring(0, bootPATH.length - 8)
								+ 'delPhotoServlet?imgstr=' + data
					});
				}
			}
		}
	}).max();
}

function util_getFromToDate(yearpar, weekpar) {
	// 计算周
	var dates = util_getPeriodOfWeek(yearpar, weekpar);
	var fromDate = dates[0];
	var toDate = dates[1];
	var obj = new Object();
	obj.fromDate = fromDate.getFullYear() + "/" + (fromDate.getMonth() + 1) + "/"
			+ fromDate.getDate();
	obj.toDate = toDate.getFullYear() + "/" + (toDate.getMonth() + 1) + "/"
			+ toDate.getDate();
	return obj;
}
/*
 * 取得指定年的指定周的开始和结束日期 以数组的形式返回（字符串）
 */
function util_getPeriodOfWeek(year, week) {
	var dateString = '1/1/' + year;
	var firstDay = new Date(dateString);
	// 只有在是在星期一才算是第一年的开始
	var curWeek = firstDay.getDay();
	var addDays = 0;
	if (curWeek == 0) {
		curWeek = 7;
	}
	if (curWeek > 1) { // 如果不是周一，则找到下一个周一作为第一周的第一天
		addDays = 8 - curWeek;
	}
	// 再跳转到指定的周的周一
	addDays = addDays + 1 + 7 * (week - 1);
	firstDay.setDate(addDays);
	var endDate = new Date(dateString);
	endDate.setDate(addDays + 6);
	var returnObj = new Array(2);
	returnObj[0] = firstDay;
	returnObj[1] = endDate;
	//返回结果
	return returnObj;
}


/**
 * 周控件数据加载，日期显示
 * 
 * @param property
 *            周控件名
 * @param year
 *            年控件名
 */
function util_tag_onWeekChanged(week, year,showDate) {
	var w = mini.get(week);
	var sd = showDate == 'true' ? true :false;
	if (year == 'null' || year == '') {
		mini.alert("周控件["+week+"]未指定年控件！","开发者警告");
		return;
	} else {
		
		var y = mini.get(year).getValue();
		
		if(y==""){
			return;
		}
		
		var weekNum = util_getNumOfWeeks(y);//获取周数
		var obj = new Array();
		if(weekNum == 52){
			w.setData([{'name':'第1周','id':1},{'name':'第2周','id':2},{'name':'第3周','id':3},{'name':'第4周','id':4},{'name':'第5周','id':5},{'name':'第6周','id':6},{'name':'第7周','id':7},{'name':'第8周','id':8},{'name':'第9周','id':9},{'name':'第10周','id':10},{'name':'第11周','id':11},{'name':'第12周','id':12},{'name':'第13周','id':13},{'name':'第14周','id':14},{'name':'第15周','id':15},{'name':'第16周','id':16},{'name':'第17周','id':17},{'name':'第18周','id':18},{'name':'第19周','id':19},{'name':'第20周','id':20},{'name':'第21周','id':21},{'name':'第22周','id':22},{'name':'第23周','id':23},{'name':'第24周','id':24},{'name':'第25周','id':25},{'name':'第26周','id':26},{'name':'第27周','id':27},{'name':'第28周','id':28},{'name':'第29周','id':29},{'name':'第30周','id':30},{'name':'第31周','id':31},{'name':'第32周','id':32},{'name':'第33周','id':33},{'name':'第34周','id':34},{'name':'第35周','id':35},{'name':'第36周','id':36},{'name':'第37周','id':37},{'name':'第38周','id':38},{'name':'第39周','id':39},{'name':'第40周','id':40},{'name':'第41周','id':41},{'name':'第42周','id':42},{'name':'第43周','id':43},{'name':'第44周','id':44},{'name':'第45周','id':45},{'name':'第46周','id':46},{'name':'第47周','id':47},{'name':'第48周','id':48},{'name':'第49周','id':49},{'name':'第50周','id':50},{'name':'第51周','id':51},{'name':'第52周','id':52}]);
		}else{
			w.setData([{'name':'第1周','id':1},{'name':'第2周','id':2},{'name':'第3周','id':3},{'name':'第4周','id':4},{'name':'第5周','id':5},{'name':'第6周','id':6},{'name':'第7周','id':7},{'name':'第8周','id':8},{'name':'第9周','id':9},{'name':'第10周','id':10},{'name':'第11周','id':11},{'name':'第12周','id':12},{'name':'第13周','id':13},{'name':'第14周','id':14},{'name':'第15周','id':15},{'name':'第16周','id':16},{'name':'第17周','id':17},{'name':'第18周','id':18},{'name':'第19周','id':19},{'name':'第20周','id':20},{'name':'第21周','id':21},{'name':'第22周','id':22},{'name':'第23周','id':23},{'name':'第24周','id':24},{'name':'第25周','id':25},{'name':'第26周','id':26},{'name':'第27周','id':27},{'name':'第28周','id':28},{'name':'第29周','id':29},{'name':'第30周','id':30},{'name':'第31周','id':31},{'name':'第32周','id':32},{'name':'第33周','id':33},{'name':'第34周','id':34},{'name':'第35周','id':35},{'name':'第36周','id':36},{'name':'第37周','id':37},{'name':'第38周','id':38},{'name':'第39周','id':39},{'name':'第40周','id':40},{'name':'第41周','id':41},{'name':'第42周','id':42},{'name':'第43周','id':43},{'name':'第44周','id':44},{'name':'第45周','id':45},{'name':'第46周','id':46},{'name':'第47周','id':47},{'name':'第48周','id':48},{'name':'第49周','id':49},{'name':'第50周','id':50},{'name':'第51周','id':51},{'name':'第52周','id':52},{'name':'第53周','id':53}]);
		}
		
		
		if(y=="" || w.getValue() == ""){
			$("#" + week + "_fromDate").empty();
			$("#" + week + "_toDate").empty();
			$("#" + week + "_fromDate").empty();
			$("#" + week + "_separator").hide();
			return;
		}
		//日期的值
		var retFromTo = util_getFromToDate(y,w.getValue());
		var d = document.getElementById(week + "_fromDate");
		if (d) {
			d.innerHTML = retFromTo.fromDate;
			$("#" + week + "_toDate").html(retFromTo.toDate);
			$("#" + week + "_separator").show();
		} else {
			$("#" + week).after(
					"&nbsp;<span id=\"" + week + "_fromDate\">"
							+ retFromTo.fromDate + "</span> <span id=\""+week+"_separator\">-</span> <span id=\""
							+ week + "_toDate\">" + retFromTo.toDate
							+ "</span>");
			if(!sd){
				$("#" + week + "_fromDate").hide();
				$("#" + week + "_toDate").hide();
				$("#" + week + "_separator").hide();
			}
		}
	}
}

/**
 * 获取周控件的日期值对象（周起始、终止日期）
 * @param week 周控件的属性值
 * @returns obj
 */
function util_week_getWeekDate(week){
	var obj = {};
	obj.fromDate = $("#"+week+"_fromDate").html();
	obj.toDate = $("#"+week+"_toDate").html();
	return obj;
}

/**
 * 上传文件到服务器.
 * 
 * @param fileTypeExts
 *            字符串；设置可以选择的文件的类型，格式如：’*.doc;*.pdf;*.rar’
 * @param size
 *            整数；上传文件的大小限制 ，如果为整数型则表示以KB为单位的大小，如果是字符串，则可以使用(B, KB, MB, or
 *            GB)为单位，比如’2MB’；如果设置为0则表示无限制
 * @param len
 *            整数；最大上传文件数量
 * @param multi
 *            布尔值；设置为true时可以上传多个文件
 * @param sysId
 *            系统标识，必填
 * @param funccode
 *            某个模块的附件，必填，一般使用该模块功能编号
 * @param idConnect
 *            模块表中的主键，即这个记录的主键，如何使用了联合主键，则将主键拼接起来以逗号分隔，必填
 */
function util_uploadify(fileTypeExts, size, len, multi, sysId, funccode,
		idConnect) {
	mini.open({
		// &imgstr= 不能省，而且必须写在最后
		url : bootPATH.substring(0, bootPATH.length - 8)
				+ "common/uploadify.jsp?fileTypeExts=" + fileTypeExts
				+ "&size=" + size + "&len=" + len + "&multi=" + multi
				+ "&sysId=" + sysId + "&funccode=" + funccode + "&idConnect="
				+ idConnect,
		title : "文件上传",
		width : 500,
		height : 450,
		showMaxButton : false,
		showCloseButton : false
	});
}

/**
 * 计算一年有多少周
 * @param year int 年份
 * @returns int 周数
 */
function util_getNumOfWeeks(year){
	 var d=new Date(year,0,1);
	 var yt=( ( year%4==0 && year%100!=0) || year%400==0)? 366:365; 
	 return Math.ceil((yt-d.getDay())/7.0);
	
//	var periodOfTime = util_getPeriodOfWeek(year,53);
//	if(periodOfTime==null || periodOfTime.length!=2){
//		return 52;
//	}
//	if(parseInt(mini.formatDate(periodOfTime[0],'yyyyMMdd').substring(0,4)) > year){
//		return 52;
//	}else{
//		return 53;
//	}	
}

/**
 * 判断有无隐私权限
 * @param t 隐私项
 * @param v 用户拥有的隐私项
 * @returns {Number}
 */
function util_hasPricacy(t,v){
	if(t=="" || v=="" || t==null || v==null){
		return 0;
	}
	var s = v.split(",");
	for(var i=0;i<s.length;i++){
		if(s[i]==t){
			return 1;
		}
	}
	return 0;
}


function util_isIE(){
	return navigator.appName.indexOf("Microsoft Internet Explorer")!=-1 && document.all;
}

function util_isIE6() {
	return navigator.userAgent.split(";")[1].toLowerCase().indexOf("msie 6.0")=="-1"?false:true;
}

function util_isIE7(){
	return navigator.userAgent.split(";")[1].toLowerCase().indexOf("msie 7.0")=="-1"?false:true;
}

function util_isIE8(){
	return navigator.userAgent.split(";")[1].toLowerCase().indexOf("msie 8.0")=="-1"?false:true;
}

//字节转换
function util_byte2Size(s){
	var size = parseInt(s);
	if(size <1024){
		return size+" 字节";
	}else if(size>=1024 && size <1024*1024){
		return Math.round(size/1024*100)/100+" KB";
	}else if(size >=1024*1024 && size <1024*1024*1024){
		return Math.round(size/(1024*1024)*100)/100+" MB";	
	}else{
		return Math.round(size/(1024*1024*1024)*100)/100+" GB";	
	}
}