<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="newsManage" extends="bsp-default" namespace="/cn/com/sinosoft/os/newsmanage">
		<!-- 查询 -->
		<action name="qryNewsManage" class="newsManageAction" method="qryParentInput">
			<param name="sessionGroup">newsManage</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/newsmanage/qryNewsManage.jsp
			</result>
		</action>
		<action name="qryNewsManageList" class="commonQueryAction">
			<param name="sessionGroup">newsManage</param>
			<param name="queryCode">QRY_OS_NEWS_MANAGE</param>
			<param name="resultName">qryList</param>
		</action>
		<!-- 查询 新闻管理-作者排名子表 -->
		<action name="qryNewsAuthorSubList" class="commonQueryAction">
			<param name="sessionGroup">newsManage</param>
			<param name="queryCode">QRY_OS_NEWS_MANAGE_NEWSAUTHORSUB</param>
			<param name="resultName">qryList</param>
		</action>
		<!-- 查看 -->
		<action name="newsManage_viewParent" class="newsManageAction" method="viewParent">
			<param name="sessionGroup">newsManage</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/newsmanage/edtNewsManage.jsp
			</result>
		</action>
		<!-- 添加 -->
		<action name="newsManage_addParentInput" class="newsManageAction" method="addParentInput">
			<param name="sessionGroup">newsManage</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/newsmanage/edtNewsManage.jsp
			</result>
		</action>
		<action name="newsManage_addParentSubmit" class="newsManageAction" method="addParentSubmit">
			<param name="sessionGroup">newsManage</param>
		</action>
		<!-- 修改 -->
		<action name="newsManage_edtParentInput" class="newsManageAction" method="edtParentInput">
			<param name="sessionGroup">newsManage</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/newsmanage/edtNewsManage.jsp
			</result>
		</action>
		<action name="newsManage_edtParentSubmit" class="newsManageAction" method="edtParentSubmit">
			<param name="sessionGroup">newsManage</param>
		</action>
		<!-- 删除 -->
		<action name="newsManage_delParentSubmit" class="newsManageAction" method="delParentSubmit">
			<param name="sessionGroup">newsManage</param>
		</action>
		
		<!-- 审批 applySubmit-->
		<action name="auditnewsManageSubmit" class="newsManageAction" method="applySubmit">
			<param name="sessionGroup">newsManage</param>
		</action>
		
		<!-- 打开审批页面 -->
		<action name="newsManage_auditParentInput" class="newsManageAction" method="auditParentInput">
			<param name="sessionGroup">newsManage</param>
			<result name="success">
			     /WEB-INF/pages/os/cn/com/sinosoft/os/newsmanage/auditNewsManage.jsp
			</result>
		</action>
		
		<!-- 打开订正页面 -->
		<action name="newsManage_auditeditParentInput" class="newsManageAction" method="auditParentInput">
			<param name="sessionGroup">newsManage</param>
			<result name="success">
			    /WEB-INF/pages/os/cn/com/sinosoft/os/newsmanage/edtNewsManage.jsp
			</result>
		</action>
		
		<!-- 新闻归档，进行推送到档案系统 -->
		<action name="pushFile" class="newsManageAction" method="pushFile">
			<param name="sessionGroup">newsManage</param>
		</action>
	</package>
</struts>