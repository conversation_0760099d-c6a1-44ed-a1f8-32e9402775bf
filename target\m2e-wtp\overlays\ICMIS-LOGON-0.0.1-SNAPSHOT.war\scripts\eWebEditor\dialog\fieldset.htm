<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj="INSERT";var ak=lang["DlgComInsert"];var D;var mk;var sT;var tx;var lG="";var lH="";var bx="";var aS="";if(I.ay()=="Control"){D=I.aX();if(D.tagName=="FIELDSET"){aj="MODI";ak=lang["DlgComModify"];lG=D.align;mk=st(D,"LEGEND");if(mk){lH=mk.align;}bx=D.style.borderColor;aS=D.style.backgroundColor;}}var Q=lang["DlgFs"]+"("+ak+")";document.write("<title>"+Q+"</title>");function ah(){lang.TranslatePage(document);bb($("d_alignfieldset"),lG.toLowerCase());bb($("d_alignlegend"),lH.toLowerCase());$("d_bordercolor").value=bx;$("s_bordercolor").style.backgroundColor=bx;$("d_bgcolor").value=aS;$("s_bgcolor").style.backgroundColor=aS;parent.bp(Q);};function st(H,sg){var t;for(var i=0;i<H.children.length;i++){if(H.children[i].tagName==sg){return H.children[i];}else{t=st(H.children[i],sg);if(t){return t;}}}return null;};function ok(){bx=$("d_bordercolor").value;if(!lU(bx)){cv($("d_bordercolor"),lang["ErrColorBorder"]);return;}aS=$("d_bgcolor").value;if(!lU(aS)){cv($("d_bgcolor"),lang["ErrColorBg"]);return;}lG=$("d_alignfieldset").options[$("d_alignfieldset").selectedIndex].value;lH=$("d_alignlegend").options[$("d_alignlegend").selectedIndex].value;if(aj=="MODI"){D.align=lG;if(mk){mk.align=lH;}D.style.borderColor=bx;D.style.backgroundColor=aS;}else{EWIN.insertHTML("<fieldset align='"+lG+"' style='border-color:"+bx+";background-color:"+aS+"'><legend align="+lH+">"+lang["DlgComTitle"]+"</legend>"+lang["DlgComContent"]+"</fieldset>");}parent.aT();}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgAlign></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgFsFieldset></span>:</td> <td noWrap width="29%"><select id=d_alignfieldset size=1 style="width:80px"><option value='' lang=DlgComDefault></option><option value='left' lang=DlgAlignLeft></option><option value='center' lang=DlgAlignCenter></option><option value='right' lang=DlgAlignRight></option></select></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgFsLegend></span>:</td> <td noWrap width="29%"><select id=d_alignlegend size=1 style="width:80px"><option value='' lang=DlgComDefault></option><option value='left' lang=DlgAlignLeft></option><option value='center' lang=DlgAlignCenter></option><option value='right' lang=DlgAlignRight></option></select></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgColor></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgColorBorder></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bordercolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bordercolor onclick="fX('bordercolor')" align=absmiddle></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgColorBg></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bgcolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bgcolor onclick="fX('bgcolor')" align=absmiddle></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id=d_ok lang=DlgBtnOK onclick="ok()">&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html>