﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>WEB打印控件LODOP的样例三十九:演示打印田字格、上下划线等文本</title>

<script language="javascript" src="LodopFuncs.js"></script>
</head>
<body>

<h2><b><font color="#009999">演示打印田字格、上下划线等文本:</font></b></h2>
<p>用函数<font color="#0000FF">SET_PRINT_STYLEA</font>的<font color="#0000FF">TextFrame</font>属性可以设置文本的各种边框类型，<p>属性值的具体含义可以参考：<font color="#0000FF">打印设计</font>-&gt;<font color="#0000FF">双击文本对象</font>-&gt;<font color="#0000FF">文本属性</font>-&gt;<font color="#0000FF">文本框类型</font>
<p>演示如下：<input type="button" value="打印预览" name="B3"  onclick="Preview()"> 
<input type="button" value="打印设计" name="B3" onclick="Design()">                                                            
<p><textarea rows="15" id="doc1" cols="102">田字格行尾用空格补齐：
最后一页不满时用空格行补齐：
　　人生不能无友。孔子说“无友不如己者”所以“有朋自远方来，不亦乐乎。”在生活旅程中，朋友就像生活的阳光照耀着我们，温暖着我们。当我们满怀疲惫时，朋友的关爱似柔情的月光，给了我们甜蜜的慰籍和生存的坚强；当我们面对失败时，朋友的鼓励，给了我们拼搏的信心和向上的力量；当我们欢呼成功时，朋友的祝福，给了我们真诚的喜悦和前进的动力。从古到今，传诵着多少朋友情谊的佳话：俞伯牙和钟子期，嵇康和阮籍，李白和杜甫，鲁迅和瞿秋白等等。尤为令人称道的是管鲍之交，几千年来，论知心之交，必曰：管、鲍。
　　春秋时，齐国有两个人，一个叫管仲，字夷吾；一个叫鲍叔，字宣子。两人自幼时以贫贱结交。后来鲍叔先在齐桓公门下信用显达，就举荐管仲为相，位在己上。两人同心辅政，始终如一。管仲曾有几句言语道：“吾尝三战三北，鲍叔不以我为怯，知我有老母也；吾尝三仕三见逐，鲍叔不以我为不肖；知我不遇时也；吾尝与鲍叔为贾，分利多，鲍叔不以我为贪，知我贫也。生我者父母，知我者鲍叔也！”我每背诵此段话，都不由心声感慨，既感慨于他们那份难得的相知相与的友谊，也感慨于管仲对这份情谊的珍惜，更感慨于当今交友之道的世风日下。
　　古人结交惟结心，今人结交惟结面。明人陈继儒在《小窗幽记》里谈到交友之道时说“先淡后浓，先疏后亲，先达后近，交友道也。”而在当今，有的人在社会上交朋友比打个出租车还随便。或素未平生，交谈不过顷刻，完全不知底里，便视为“知心朋友”；或歌肆酒廊生意场相识，点点头递支烟酒杯一碰，醉意朦胧之中，便为“莫逆之交”；或旅途聚首，乍感气味相投，凭一时高兴，便当作“割头不换的生死朋友。这样的朋友正如古人所言“世人片言合，杯酒结新欢，生死轻相许，酒寒盟亦寒”用我们现在的话说是酒肉朋友是经不起时间的蒸发更经不起时间的蒸馏，是绝难长久的。
　　有一种朋友，是以互为利用作前提的，欧阳修在《朋党论》里说“小人所好者禄利也，所贪者财货也。当其同利之时，暂相党引以为朋者，伪也。”在现实中这样利用过了便散伙，榨不出油就撒手，刚才还“朋友”成一坨稀泥分不出彼此，转背就成了乌鸦麻雀不通语言的“伪朋友”者多矣。
　　或许你们经常在一起吃喝玩乐，看似十分投缘，有时他为你办件事，你也帮他办件事，其实各人心里都打着“小九九”，有时算计人情帐的声音都能听得见，这样的朋友是朋友吗？
或许你是个官，他以你的取舍为取舍，以你的好恶为好恶。看你的脸色说话。为了讨得你的欢心，整天低眉折腰，揣摸你的心思，瞪大眼睛，竖起耳朵，设法对你的爱好、嗜好、脾气、口味等进行摸底，然后有的放“矢”，投其所好：你爱腾云驾雾，便送上大“中华”；你的孩子要上学，送上红包表祝贺；你的寿诞到来即，跑前忙后孝过子；你好挥毫书字画，求得“宝墨”堂中挂。你觉得他好够朋友，可是那满脸的笑容后面却堆满了假意的呆板，这样的朋友是朋友吗？如果你觉得是，那他也是和你的官位是朋友。
</textarea></p>



<p><a href="PrintSampIndex.html">&lt;&lt;回样例目录</a></p>
<script language="javascript" type="text/javascript">  
        var LODOP; //声明为全局变量 
	function Preview() {		
		CreatePrintPage();
		LODOP.SET_SHOW_MODE("NP_NO_RESULT",true);//设置NP插件无返回，这可避免chrome对弹窗超时误报崩溃
	  	LODOP.PREVIEW();		
	};
	function Design() {		
		CreatePrintPage();
		LODOP.PRINT_DESIGN();		
	};
	function CreatePrintPage() {
		LODOP=getLodop();  
		LODOP.PRINT_INIT("打印控件功能演示_Lodop功能_文本边框线");
		LODOP.SET_PRINT_STYLE("FontSize",14);
		LODOP.ADD_PRINT_TEXT(60,17,125,25,"字符带上划线");
		LODOP.SET_PRINT_STYLEA(0,"TextFrame",1);
		LODOP.SET_PRINT_STYLEA(0,"LetterSpacing",5);
		LODOP.ADD_PRINT_TEXT(100,17,125,25,"整行带上划线");
		LODOP.SET_PRINT_STYLEA(0,"TextFrame",7);
		LODOP.ADD_PRINT_TEXT(60,164,125,25,"字符带下划线");
		LODOP.SET_PRINT_STYLEA(0,"TextFrame",2);
		LODOP.SET_PRINT_STYLEA(0,"LetterSpacing",5);
		LODOP.ADD_PRINT_TEXT(100,164,125,25,"整行带下划线");
		LODOP.SET_PRINT_STYLEA(0,"TextFrame",8);
		LODOP.ADD_PRINT_TEXT(60,315,125,25,"字符带边框线");
		LODOP.SET_PRINT_STYLEA(0,"TextFrame",5);
		LODOP.SET_PRINT_STYLEA(0,"LetterSpacing",5);
		LODOP.ADD_PRINT_TEXT(100,315,125,25,"整行带边框线");
		LODOP.SET_PRINT_STYLEA(0,"TextFrame",11);
		LODOP.ADD_PRINT_TEXT(60,464,125,25,"字符带圆圈");
		LODOP.SET_PRINT_STYLEA(0,"TextFrame",6);
		LODOP.SET_PRINT_STYLEA(0,"LetterSpacing",10);
		LODOP.ADD_PRINT_TEXT(100,464,125,25,"整行被圆圈");
		LODOP.SET_PRINT_STYLEA(0,"TextFrame",12);
		LODOP.ADD_PRINT_TEXT(100,613,105,25,"\"田字格\"");
		LODOP.SET_PRINT_STYLEA(0,"TextFrame",13);
		LODOP.SET_PRINT_STYLEA(0,"LetterSpacing",10);
		LODOP.ADD_PRINT_TEXT(60,613,105,25,"分割线");
		LODOP.SET_PRINT_STYLEA(0,"TextFrame",14);
		LODOP.SET_PRINT_STYLEA(0,"LetterSpacing",20);
		LODOP.ADD_PRINT_TEXT(166,35,691,401,document.getElementById("doc1").innerHTML);
		LODOP.SET_PRINT_STYLEA(0,"Offset2Top",-140);
		LODOP.SET_PRINT_STYLEA(0,"TextFrame",13);
		LODOP.SET_PRINT_STYLEA(0,"SpacePatch",1);
		LODOP.SET_PRINT_STYLEA(0,"ItemType",4);
		LODOP.SET_PRINT_STYLEA(0,"Horient",3);
		LODOP.SET_PRINT_STYLEA(0,"Vorient",3);
	};	
</script> 
</body>
</html>
