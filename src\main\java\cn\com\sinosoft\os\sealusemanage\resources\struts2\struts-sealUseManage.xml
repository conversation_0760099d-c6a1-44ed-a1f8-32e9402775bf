<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="sealUseManage" extends="bsp-default" namespace="/cn/com/sinosoft/os/sealusemanage">
		<!-- 查询 -->
		<action name="qrySealUseManage" class="sealUseManageAction" method="qryParentInput">
			<param name="sessionGroup">sealUseManage</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealusemanage/qrySealUseManage.jsp
			</result>
		</action>
		<action name="qrySealUseManageList" class="commonQueryAction">
			<param name="sessionGroup">sealUseManage</param>
			<param name="queryCode">QRY_OS_SEAL_USE_MANAGE</param>
			<param name="resultName">qryList</param>
		</action>
		<!-- 查看 -->
		<action name="sealUseManage_viewParent" class="sealUseManageAction" method="viewParent">
			<param name="sessionGroup">sealUseManage</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealusemanage/edtSealUseManage.jsp
			</result>
		</action>
		<!-- 添加 -->
		<action name="sealUseManage_addParentInput" class="sealUseManageAction" method="addParentInput">
			<param name="sessionGroup">sealUseManage</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealusemanage/edtSealUseManage.jsp
			</result>
		</action>
		<action name="sealUseManage_addParentSubmit" class="sealUseManageAction" method="addParentSubmit">
			<param name="sessionGroup">sealUseManage</param>
		</action>
		<!-- 修改 -->
		<action name="sealUseManage_edtParentInput" class="sealUseManageAction" method="edtParentInput">
			<param name="sessionGroup">sealUseManage</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealusemanage/edtSealUseManage.jsp
			</result>
		</action>
		<action name="sealUseManage_edtParentSubmit" class="sealUseManageAction" method="edtParentSubmit">
			<param name="sessionGroup">sealUseManage</param>
		</action>
		<!-- 删除 -->
		<action name="sealUseManage_delParentSubmit" class="sealUseManageAction" method="delParentSubmit">
			<param name="sessionGroup">sealUseManage</param>
		</action>
	</package>
</struts>