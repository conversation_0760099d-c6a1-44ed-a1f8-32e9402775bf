﻿.mini-button
{
    border-radius: 2px; 
}
.mini-tab
{
    border-radius:3px;   
}
.mini-tabs-header-right .mini-tab
{
    border-top-left-radius:0px;
    border-bottom-left-radius:0px;     
}

.mini-tabs-header-left .mini-tab
{
    border-top-right-radius:0px;
    border-bottom-right-radius:0px;     
}
.mini-tabs-header-bottom .mini-tab
{
    border-top-right-radius:0px;
    border-top-left-radius:0px;     
}
.mini-tabs-header-top .mini-tab
{
    border-bottom-right-radius:0px;
    border-bottom-left-radius:0px; 
}


/*----------------------------------- bgcss ----------------------------------*/
.bg-toolbar
{
    background:#f0f0f0 url(images/toolbar/toolbar.gif) repeat-x 0 0;
}


/*----------------------------------- tools ----------------------------------*/

.mini-tools .mini-tools-collapse
{
    background:url(images/tools/collapse.gif) no-repeat 50% 50%;
    width:15px;	
}
.mini-tools .mini-tools-expand
{
    background:url(images/tools/expand.gif) no-repeat 50% 50%;
    width:15px;	
}
.mini-tools-close
{
    background:url(images/tools/close.gif) no-repeat 50% 0px;
    width:15px;	
}


/*----------------------------------- toolbar ----------------------------------*/

.mini-toolbar
{
    border:solid 1px #99bce8;
    background:#f0f0f0 url(images/toolbar/toolbar.gif) repeat-x 0 0;
}
.separator
{
    border-left:solid 1px #A8A8A8;    
}

/*----------------------------------- button ----------------------------------*/

.mini-button
{
	background:url(images/button/button.gif) repeat-x 0px 50%;
	border:solid 1px #b5afaf;	
    color:#222;
}
body a:hover.mini-button
{
	background:#e2f2fe url(images/button/hover.gif) repeat-x 0px 50%;
	border:solid 1px #80a4d0;	         
}
body .mini-button-pressed, body a:hover.mini-button-pressed,
body .mini-button-checked, body a:hover.mini-button-checked,
body a.mini-button-popup, body a:hover.mini-button-popup
{
	background:#bacee4 url(images/button/pressed.gif) repeat-x 0px 50%;	
	border-color:#7a9ac4;
}
body .mini-button-disabled, body a:hover.mini-button-disabled
{
    border-color:#E1E2E5;
    color:#BABAC1;
    background:#F8F9FA url(images/button/disabled.png) repeat-x 0 0px;       
}


/*----------------------------------- textbox ----------------------------------*/
.mini-textbox-border
{
    background:white url(images/buttonedit/text-bg.gif) repeat-x 0px top;
	border:solid 1px #b5b8c8;        
}
body .mini-textbox-focus .mini-textbox-border
{
    border-color: #7eadd9;
}


/*----------------------------------- buttonedit ----------------------------------*/

.mini-buttonedit-border
{
	background:white url(images/buttonedit/text-bg.gif) repeat-x 0px top;
	border-color:#b5b8c8;    
}
body .mini-buttonedit-focus .mini-buttonedit-border
{
    border-color: #7eadd9;
}
.mini-buttonedit-button
{
	background:url(images/buttonedit/btn.gif) repeat-x 0px 50%;		
	border:#b5b8c8 1px solid;   
	padding:0;
}
.mini-buttonedit-button-hover,
.mini-buttonedit-hover .mini-buttonedit-button
{
    background:url(images/buttonedit/btn-hover.gif) repeat-x 0 50%;	
	border-color:#80a4d0;
}
.mini-buttonedit-button-pressed,
.mini-buttonedit-popup .mini-buttonedit-button
{
    background:url(images/buttonedit/btn-pressed.gif) repeat-x 0 50%;	
	border-color:#80a4d0;
}
.mini-popupedit .mini-buttonedit-icon
{
    background:url(images/buttonedit/arrow.gif) no-repeat  1px 2px;
}
.mini-datepicker .mini-buttonedit-icon
{
    background:url(images/buttonedit/date.gif) no-repeat  1px 2px;
}


/*------------------------- panel -----------------------*/

.mini-panel-border
{    
    border:1px solid #99bce8;     
}
.mini-panel-header
{
    height:25px;
    background:#E3E6E8 url(images/panel/header.gif) repeat-x 0 0px;
    color:#04408c;
    font-weight:bold;
    border-bottom:solid 1px #99bce8;
}
.mini-panel-header-inner
{
   padding-top:4px;
}
.mini-panel-toolbar
{
    border-bottom:solid 1px #99bce8;
    background:#D3E1F1;
}

.mini-panel-footer
{
    border-top:solid 1px #99bce8;
    background:#D3E1F1;
}

.mini-panel-collapse .mini-tools-collapse
{
    background:url(images/tools/expand.gif) no-repeat 50% 50%;
}


/*----------------------------- window -------------------------*/
.mini-window .mini-panel-header
{
    background:#E3E6E8 url(images/window/header.gif) repeat-x 0 0px;
}
.mini-window .mini-panel-footer
{
    background:#D3E1F1;
}
.mini-tools-max
{
	background:url(images/tools/max.gif) no-repeat;
}
.mini-tools-restore
{
	background:url(images/tools/restore.gif) no-repeat;
}


/*------------------- navbar ------------------*/
.mini-outlookbar-border
{
    border:1px solid #99bce8;         
}
.mini-outlookbar .mini-outlookbar-groupHeader
{
    background:#d9e8fb url(images/navbar/header.gif) repeat-x 0 0;    
    border-color:#99bce8;
}
.mini-outlookbar .mini-outlookbar-groupTitle
{
    font-weight:normal;
}
.mini-outlookbar .mini-outlookbar-group 
{
    border-color:#99bce8;
}
.mini-outlookbar .mini-outlookbar-groupBody
{    
    border-color:#99bce8;
}
/* view2 */
.mini-outlookbar-view2 .mini-outlookbar-groupHeader
{
    border:solid 1px #99bce8; 
}
.mini-outlookbar-view2 .mini-outlookbar-groupBody
{    
    background:#fff;
}
/* view3 */
.mini-outlookbar-view3 .mini-outlookbar-group
{
    border:solid 1px #99bce8; 
}

.mini-outlookbar .mini-tools-collapse
{
    width:15px;	
}
.mini-outlookbar .mini-outlookbar-expand .mini-tools-collapse
{
    background:url(images/navbar/expand.gif) no-repeat 50% 50%;   
}
.mini-outlookbar .mini-outlookbar-collapse .mini-tools-collapse
{
    background:url(images/navbar/collapse.gif) no-repeat 50% 50%;   
}


/*----------------------- splitter -----------------------*/
.mini-splitter-border
{
    border:solid 1px #99bce8;     
}
.mini-splitter .mini-splitter-pane1{
    border-color:#99bce8;
}
.mini-splitter .mini-splitter-pane2{
    border-color:#99bce8;
}

/*----------------------- layout -----------------------*/
.mini-layout-border
{
    
}
.mini-layout-region
{
    border-color:#90b5e3;    
}
.mini-layout-region-header
{
    background:url(images/layout/header.gif) repeat-x 0 0;
    border-bottom:solid 1px #99bce8;
}
.mini-layout-proxy
{
    border-color:#90b5e3;
    background:#D2E0F2;
}
.mini-layout-proxy-hover
{
    background:#e1f0f2;    
}

.mini-layout-region-west .mini-tools-collapse
{
    background:url(images/layout/west.gif) no-repeat 50% 50%;
}
.mini-layout-region-east .mini-tools-collapse
{
    background:url(images/layout/east.gif) no-repeat 50% 50%;
}
.mini-layout-region-north .mini-tools-collapse
{
    background:url(images/layout/north.gif) no-repeat 50% 50%;
}
.mini-layout-region-south .mini-tools-collapse
{
    background:url(images/layout/south.gif) no-repeat 50% 50%;
}

.mini-layout-proxy-west .mini-tools-collapse
{
    background:url(images/layout/east.gif) no-repeat 50% 50%;
}
.mini-layout-proxy-east .mini-tools-collapse
{
    background:url(images/layout/west.gif) no-repeat 50% 50%;
}
.mini-layout-proxy-north .mini-tools-collapse
{
    background:url(images/layout/south.gif) no-repeat 50% 50%;
}
.mini-layout-proxy-south .mini-tools-collapse
{
    background:url(images/layout/north.gif) no-repeat 50% 50%;
}

/*------------------------- menu --------------------------------*/

.mini-menu
{
	background:#f0f0f0 url(images/menu/menu.gif) repeat-y -5px 0;		
	border-color:#89b1e4;
    color:black;
}
.mini-menuitem
{
    line-height:20px;    
}
.mini-menuitem-hover
{
    border-color:#80a4d0;
	background:#d6e6fa url(images/menu/hover.gif) repeat-x;	
}
.mini-menu-popup
{
    border-color:#80a4d0;
	background:#d6e6fa url(images/menu/hover.gif) repeat-x;	
}
.mini-menuitem-selected
{
    border-color:#80a4d0;
    background:#DBDDE2 url(images/menu/pressed.png) repeat-x 0 0;
}
.mini-menuitem-text, .mini-menuitem-text a
{
    color:black;
}
.mini-separator
{
    border-top:solid 1px #8EAADE;
}

/* menu horizontal */
.mini-menu-horizontal .mini-menu-inner
{
    background:#d3e1f1 url(images/menu/hmenu.gif) repeat-x 0 0;
}
.mini-menu-horizontal .mini-menuitem-hover
{
    border:solid 1px #80a4d0;
    background:#d6e6fa url(images/menu/hover.gif) repeat-x;	
}
.mini-menu-horizontal  .mini-menu-popup
{
    border:solid 1px #80a4d0;
    background:#d6e6fa url(images/menu/hover.gif) repeat-x;	
    border-bottom:0px;
}

/*---------------------- listbox -----------------------------*/
.mini-listbox-border
{    
    border:#98c0f4 1px solid;
}
.mini-listbox-border td
{
    line-height:20px;       
}
.mini-listbox-item td{
	border-top:white 1px dotted;
	border-bottom:white 1px dotted;	
}
.mini-listbox-item-hover td{
    background:#dfe8f6;
	border-top:#8eabe4 1px dotted;
	border-bottom:#8eabe4 1px dotted;
}
.mini-listbox-item-selected td{
	background:#cbdaf0;
	border-top:#8eabe4 1px dotted;
	border-bottom:#8eabe4 1px dotted;
}
.mini-listbox-header
{
    background:#E7EBEF url(images/listbox/header.png) repeat-x 0 0;    
    border-bottom:solid 1px #a5acb5;
}

/*------------------- treegrid --------------------*/
.mini-treegrid-border
{
    border-color:#98c0f4;
}

.mini-treegrid-header
{
    border-bottom:none;
}
.mini-treegrid-headerInner
{
    background:#E7EBEF url(images/treegrid/header.png) repeat-x 0 0;
}
.mini-treegrid td
{
    border-color:#d0d0d0;    
}
.mini-treegrid-header td
{
    border-color:#d0d0d0;
}

.mini-treegrid-selectedNode
{
	background:#cbdaf0;
}
.mini-treegrid-hoverNode
{
    background:#dfe8f6;
}
/*
.mini-treegrid-expand .mini-treegrid-ec-icon
{
	background-image:url(images/treegrid/expand.gif);
	background-position:50% 50%;
}
.mini-treegrid-collapse .mini-treegrid-ec-icon
{
	background-image:url(images/treegrid/collapse.gif);
	background-position:50% 50%;
}*/
.mini-treegrid-leaf
{
    background-image:url(images/treegrid/file.png);
}
.mini-treegrid-folder
{
    background-image:url(images/treegrid/folder.gif);
}

/*---------------------- calendar -----------------------------*/
.mini-calendar
{    
    border:1px solid #98c0f4;       
}
.mini-calendar-header
{   
    background:#cad8ec url(images/calendar/footer.gif) repeat-x 0 0;    
    border-bottom:solid 1px #9bb5d3;    
}
.mini-calendar-footer
{
    border-top:solid 1px #b2d1f5;
    background:#cad8ec url(images/calendar/footer.gif) repeat-x 0 0;    
}
.mini-calendar-tadayButton, .mini-calendar-clearButton,
.mini-calendar-okButton, .mini-calendar-cancelButton
{
	background:url(images/button/button.gif) repeat-x 0px 50%;
	border:solid 1px #b5afaf;	
    color:#222;
}
.mini-calendar-menu-selected, a:hover.mini-calendar-menu-selected
{
    color:#333;
    background:#dae5f3;
    border:solid 1px #8db2e3;
}

.mini-calendar .mini-calendar-selected
{
    color:#333;
    background:#dae5f3;
    border:solid 1px #8db2e3;
}
.mini-calendar .mini-calendar-today
{
    border:1px solid #C00000;
}
.mini-calendar-daysheader td
{border-bottom:solid 1px #b2d1f5;    
}
    
/*---------------------- tabs -----------------------------*/

.mini-tabs-scrollCt
{
    border-color:#8CB2E2;
    background:#f0f0f0 url(images/toolbar/toolbar.gif) repeat-x 0 0;
}

.mini-tabs-leftButton, .mini-tabs-rightButton
{
    border:solid 1px #b5afaf;
    background-color:#EBEBEE;
}
a:hover.mini-tabs-leftButton,a:hover.mini-tabs-rightButton
{
    border:solid 1px #80a4d0;
    background-color:#E1E8FD;
}
/* top */
.mini-tabs-bodys
{
    border-color:#8CB2E2;
}
.mini-tabs-space
{
    border-color:#8CB2E2;
}
.mini-tabs-space2
{
    border-color:#8CB2E2;
}

.mini-tab
{
    background: #EBEBEE url(images/tabs/tab.gif) repeat-x 0 0;  
    border: 1px solid #8CB2E2;
    color: #333;    
    
}
.mini-tab-hover
{    
    background:#E1E8FD url(images/tabs/hover.gif) repeat-x 0 0; 
}
.mini-tab-active
{
    border-bottom:solid 1px #deecfd;
    background:#E1E8FD url(images/tabs/pressed.gif) repeat-x 0 0;  
}

/* bottom */
.mini-tabs-header-bottom .mini-tabs-space,
.mini-tabs-header-bottom .mini-tabs-space2
{
    border:0;
    border-top: 1px solid #8CB2E2;
}
.mini-tabs-body-bottom
{    
    border:solid 1px #99bce8;
    border-bottom:0;
}
.mini-tabs-header-bottom .mini-tab-active
{
    border-top:solid 1px white;
    border-bottom:solid 1px #99bce8;
}

/* left */
.mini-tabs-header-left .mini-tabs-space,
.mini-tabs-header-left .mini-tabs-space2
{
    border:0;
    border-right: 1px solid #99bce8;
}
.mini-tabs-header-left .mini-tabs-bodys
{
    border:solid 1px #99bce8;
    border-left:0;
}
.mini-tabs-header-left .mini-tab-active
{    
    border:solid 1px #99bce8;
    border-right:solid 1px white;
}

/* right */
.mini-tabs-header-right .mini-tabs-space,
.mini-tabs-header-right .mini-tabs-space2
{
    border:0;
    border-left: 1px solid #99bce8;
}
.mini-tabs-header-right .mini-tabs-bodys
{    
    border:solid 1px #99bce8;
    border-right:0;
}
.mini-tabs-header-right .mini-tab-active
{    
    border:solid 1px #99bce8;
    border-left:solid 1px white;
}

/*---------------------- tree -----------------------------*/


.mini-tree-node-hover .mini-tree-nodeshow
{
    background:url(images/tree/button.gif) repeat-x 0px 50%;
	border:solid 1px #b5afaf;		  
}
.mini-tree-selectedNode .mini-tree-nodeshow
{
    background:#e2f2fe url(images/tree/hover.gif) repeat-x 0px 50%;
	border:solid 1px #80a4d0;	
}

.mini-tree-leaf
{
    background-image:url(images/tree/leaf.gif);
}
.mini-tree-folder
{
    background-image:url(images/tree/folder.gif);
}
.mini-tree-expand .mini-tree-folder
{
    background-image:url(images/tree/folder-open.gif);
}

/*------------------- grid --------------------*/
.mini-grid-border
{
    border-color:#99bce8;
}
.mini-grid-header
{
    background:url(images/grid/header.gif) repeat-x scroll left top #e2e4e6;
}
.mini-grid-headerCell, .mini-grid-topRightCell
{
    background:url(images/grid/header.gif) repeat-x scroll left top #e2e4e6;
    border-right:#c5c5c5 1px solid;
    border-bottom:#c5c5c5 1px solid;
}
.mini-grid-cell
{
    border-color:#d0d0d0;
    border-bottom-color:#d0d0d0;
}
.mini-grid-footer, .mini-grid-pager
{
    border-top:solid 1px #99bce8;    
    background:url(images/grid/footer.gif) repeat-x scroll left top #d4e2f1;
}
.mini-grid-columnproxy
{
    background:url(images/grid/header.gif) repeat-x scroll left top #e2e4e6;
    border:#c5c5c5 1px solid;    
}


body .mini-grid-row-hover, body .mini-grid-row-hover .mini-grid-frozenCell
{
    background:#ecedef;    
}
body .mini-grid-row-selected, body .mini-grid-row-selected .mini-grid-frozenCell
{
    background:#dfe8f6;		
}

/*------------------- pager --------------------*/
.mini-pager-first
{    
    background:url(images/pager/first.gif) no-repeat;
}
.mini-pager-prev
{
    background-image:url(images/pager/prev.gif);
}
.mini-pager-next
{
    background-image:url(images/pager/next.gif);
}
.mini-pager-last
{
    background-image:url(images/pager/last.gif);
}

/*---------------------- progressbar -----------------------------*/

.mini-progressbar-border
{
    border:1px solid #98c0f4;
}
.mini-progressbar-bar
{
     background:#D3E1F1;
}
.mini-progressbar-text
{ 
    color:#396295; 
}