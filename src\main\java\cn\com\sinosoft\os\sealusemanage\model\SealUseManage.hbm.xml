<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="cn.com.sinosoft.os.sealusemanage.model.SealUseManage" table="OS_SEAL_USE_MANAGE">
		<id name="id" column="ID" type="java.lang.String">
		</id>
		<property name="applyUser" column="APPLY_USER" type="java.lang.String"/>
		<property name="applyContent" column="APPLY_CONTENT" type="java.lang.String"/>
		<property name="urgencyDegree" column="URGENCY_DEGREE" type="java.lang.String"/>
		<property name="sealType" column="SEAL_TYPE" type="java.lang.String"/>
		<property name="finalApproval" column="FINAL_APPROVAL" type="java.lang.String"/>
		<property name="monitorPerson" column="MONITOR_PERSON" type="java.lang.String"/>
		<property name="sealTime" column="SEAL_TIME" type="java.util.Date"/>
		<property name="addZone" column="ADD_ZONE" type="java.lang.String"/>
		<property name="addOrg" column="ADD_ORG" type="java.lang.String"/>
		<property name="addDep" column="ADD_DEP" type="java.lang.String"/>
		<property name="addUser" column="ADD_USER" type="java.lang.String"/>
		<property name="addTime" column="ADD_TIME" type="java.util.Date"/>
		<property name="modyZone" column="MODY_ZONE" type="java.lang.String"/>
		<property name="modyOrg" column="MODY_ORG" type="java.lang.String"/>
		<property name="modyDep" column="MODY_DEP" type="java.lang.String"/>
		<property name="modyUser" column="MODY_USER" type="java.lang.String"/>
		<property name="modyTime" column="MODY_TIME" type="java.util.Date"/>
		<property name="state" column="STATE" type="java.lang.String"/>
		<property name="dataSource" column="DATA_SOURCE" type="java.lang.String"/>
		<property name="dataModyTime" column="DATA_MODY_TIME" type="java.util.Date"/>
		<property name="applyId" column="APPLY_ID" type="java.lang.String"/>
		<property name="sealPerson" column="SEAL_PERSON" type="java.lang.String"/>
	</class>
</hibernate-mapping>