<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj="INSERT";var ak=lang["DlgComInsert"];var D;var sT;var aI="http://";var jS="";var ez="0";var bx="";var ji="";var bt="";var au="";var at="";var cJ="";var cL="";var cj="";var cX="file";var bz=((parseFloat(config.AllowImageSize)>0)?true:false);var ug=(((config.SYWZFlag=="2")||(config.SYTPFlag=="2"))?true:false);if(I.ay()=="Control"){D=I.aX();if(D.tagName=="IMG"&&bU(D,"_ewebeditor_fake_tag")==""){aj="MODI";ak=lang["DlgComModify"];cX="url";aI=sp(D,"src");jS=bU(D,"alt");ez=bU(D,"border");bx=D.style.borderColor;ji=D.style.filter;bt=bU(D,"align");au=jV(dE(D,"width"));at=jV(dE(D,"height"));cJ=bU(D,"vspace");cL=bU(D,"hspace");}}var Q=lang["DlgImg"]+"("+ak+")";document.write("<title>"+Q+"</title>");function ah(){lang.TranslatePage(document);bb($("d_filter"),ji);bb($("d_align"),bt.toLowerCase());if(!bz){cX="url";}xM(cX);$("d_fromurl").value=aI;$("d_alt").value=jS;$("d_border").value=ez;$("d_bordercolor").value=bx;$("s_bordercolor").style.backgroundColor=bx;$("d_width").value=au;$("d_height").value=at;$("d_vspace").value=cJ;$("d_hspace").value=cL;var kP=$("TD_Right");var eU=$("Fieldset_Right");var h1=kP.offsetHeight;var h2=eU.offsetHeight;if(h1>h2){if(O.ae){eU.style.height=h1+"px";}else{eU.style.height=(h1-2)+"px";}}xN();if($("d_fromurl")){if(O.ae){$("d_fromurl").onpropertychange=lg;}else{C.ao($("d_fromurl"),"input",lg);}}parent.bp(Q);};function lg(){if(O.ae){if(event.propertyName!='value'){return;}}var R=$("d_fromurl").value;var n=R.lastIndexOf(".");if(n>=1){if(R.length>n+1){var cy="|"+R.substr(n+1)+"|";if("|gif|jpg|jpeg|png|bmp|".indexOf(cy.toLowerCase())>=0){xN();return;}}}pG();};function xM(dc){if(dc=="url"){$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_checkfromurl").checked=true;if(bz){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}}else{$("d_checkfromurl").checked=false;$("uploadfile").disabled=false;$("d_checkfromfile").checked=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}}};function UploadError(an){eW();xM('file');$("divProcessing").style.display="none";try{cv($("uploadfile"),jF(an,config.AllowImageExt,config.AllowImageSize));}catch(e){}};function UploadSaved(mO,vG){$("d_fromurl").value=mO;cj=vG;if(config.SLTMode=="0"&&config.SLTFlag=="2"){cj=mO;}cH();};function cH(){aI=$("d_fromurl").value;jS=$("d_alt").value;ez=$("d_border").value;bx=$("d_bordercolor").value;ji=$("d_filter").options[$("d_filter").selectedIndex].value;bt=$("d_align").value;au=$("d_width").value;at=$("d_height").value;cJ=$("d_vspace").value;cL=$("d_hspace").value;if(aj=="MODI"){if(cj){bm(D,"src",cj);jZ(D,"src",cj);}else{bm(D,"src",aI);jZ(D,"src",aI);}bm(D,"alt",jS);bm(D,"border",ez);D.style.borderColor=bx;D.style.filter=ji;bm(D,"align",bt);D.style.width=au;D.style.height=at;bm(D,"width","");bm(D,"height","");bm(D,"vspace",cJ);bm(D,"hspace",cL);if(cj){A.F.execCommand("CreateLink",false,aI);var cK=D.parentNode;if(cK.tagName=="A"){bm(cK,"target","_blank");bm(cK,"href",aI);jZ(cK,"href",aI)}}}else{var f='';if(ji!=''){f=f+'filter:'+ji+';';}if(bx!=''){f=f+'border-color:'+bx+';';}if(au!=""){f=f+'width:'+au+';';}if(at!=""){f=f+'height:'+at+';';}if(f!=''){f=' style="'+f+'"';}if(cj){f='<img src="'+cj+'"'+f;}else{f='<img src="'+aI+'"'+f;}if(ez!=''){f=f+' border="'+ez+'"';}if(jS!=''){f=f+' alt="'+jS+'"';}if(bt!=''){f=f+' align="'+bt+'"';}if(cJ!=''){f=f+' vspace="'+cJ+'"';}if(cL!=''){f=f+' hspace="'+cL+'"';}f=f+'>';if(cj){f='<a href="'+aI+'" target="_blank">'+f+'</a>';}EWIN.insertHTML(f);}parent.qB();};var nm="";function ok(){$("d_border").value=du($("d_border").value);$("d_width").value=du($("d_width").value);$("d_height").value=du($("d_height").value);$("d_vspace").value=du($("d_vspace").value);$("d_hspace").value=du($("d_hspace").value);if(!lU($("d_bordercolor").value)){cv($("d_bordercolor"),lang["DlgImgErrBorColor"]);return false;}if($("d_checkfromurl").checked){cH();}else{if(!hw($("uploadfile").value,config.AllowImageExt)){UploadError("ext");return false;}fD();$("divProcessing").style.display="";if(ug){if(nm==""){nm=document.myuploadform.action;}if($("d_syflag").checked){document.myuploadform.action=nm+"&syflag=1";}else{document.myuploadform.action=nm;}}document.myuploadform.submit();}};function fD(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_alt").disabled=true;$("d_border").disabled=true;$("d_bordercolor").disabled=true;$("d_filter").disabled=true;$("d_align").disabled=true;$("d_width").disabled=true;$("d_height").disabled=true;$("d_vspace").disabled=true;$("d_hspace").disabled=true;$("d_ok").disabled=true;};function eW(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_alt").disabled=false;$("d_border").disabled=false;$("d_bordercolor").disabled=false;$("d_filter").disabled=false;$("d_align").disabled=false;$("d_width").disabled=false;$("d_height").disabled=false;$("d_vspace").disabled=false;$("d_hspace").disabled=false;$("d_ok").disabled=false;};function xN(){var R="";if($("d_checkfromurl").checked){R=$("d_fromurl").value;if(config.BaseHref!=""){R=nk(R);}}else{R=$("uploadfile").value;}if(R.lastIndexOf(".")<=0){pG();return;}if(!O.ae){if($("uploadfile").files.length>0){R=$("uploadfile").files[0].getAsDataURL();}}var ui=false;if((bz)&&(O.gM)){if($("d_checkfromfile").checked){ui=true;}}if(R){if(ui){wo(R);}else{var img=new Image();img.onload=function(){wk(img.width,img.height,img.src);};img.onerror=pG;img.src=R;}}else{$("tdPreview").innerHTML="";}};function wk(w,h,R){var ft=$("tdPreview").offsetWidth;var fu=$("tdPreview").offsetHeight;var sw,dS;if((w>ft)||(h>fu)){var nw=ft/w;var gZ=fu/h;if(nw>gZ){dS=fu;sw=w*gZ;}else{sw=ft;dS=h*nw;}}else{sw=w;dS=h;}$("tdPreview").innerHTML="<img border=0 src='"+R+"' width='"+sw+"' height='"+dS+"' style=\"filter:"+$("d_filter").value+"\">";$("tdPreviewSize").innerHTML=w+" * "+h;sA(w,h);};function pG(){$("tdPreview").innerHTML="";$("tdPreviewSize").innerHTML="";};function wo(url){var ft=$("tdPreview").offsetWidth;var fu=$("tdPreview").offsetHeight;$("tdPreview").innerHTML="<div id=imgPreviewDiv style=\"filter : progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=image) "+$("d_filter").value+";WIDTH:10px; HEIGHT:10px;\"></div>";$("imgPreviewDiv").filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src=url;var w=$("imgPreviewDiv").offsetWidth;var h=$("imgPreviewDiv").offsetHeight;var sw,dS;if((w>ft)||(h>fu)){var nw=ft/w;var gZ=fu/h;if(nw>gZ){dS=fu;sw=w*gZ;}else{sw=ft;dS=h*nw;}}else{sw=w;dS=h;}$("imgPreviewDiv").style.width=sw;$("imgPreviewDiv").style.height=dS;$("imgPreviewDiv").filters.item("DXImageTransform.Microsoft.AlphaImageLoader").sizingMethod='scale';$("tdPreviewSize").innerHTML=w+" * "+h;sA(w,h);};function sA(w,h){if(config.SLTFlag!="2"){return;}var nb=parseInt(config.SLTMinSize);var hl=parseInt(config.SLTOkSize);var tw,th;switch(config.SLTCheckFlag){case "0":if(w<=nb){return;}tw=hl;th=parseInt(h*(hl/w));break;case "1":if(h<=nb){return;}tw=parseInt(w*(hl/h));th=hl;break;case "2":if(w<=nb&&h<=nb){return;}if(w>h){tw=hl;th=parseInt(h*(hl/w));}else{tw=parseInt(w*(hl/h));th=hl;}break;}$("d_width").value=tw+"";$("d_height").value=th+"";};function js(bG,hp,co){if(co=="tab_mfu"){DLGMFU.lY("image",$(co),DLGTab.gR[1].Width+"px",DLGTab.gR[1].Height+"px");}}</script> <script event="OnCancel(an)" for="eWebEditorMFU">if(an==""){parent.aP();}</script> <script event="OnUpload(an, aa)" for="eWebEditorMFU">if(an=="endall"||an=="endapart"){var f="";var dY=aa.split("|");for(var i=0;i<dY.length;i++){var a=dY[i].split("::");if(a.length==3&&a[1]!=""){var bO=a[0].substr(a[0].lastIndexOf("\\")+1);if(a[2]==""){f+="<img border=0 src='"+a[1]+"'><br>";EWIN.addUploadFile(bO,a[1]);}else{f+="<a href='"+a[1]+"' target='_blank'><img border=0 src='"+a[2]+"'></a><br>";EWIN.addUploadFile(bO,a[1]);EWIN.addUploadFile(bO,a[2]);}}}EWIN.insertHTML(f);parent.aT();}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <script type="text/javascript">if(config.MFUEnable=="1"){DLGTab.jB([[lang["DlgComTabNormal"],"tab_normal"],[lang["DlgComTabMFU"],"tab_mfu"]]);}</script> <table id="tab_normal" border=0 cellpadding=0 cellspacing=5 align=center> <tr valign=top><td> <table border=0 cellpadding=0 cellspacing=0 align=center width="100%"> <tr> <td> <fieldset> <legend><span lang=DlgImgSource></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript">if(bz){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"xM('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\">");document.write(jL("image"));document.write("</td>");document.write("</tr>");}</script> <tr> <td noWrap width="20%"><input type=radio id="d_checkfromurl" value="1" onclick="xM('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td noWrap width="80%"> <script type="text/javascript">if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:100%' size=20 value=''></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"xQ('image','fromurl')\" value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:100%' size=30 value=''>");}</script> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgImgEffect></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgImgAlt></span>:</td> <td noWrap width="80%" colspan=4><input type=text id=d_alt size=10 value="" style="width:100%"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgImgBorder></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_border size=10 value="" onkeydown="et(event);"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgImgBorderColor></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bordercolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bordercolor onclick="fX('bordercolor')" align=absmiddle></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgImgSpecEffect></span>:</td> <td noWrap width="29%"> <select id=d_filter size=1 style="width:80px" onchange="xN()"> <option value='' selected lang=DlgComNone></option> <option value='Alpha(Opacity=50)' lang=DlgImgAlpha1></option> <option value='Alpha(Opacity=0, FinishOpacity=100, Style=1, StartX=0, StartY=0, FinishX=100, FinishY=140)' lang=DlgImgAlpha2></option> <option value='Alpha(Opacity=10, FinishOpacity=100, Style=2, StartX=30, StartY=30, FinishX=200, FinishY=200)' lang=DlgImgAlpha3></option> <option value='blur(add=1,direction=14,strength=15)' lang=DlgImgBlur1></option> <option value='blur(add=true,direction=45,strength=30)' lang=DlgImgBlur2></option> <option value='Wave(Add=0, Freq=60, LightStrength=1, Phase=0, Strength=3)' lang=DlgImgWave></option> <option value='gray' lang=DlgImgGray></option> <option value='Chroma(Color=#FFFFFF)' lang=DlgImgChroma></option> <option value='DropShadow(Color=#999999, OffX=7, OffY=4, Positive=1)' lang=DlgImgDropShadow></option> <option value='Shadow(Color=#999999, Direction=45)' lang=DlgImgShadow></option> <option value='Glow(Color=#ff9900, Strength=5)' lang=DlgImgGlow></option> <option value='flipv' lang=DlgImgFlipv></option> <option value='fliph' lang=DlgImgFliph></option> <option value='grays' lang=DlgImgGrays></option> <option value='xray' lang=DlgImgXray></option> <option value='invert' lang=DlgImgInvert></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgAlign></span>:</td> <td noWrap width="29%"> <select id=d_align size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='right' lang=DlgAlignRight></option> <option value='top' lang=DlgAlignTop></option> <option value='middle' lang=DlgAlignMiddle></option> <option value='bottom' lang=DlgAlignBottom></option> <option value='absmiddle' lang=DlgAlignAbsmiddle></option> <option value='absbottom' lang=DlgAlignAbsbottom></option> <option value='baseline' lang=DlgAlignBaseline></option> <option value='texttop' lang=DlgAlignTexttop></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgImgWidth></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_width size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgImgHeight></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_height size=10 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgImgVSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_vspace size=10 value="" onkeydown="et(event);"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgImgHSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_hspace size=10 value="" onkeydown="et(event);"></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> </table> </td><td id="TD_Right"> <fieldset id="Fieldset_Right"> <legend><span lang=DlgImgPreview></span></legend> <table border=0 cellpadding=0 cellspacing=5 width="200" height="200" valign=top> <tr><td colspan=2 bgcolor=#FFFFFF align=center valign=middle id=tdPreview height="150"> </td></tr> <tr><td id=tdPreviewSize></td><td align=right><input type=button class="dlgbtn" id=btnPreivew onclick="xN()" lang=DlgImgPreview></td></tr> </table> </fieldset> </td></tr> <tr> <td noWrap> <script type="text/javascript">if(ug){document.write("<input type=checkbox id=d_syflag value='1'><label for=d_syflag><span lang=DlgImgSYFlag></span></label>");}</script> </td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="d_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel></td> </tr> </table> <div id="tab_mfu" style="display:none"></div> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:80px;top:100px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> </body> </html>