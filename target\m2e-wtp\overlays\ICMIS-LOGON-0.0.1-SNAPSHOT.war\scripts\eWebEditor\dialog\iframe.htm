<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj="INSERT";var ak=lang["DlgComInsert"];var D;var bX="http://";var lB="";var lN="0";var lI="0";var lF="0";var au="500";var at="400";if(I.ay()=="Control"){D=I.aX();if(D.tagName=="IFRAME"){aj="MODI";ak=lang["DlgComModify"];bX=D.src;lB=D.scrolling;lN=D.frameBorder;lI=D.marginHeight;lF=D.marginWidth;au=D.width;at=D.height;}}var Q=lang["DlgIframe"]+"("+ak+")";document.write("<title>"+Q+"</title>");function ah(){lang.TranslatePage(document);bb($("d_scrolling"),lB.toLowerCase());$("d_url").value=bX;$("d_frameborder").value=lN;$("d_marginheight").value=lI;$("d_marginwidth").value=lF;$("d_width").value=au;$("d_height").value=at;parent.bp(Q);};function ok(){lB=$("d_scrolling").options[$("d_scrolling").selectedIndex].value;bX=$("d_url").value;if(!wr(bX)){cv($("d_url"),lang["DlgIframeErrUrl"]);return;}$("d_frameborder").value=du($("d_frameborder").value);$("d_marginheight").value=du($("d_marginheight").value);$("d_marginwidth").value=du($("d_marginwidth").value);lN=$("d_frameborder").value;lI=$("d_marginheight").value;lF=$("d_marginwidth").value;var au="";if(!eH($("d_width"),lang["DlgIframeErrWidth"]))return;au=$("d_width").value;var at="";if(!eH($("d_height"),lang["DlgIframeErrHeight"]))return;at=$("d_height").value;if(aj=="MODI"){D.src=bX;D.scrolling=lB;D.frameBorder=lN;D.marginHeight=lI;D.marginWidth=lF;D.width=au;D.height=at;}else{EWIN.insertHTML("<iframe src='"+bX+"' scrolling='"+lB+"' frameborder='"+lN+"' marginheight='"+lI+"' marginwidth='"+lF+"' width='"+au+"' height='"+at+"'></iframe>");}parent.aT();};function eH(H,io){var b=false;if(H.value!=""){H.value=parseFloat(H.value);if(H.value!="0"){b=true;}}if(b==false){cv(H,io);return false;}return true;}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgIframeProperty></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgIframeUrl></span>:</td> <td noWrap width="80%" colspan=4><input type=text id=d_url size=10 value="" style="width:100%"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgIframeScroll></span>:</td> <td noWrap width="29%"> <select id=d_scrolling size=1 style="width:80px"> <option value='' lang=DlgComDefault></option> <option value='yes' lang=DlgIframeYes></option> <option value='no' lang=DlgIframeNo></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgIframeBorder></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_frameborder size=10 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgIframeMarginHeight></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_marginheight size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgIframeMarginWidth></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_marginwidth size=10 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgIframeWidth></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_width size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgIframeHeight></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_height size=10 value=""></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html> 