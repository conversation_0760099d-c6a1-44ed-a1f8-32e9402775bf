/* CSS Document */
.tab-1
{
	table-layout:fixed;
	font-size: 14px;
	word-break:break-all;
	border-collapse:collapse;
	margin: 5px 0 5px 0;
	padding: 1px;
	text-align: left;
	background: #f5f5f5;
	width:95%;
	border-color: #cccccc;
}
.tab-1 tr
{
	background:white;
	word-break: break-all; 
}
.tab-1 td{
	font-size: 14px;
}
table{
	font-size: 14px;
}
pre{
	word-break: break-all; 
	font-size: 14px;
}
 
.tab-1 .bgcolor
{
	background:#f5f5f5;
}
.asLabel .mini-textbox-border
{
    border:0;background:none;cursor:default;
    
}
.asLabel .mini-textbox-input,
.asLabel .mini-buttonedit-border,
.asLabel .mini-buttonedit-input,
.asLabel .mini-textboxlist-border
{
    border:0;background:none;cursor:default;
    
}
.asLabel .mini-buttonedit-button,
.asLabel .mini-textboxlist-close
{
    display:none;
}
.asLabel .mini-textboxlist-item
{
    padding-right:8px;
} 
fieldset
{
    border:solid 1px #aaa;
    margin: 0;padding: 0;
}        
.hideFieldset
{
    border-left:0;
    border-right:0;
    border-bottom:0;
}
.hideFieldset .fieldset-body
{
    display:none;
}
.validGroup
{
    padding:5px;
    margin:0;
    color: Red;     
    font-size:14px;
    text-align: left;
    overflow: auto;
}