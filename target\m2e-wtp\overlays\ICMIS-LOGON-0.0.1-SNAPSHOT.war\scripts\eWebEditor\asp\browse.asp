<%@language="vbscript" codepage="936" %>
<HTML><HEAD><meta http-equiv='Content-Type' content='text/html; charset=gb2312'><TITLE>eWebEditor</TITLE></head><body>
<%
Const SaveUpFilesPath="uploads"
const shareimagepath="sharefile/image"
const shareflashpath="sharefile/flash"
const sharemediapath="sharefile/media"
const share0therpath="sharefile/0ther"
const strFileName="UploadFile.asp"

dim action,typea,dir,foldertype,returnflag

action=request.querystring("action")
typea=request.querystring("type")
dir=request.querystring("dir")
foldertype=request.querystring("foldertype")
returnflag=request.querystring("returnflag")

if dir <>"" then
	if not isnumeric(mid(dir,1,len(dir)-1)) then response.end
end if

dim UploadDir
if right(SaveUpFilesPath,1)<>"/" then
	UploadDir="/" & SaveUpFilesPath & "/"
else
	UploadDir="/" & SaveUpFilesPath
end if

set fso=server.CreateObject("Scripting.FileSystemObject")
if action="folder" then
	%>
	<script language=javascript>
	var arrUpload = new Array();
	var arrShareImage = new Array();
	var arrShareFlash = new Array();
	var arrShareMedia = new Array();
	var arrShareOther = new Array();<%
	dim TruePath
	TruePath=Server.MapPath(UploadDir)
	if fso.FolderExists(TruePath) then
		Set theFolder=fso.GetFolder(TruePath)
		i=0
		For Each theFile In theFolder.subfolders
			response.write vbnewline&vbtab&"arrUpload["&i&"]=new Array("""&theFile.name&""",1, 0);"
		i=1+i
		next
	end if
	%>
	parent.setFolderList(arrUpload, arrShareImage, arrShareFlash, arrShareMedia, arrShareOther);
	</script>
	<%
	set fso=nothing
end if
if action="file" then
%>
	<script language=javascript>
	var arr = new Array();
	<%
	if dir="" and foldertype="shareimage" then objfile=Server.MapPath("../"&shareimagepath):typea=""
	if dir="" and foldertype="shareflash" then objfile=Server.MapPath("../"&shareflashpath):typea=""
	if dir="" and foldertype="sharemedia" then objfile=Server.MapPath("../"&sharemediapath):typea=""
	if dir="" and foldertype="shareother" then objfile=Server.MapPath("../"&share0therpath):typea=""
	if dir<>"" and foldertype="upload" then objfile=Server.MapPath(UploadDir&dir)
	Set theFolder=fso.GetFolder(objfile)
	j=0
	For Each theFile In theFolder.Files
			select case typea
				case "file"
					arrextension=split("rar|zip|pdf|doc|docx|xls|xlsx|ppt|pptx|chm|hlp","|")
					for i=0 to ubound(arrextension)
						if lcase(fso.getextensionname(thefile.name))=arrextension(i) then
							response.write vbnewline&vbtab&"arr["&j&"]=new Array("""&theFile.name&""", """&Round((theFile.size/1024),2)&" KB"","""&theFile.datelastmodified&""");"
						end if
					next
				case "image"
					arrextension=split("gif|jpg|jpeg|bmp|png","|")
					for i=0 to ubound(arrextension)
						if lcase(fso.getextensionname(thefile.name))=arrextension(i) then
							response.write vbnewline&vbtab&"arr["&j&"]=new Array("""&theFile.name&""", """&Round((theFile.size/1024),2)&" KB"","""&theFile.datelastmodified&""");"
						end if
					next
				case "flash"
						if lcase(fso.getextensionname(thefile.name))="swf" then
							response.write vbnewline&vbtab&"arr["&j&"]=new Array("""&theFile.name&""", """&Round((theFile.size/1024),2)&" KB"","""&theFile.datelastmodified&""");"
						end if
				case "media"
					arrextension=split("rm|flv|wmv|asf|mov|mpg|mpeg|avi|mp3|wav|mid|midi|ra|wma","|")
					for i=0 to ubound(arrextension)
						if lcase(fso.getextensionname(thefile.name))=arrextension(i) then
							response.write vbnewline&vbtab&"arr["&j&"]=new Array("""&theFile.name&""", """&Round((theFile.size/1024),2)&" KB"","""&theFile.datelastmodified&""");"
						end if
					next
				case else
					response.write vbnewline&vbtab&"arr["&j&"]=new Array("""&theFile.name&""", """&Round((theFile.size/1024),2)&" KB"","""&theFile.datelastmodified&""");"
			end select
		j=1+j
	Next
	set fso=nothing
%>
	parent.setFileList('<%=returnflag%>', '<%=foldertype%>', '<%=dir%>', arr);
</script>
<%end if%>
</body></html>