#
# Pushlet configuration.
# Place this file in the CLASSPATH (e.g. WEB-INF/classes) or directly under WEB-INF.
#
# $Id: pushlet.properties,v 1.13 2007/12/07 12:57:40 justb Exp $
#

#
#
#
config.version=1.0.2

#
# CLASS FACTORY SPECIFICATION
#
# Change these if you want to override any of the core classes
# within the Pushlet framework with your own custom classes.
#
# Examples:
# - custom SessionManager for authorisation
# - maintain lists of active subjects (topics)
# - send events on subscription
# - plug in custom logging like log4j
# Note that you must maintain the semantics of each class !
# Below are the default properties for the core classes.
controller.class=nl.justobjects.pushlet.core.Controller
dispatcher.class=nl.justobjects.pushlet.core.Dispatcher
logger.class=nl.justobjects.pushlet.util.Log4jLogger
# logger.class=nl.justobjects.pushlet.util.DefaultLogger
sessionmanager.class=nl.justobjects.pushlet.core.SessionManager
session.class=nl.justobjects.pushlet.core.Session
subscriber.class=nl.justobjects.pushlet.core.Subscriber
subscription.class=nl.justobjects.pushlet.core.Subscription
 
# sessionmanager.maxsessions=200

#
# DISPATCHER
#


# TODO: allow properties to be maintained in
# a user dir
# config.redirect=/etc/pushlet.properties

#
# LOGGING
#

# log level (trace(6) debug(5) info (4), warn(3), error(2), fatal(1))
# default is info(4)
log.level=4

#
# LOCAL EVENT SOURCES
#

# should local sources be loaded ?
sources.activate=true

#
# SESSION
#


# algoritm to generate session key:
# values: "randomstring" (default) or "uuid".
# session.id.generation=uuid
session.id.generation=randomstring

# length of generated session key when using "randomstring" generation
session.id.size=10

# Overall session lease time in minutes
# Mainly used for clients that do not perform
# listening, e.g. when publishing only.
session.timeout.mins=5

#
# EVENT QUEUE
#
# Properties for per-client data event queue

# Size for
queue.size=24
queue.read.timeout.millis=20000
queue.write.timeout.millis=20

#
# LISTENING MODE
#

# You may force all clients to use pull mode
# for scalability
listen.force.pull.all=true

#
# Comma-separated list of User Agent substrings.
# Force these browsers to use pull mode, since they
# don't support JS streaming, matching is done using
# String.indexOf() with lowercased agent strings
# use multiple criteria with &.
#
listen.force.pull.agents=safari

#
# PULL MODE
#

# time server should wait on refresing pull client
pull.refresh.timeout.millis=45000

# minimum/maximum wait time client should wait before refreshing
# server provides a random time between these values
pull.refresh.wait.min.millis=2000
pull.refresh.wait.max.millis=6000

#
# POLL MODE
#

# time server should wait on refresing poll client
poll.refresh.timeout.millis=60000

# minimum/maximum wait time client should wait before refreshing
# server provides a random time between these values
poll.refresh.wait.min.millis=6000
poll.refresh.wait.max.millis=10000
