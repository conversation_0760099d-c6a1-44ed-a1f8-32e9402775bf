<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj="INSERT";var ak=lang["DlgComInsert"];var jE="";var aS="#CCCCCC";var hD="#000000";var tR=true;if(I.ay()=="Control"){var aB=I.aX();if(aB.tagName=="APPLET"){if(aB.getAttribute("code",2).toLowerCase()=="webeq3.viewercontrol"){aj="MODI";ak=lang["DlgComModify"];jE=oc(aB.innerHTML,"eq");jE=jE.replace(/&amp;/,"&");aS=oc(aB.innerHTML,"background");hD=oc(aB.innerHTML,"foreground");}}}var Q=lang["DlgEQ"]+"("+ak+")";document.write("<title>"+Q+"</title>");function oc(html,vl){var P=new RegExp("<param name=\""+vl+"\" value=\"(.*?)\">","gi");var sR=P.exec(html);if(sR){return sR[1];}return "";};function ah(){lang.TranslatePage(document);try{$("d_eq").setMathML(jE);}catch(e){tR=false;}if(tR){$("v_normal").style.display="";$("d_bgcolor").value=aS;$("s_bgcolor").style.backgroundColor=aS;$("d_forecolor").value=hD;$("s_forecolor").style.backgroundColor=hD;}else{$("v_install").style.display="";$("v_installing").innerHTML="<OBJECT CLASSID='clsid:41649A90-B484-11d1-8D75-00C04FC24EE6' CODEBASE='WebEQInstall.cab#Version=3,0,1,6' HEIGHT=1 WIDTH=1></OBJECT>";}parent.bp(Q);};function ok(){aS=$("d_bgcolor").value;hD=$("d_forecolor").value;jE=$("d_eq").getPackedMathML();if(!lU(aS)){cv($("d_bgcolor"),lang["ErrColorBg"]);return;}if(aS==""){aS="#FFFFFF";}if(hD==""){hD="#000000";}EWIN.insertHTML("<APPLET codeBase=./ height=100 width=320 code=webeq3.ViewerControl><PARAM NAME=\"foreground\" VALUE=\""+hD+"\"><PARAM NAME=\"background\" VALUE=\""+aS+"\"><PARAM NAME=\"size\" VALUE=\"18\"><PARAM NAME=\"eq\" VALUE=\""+jE+"\"></APPLET>");parent.aT();}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table id=v_install border=0 cellpadding=5 cellspacing=10 style="display:none" align=center> <tr><td bgcolor=#ffffff noWrap><span lang=DlgEQInstallAlt></span></td></tr> </table> <div id=v_installing style="display:none"></div> <table border=0 cellpadding=0 cellspacing=0 align=center id=v_normal style="display:none"> <tr> <td colspan=2> <object code='webeq3.editor.InputControl' width=620 height=200 id=d_eq MAYSCRIPT><param name=eq value=''></object> </td> </tr> <tr><td height=8 colspan=2></td></tr> <tr> <td noWrap><span lang=DlgEQBackground></span>:<input type=text id=d_bgcolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bgcolor onclick="fX('bgcolor')" align=absmiddle>&nbsp; <span lang=DlgEQForeground></span>:<input type=text id=d_forecolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_forecolor onclick="fX('forecolor')" align=absmiddle></td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel></td> </tr> </table> </td></tr></table> </body> </html> 