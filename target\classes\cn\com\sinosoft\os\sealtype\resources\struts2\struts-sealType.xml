<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="sealType" extends="bsp-default" namespace="/cn/com/sinosoft/os/sealtype">
		<!-- 查询 -->
		<action name="qrySealType" class="sealTypeAction" method="qryParentInput">
			<param name="sessionGroup">sealType</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealtype/qrySealType.jsp
			</result>
		</action>
		<action name="qrySealTypeList" class="commonQueryAction">
			<param name="sessionGroup">sealType</param>
			<param name="queryCode">QRY_OS_SEAL_TYPE</param>
			<param name="resultName">qryList</param>
		</action>
		<!-- 查看 -->
		<action name="sealType_viewParent" class="sealTypeAction" method="viewParent">
			<param name="sessionGroup">sealType</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealtype/edtSealType.jsp
			</result>
		</action>
		<!-- 添加 -->
		<action name="sealType_addParentInput" class="sealTypeAction" method="addParentInput">
			<param name="sessionGroup">sealType</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealtype/edtSealType.jsp
			</result>
		</action>
		<action name="sealType_addParentSubmit" class="sealTypeAction" method="addParentSubmit">
			<param name="sessionGroup">sealType</param>
		</action>
		<!-- 修改 -->
		<action name="sealType_edtParentInput" class="sealTypeAction" method="edtParentInput">
			<param name="sessionGroup">sealType</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealtype/edtSealType.jsp
			</result>
		</action>
		<action name="sealType_edtParentSubmit" class="sealTypeAction" method="edtParentSubmit">
			<param name="sessionGroup">sealType</param>
		</action>
		<!-- 删除 -->
		<action name="sealType_delParentSubmit" class="sealTypeAction" method="delParentSubmit">
			<param name="sessionGroup">sealType</param>
		</action>
	</package>
</struts>