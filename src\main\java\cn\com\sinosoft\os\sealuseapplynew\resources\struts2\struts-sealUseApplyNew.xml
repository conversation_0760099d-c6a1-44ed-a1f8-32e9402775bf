<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="sealuseapplynew" extends="bsp-default" namespace="/cn/com/sinosoft/os/sealuseapplynew">
		<!-- 查询 -->
		<action name="qrySealUseApplyNew" class="sealUseApplyNewAction" method="qryParentInput">
			<param name="sessionGroup">sealUseApplyNew</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapplynew/qrySealUseApplyNew.jsp
			</result>
		</action>
		<action name="qrySealUseApplyNewList" class="commonQueryAction">
			<param name="sessionGroup">sealUseApplyNew</param>
			<param name="queryCode">QRY_OS_SEAL_USE_APPLY_NEW</param>
			<param name="resultName">qryList</param>
		</action>
		<!-- 查看 -->
		<action name="sealUseApplyNew_viewParent" class="sealUseApplyNewAction" method="viewParent">
			<param name="sessionGroup">sealUseApplyNew</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapplynew/edtSealUseApplyNew.jsp
			</result>
		</action>
		<!-- 添加 -->
		<action name="sealUseApplyNew_addParentInput" class="sealUseApplyNewAction" method="addParentInput">
			<param name="sessionGroup">sealUseApplyNew</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapplynew/edtSealUseApplyNew.jsp
			</result>
		</action>
		<action name="sealUseApplyNew_addParentSubmit" class="sealUseApplyNewAction" method="addParentSubmit">
			<param name="sessionGroup">sealUseApplyNew</param>
		</action>
		<!-- 修改 -->
		<action name="sealUseApplyNew_edtParentInput" class="sealUseApplyNewAction" method="edtParentInput">
			<param name="sessionGroup">sealUseApplyNew</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapplynew/edtSealUseApplyNew.jsp
			</result>
		</action>
		<action name="sealUseApplyNew_edtParentSubmit" class="sealUseApplyNewAction" method="edtParentSubmit">
			<param name="sessionGroup">sealUseApplyNew</param>
		</action>
		<!-- 删除 -->
		<action name="sealUseApplyNew_delParentSubmit" class="sealUseApplyNewAction" method="delParentSubmit">
			<param name="sessionGroup">sealUseApplyNew</param>
		</action>
		
		<!-- 打开审批页面 -->
		<action name="sealUseApplyNew_auditParentInput" class="sealUseApplyNewAction" method="auditParentInput">
			<param name="sessionGroup">sealUseApplyNew</param>
			<result name="success">
			     /WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapplynew/auditSealUseApplyNew.jsp
			</result>
		</action>
		<!-- 打开订正页面 -->
		<action name="sealUseApplyNew_auditeitParentInput" class="sealUseApplyNewAction" method="auditParentInput">
			<param name="sessionGroup">sealUseApplyNew</param>
			<result name="success">
			    /WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapplynew/edtSealUseApplyNew.jsp
			</result>
		</action>
		
		<!-- 办理 -->
		<action name="goToDo" class="WorkflowAction" method="goToDo">
			<param name="sessionGroup">workflow</param>
		</action>
		
		<!-- 审批 applySubmit-->
		<action name="auditsealUseApplyNewSubmit" class="sealUseApplyNewAction" method="applySubmit">
			<param name="sessionGroup">sealUseApplyNew</param>
		</action>
		
				<!-- 附件在线编辑-->
		<action name="sealUseApplyNew_OnlineEdit" class="sealUseApplyNewAction" method="onlineEditInput">
			<param name="sessionGroup">sealUseApplyNew</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/sealuseapplynew/viewForWebOffice.jsp
			</result>
		</action>
		
		<!-- 在线编辑保存提交-->
		<action name="sealUseApplyNew_onlineEditSubmit" class="sealUseApplyNewAction" method="onlineEditSubmit">
			<param name="sessionGroup">sealUseApplyNew</param>
		</action>
		
		<!-- 导出申请单 -->
		<action name="sealUseApplyNewWordExp" class="sealUseApplyNewAction" method="wordExp">
			<param name="sessionGroup">sealUseApplyNew</param>
		</action>
		
		<!-- 打印word -->
		 <action name="printWordSealUseApplyNew" class="sealUseApplyNewAction" method="printWordArticlePublishe">
			<param name="sessionGroup">sealUseApplyNew</param>
		</action>
		
		<!-- 导出Excel -->
		 <action name="SealUseApplyNewExcel" class="sealUseApplyNewAction" method="exportExcel">
			<param name="sessionGroup">sealUseApplyNew</param>
		</action>
		
	</package>
</struts>