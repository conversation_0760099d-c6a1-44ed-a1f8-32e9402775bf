<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE taglib PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.1//EN"
                        "http://java.sun.com/j2ee/dtds/web-jsptaglibrary_1_1.dtd">
<taglib>
	<tlibversion>1.0</tlibversion>
	<jspversion>1.2</jspversion>
	<shortname>BspHtml</shortname>
	<tag>
		<name>TextBox</name>
		<tagclass>ie.bsp.ui.jsptag.BspTextBoxTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>inputStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnLeave</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>vtype</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>emailErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>urlErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>floatErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>intErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dateErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rangeLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rangeErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
	</tag>
	<tag>
		<name>Password</name>
		<tagclass>ie.bsp.ui.jsptag.BspPasswordTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>inputStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnLeave</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>vtype</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>emailErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>urlErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>floatErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>intErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dateErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rangeLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rangeErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>TextArea</name>
		<tagclass>ie.bsp.ui.jsptag.BspTextAreaTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>inputStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnLeave</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>vtype</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>emailErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>urlErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>floatErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>intErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dateErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rangeLengthErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rangeErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>Hidden</name>
		<tagclass>ie.bsp.ui.jsptag.BspHiddenTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>TextBoxList</name>
		<tagclass>ie.bsp.ui.jsptag.BspTextBoxListTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>url</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dataField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>ButtonEdit</name>
		<tagclass>ie.bsp.ui.jsptag.BspButtonEditTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>ComboBox</name>
		<tagclass>ie.bsp.ui.jsptag.BspComboBoxTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dataField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>data</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>url</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columns</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showNullItem</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nullItemText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueFromSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>HtmlFile</name>
		<tagclass>ie.bsp.ui.jsptag.BspHtmlFileTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>buttonText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>limitType</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfileselect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>MultiHtmlFile</name>
		<tagclass>ie.bsp.ui.jsptag.BspMultiHtmlFileTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>Calendar</name>
		<tagclass>ie.bsp.ui.jsptag.BspCalendarTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columns</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>rows</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewDate</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTime</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>timeFormat</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showHeader</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showFooter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showWeekNumber</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showDaysHeader</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showMonthButtons</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showYearButtons</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTodayButton</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClearButton</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondatechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ontimechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>drawdate</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dateclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>DatePicker</name>
		<tagclass>ie.bsp.ui.jsptag.BspDatePickerTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>format</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>timeFormat</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewDate</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minDate</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxDate</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTime</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTodayButton</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showOkButton</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClearButton</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>Spinner</name>
		<tagclass>ie.bsp.ui.jsptag.BspSpinnerTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>format</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minValue</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxValue</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>increment</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>decimalPlaces</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowLimitValue</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>TimeSpinner</name>
		<tagclass>ie.bsp.ui.jsptag.BspTimeSpinnerTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>format</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>TreeSelect</name>
		<tagclass>ie.bsp.ui.jsptag.BspTreeSelectTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>showFolderCheckBox</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>data</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>url</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>parentField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>resultAsTree</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>checkRecursive</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTreeIcon</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTreeLines</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>autoCheckParent</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>expandOnLoad</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueFromSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onnodeclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforenodecheck</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforenodeselect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>virtualScroll</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>AutoComplete</name>
		<tagclass>ie.bsp.ui.jsptag.BspAutoCompleteTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>searchField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>qryTextCode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dataField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>data</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>url</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columns</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showNullItem</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nullItemText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueFromSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>ListBox</name>
		<tagclass>ie.bsp.ui.jsptag.BspListBoxTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columns</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>data</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>url</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dataField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showCheckBox</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showAllCheckBox</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showNullItem</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nullItemText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemdblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onloaderror</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>CheckBoxList</name>
		<tagclass>ie.bsp.ui.jsptag.BspCheckBoxListTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>repeatLayout</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>repeatDirection</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>repeatItems</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columns</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>data</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>url</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dataField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showCheckBox</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showAllCheckBox</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showNullItem</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nullItemText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemdblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onloaderror</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>RadioButtonList</name>
		<tagclass>ie.bsp.ui.jsptag.BspRadioButtonListTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>repeatLayout</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>repeatDirection</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>repeatItems</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columns</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>data</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>url</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dataField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showCheckBox</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showAllCheckBox</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showNullItem</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nullItemText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemdblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onloaderror</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>ZoneCode</name>
		<tagclass>ie.bsp.ui.jsptag.BspZoneCodeTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>data</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>url</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>parentField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>resultAsTree</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>checkRecursive</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTreeIcon</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTreeLines</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>autoCheckParent</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>expandOnLoad</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueFromSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onnodeclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforenodecheck</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforenodeselect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>topLevel</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>zs</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>orgType</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>org</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showUnknown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTop</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>OrgCode</name>
		<tagclass>ie.bsp.ui.jsptag.BspOrgCodeTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>data</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columns</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showNullItem</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nullItemText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueFromSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>department</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showUser</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>Tree</name>
		<tagclass>ie.bsp.ui.jsptag.BspTreeTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>contextMenu</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>data</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>url</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>idField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>iconField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nodesField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>parentField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>resultAsTree</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dataField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>checkedField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>checkRecursive</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>autoCheckParent</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>expandOnLoad</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTreeIcon</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTreeLines</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showCheckBox</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showFolderCheckBox</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showExpandButtons</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enableHotTrack</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>expandOnDblClick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>expandOnNodeClick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>removeOnCollapse</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowLeafDropIn</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowDrag</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowDrop</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dragGroupName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dropGroupName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondrawnode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onnodedblclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onnodeclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onnodemousedown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondragstart</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforenodeselect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onnodeselect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onpreload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onloaderror</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onloadnode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onendedit</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforenodecheck</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onnodecheck</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeexpand</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onexpand</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforecollapse</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncollapse</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforedrop</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondrop</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ongivefeedback</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>CheckBox</name>
		<tagclass>ie.bsp.ui.jsptag.BspCheckboxTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>checked</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>trueValue</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>falseValue</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncheckedchanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>Department</name>
		<tagclass>ie.bsp.ui.jsptag.BspDepartmentTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>data</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>resultAsTree</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>checkRecursive</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTreeIcon</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTreeLines</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>autoCheckParent</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>expandOnLoad</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueFromSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onnodeclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforenodecheck</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforenodeselect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showUser</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>MonthPicker</name>
		<tagclass>ie.bsp.ui.jsptag.BspMonthPickerTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>allowInput</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>format</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>timeFormat</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewDate</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minDate</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxDate</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTime</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showTodayButton</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showOkButton</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClearButton</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>privacy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>Year</name>
		<tagclass>ie.bsp.ui.jsptag.BspYearTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columns</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showNullItem</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nullItemText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueFromSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>beginYear</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>endYear</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>Month</name>
		<tagclass>ie.bsp.ui.jsptag.BspMonthTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columns</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showNullItem</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nullItemText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueFromSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>beginMonth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>endMonth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>Week</name>
		<tagclass>ie.bsp.ui.jsptag.BspWeekTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columns</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showNullItem</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nullItemText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueFromSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>beginWeek</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>endWeek</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showDate</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>yearCtl</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>Hour</name>
		<tagclass>ie.bsp.ui.jsptag.BspHourTag</tagclass>
		<bodycontent>JSP</bodycontent>
		<attribute>
			<name>emptyText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>requiredErrorText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textField</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>multiSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columns</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showNullItem</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>nullItemText</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>valueFromSelect</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onitemclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupWidth</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popupHeight</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>popup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onshowpopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onhidepopup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>text</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>value</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>textName</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>selectOnFocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>minLength</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>required</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>errorMode</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>validateOnChanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>showClose</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvaluechanged</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbuttonclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onvalidation</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onenter</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onblur</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>oncloseclick</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>addState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>editState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>pageState</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>visible</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>enabled</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cls</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>style</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>borderStyle</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>width</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>height</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>tooltip</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>ondestroy</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onbeforeload</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>beginHour</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>endHour</name>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
</taglib>
