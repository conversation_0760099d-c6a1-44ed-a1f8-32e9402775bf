<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj="INSERT";var ak=lang["DlgComInsert"];var D;var eC="";var iV="";if(I.ay()=="Control"){D=I.aX();if(D.tagName=="MARQUEE"){aj="MODI";ak=lang["DlgComModify"];iV=D.behavior;eC=D.innerHTML;}}var Q=lang["DlgMarquee"]+"("+ak+")";document.write("<title>"+Q+"</title>");function ah(){lang.TranslatePage(document);$("d_text").value=eC;switch(iV){case "scroll":$("d_behavior_scroll").checked=true;break;case "slide":$("d_behavior_slide").checked=true;break;default:iV="alternate";$("d_behavior_alternate").checked=true;break;}parent.bp(Q);};function ok(){eC=$("d_text").value;iV=vL("d_behavior");if(aj=="MODI"){D.behavior=iV;D.innerHTML=eC;}else{EWIN.insertHTML("<marquee behavior='"+iV+"'>"+eC+"</marquee>");}parent.aT();};function vL(bn){var K=document.getElementsByName(bn);for(var i=0;i<K.length;i++){if(K[i].checked){return K[i].value;}}}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr><td> <FIELDSET> <LEGEND></LEGEND> <table border=0 cellspacing=0 cellpadding=5 width="100%"> <tr><td> <table border=0 cellspacing=2 cellpadding=0 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgMarqueeText></span>:</td> <td noWrap width="80%"><input type=text id="d_text" size=40 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgMarqueeBehavior></span>:</td> <td noWrap width="80%"><input type="radio" name="d_behavior" id="d_behavior_scroll" value="scroll"><label for="d_behavior_scroll"><span lang=DlgMarqueeScroll></span></label> <input type="radio" name="d_behavior" id="d_behavior_slide" value="slide"><label for="d_behavior_slide"><span lang=DlgMarqueeSlide></span></label> <input type="radio" name="d_behavior" id="d_behavior_alternate" value="alternate"><label for="d_behavior_alternate"><span lang=DlgMarqueeAlternate></span></label></td> </tr> </table> </td></tr> </table> </FIELDSET> </td></tr> <tr><td height=10></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html> 