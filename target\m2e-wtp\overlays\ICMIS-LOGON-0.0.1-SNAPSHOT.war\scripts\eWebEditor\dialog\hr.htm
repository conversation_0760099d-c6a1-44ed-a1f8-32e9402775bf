<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ="INSERT";var aa=lang["DlgComInsert"];var t;var mH;var wI;var wO;var bO="";var bD="";var cD="";var cX="";if(C.ai()=="Control"){t=C.ax();if(t.tagName=="HR"){aJ="MODI";aa=lang["DlgComModify"];bO=eZ(t,"width");bD=t.style.height;if(!bD){bD=dE(t,"size");}cD=dE(t,"align");cX=eZ(t,"color");if(!cX){cX=t.style.backgroundColor;}}}var bm=lang["DlgHr"]+"("+aa+")";document.write("<title>"+bm+"</title>");function aq(){lang.ag(document);aC($("d_align"),cD.toLowerCase());$("d_color").value=cX;$("s_color").style.backgroundColor=cX;$("d_width").value=bO;$("d_height").value=bD;parent.ar(bm);};function fd(obj,kR){var b=false;if(obj.value!=""){obj.value=parseFloat(obj.value);if(obj.value!="0"){b=true;}}if(b==false){bX(obj,kR);return false;}return true;};function ok(){bO=$("d_width").value;bD=$("d_height").value;cX=$("d_color").value;if(!oV(cX)){bX($("d_color"),lang["ErrColorInvalid"]);return;}cD=$("d_align").options[$("d_align").selectedIndex].value;if(aJ=="MODI"){t.style.width=bO;bq(t,"size",bD);t.align=cD;t.style.backgroundColor=cX;t.style.color=cX;}else{var Bc='';if(cX){Bc+='color:'+cX+';background-color:'+cX+';';}if(bO){Bc+='width:'+bO+';';}var V='<hr';if(cD){V+=' align="'+cD+'"';}if(Bc){V+=' style="'+Bc+'"';}if(bD){V+=' size="'+bD+'"';}V+='>';EWIN.insertHTML(V);}parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgComWidth></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_width size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgComHeight></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_height size=10 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgComAlign></span>:</td> <td noWrap width="29%"><select id=d_align size=1 style="width:80px"><option value='' lang=DlgComDefault></option><option value='left' lang=DlgAlignLeft></option><option value='center' lang=DlgAlignCenter></option><option value='right' lang=DlgAlignRight></option></select></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgComColor></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_color size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_color onclick="hu('color')" align=absmiddle></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id=d_ok lang=DlgBtnOK onclick="ok()">&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html>