package cn.com.sinosoft.os.constant;

import net.sf.json.JSONObject;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.*;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static java.lang.System.out;

public class HttpRequestUtil {

	// this is config
	private static final String REQ_ENCODEING_UTF8 = "utf-8";
	private static PoolingHttpClientConnectionManager httpClientConnectionManager;

	private static Logger logger = LoggerFactory.getLogger(HttpRequestUtil.class);

	public HttpRequestUtil() {
		httpClientConnectionManager = new PoolingHttpClientConnectionManager();
		httpClientConnectionManager.setMaxTotal(100);
		httpClientConnectionManager.setDefaultMaxPerRoute(20);
	}

	// get 请求
	public static String httpGet(String url, Header[] headers) throws Exception {
		HttpUriRequest uriRequest = new HttpGet(url);
		if (null != headers)
			uriRequest.setHeaders(headers);
		CloseableHttpClient httpClient = null;
		try {
			httpClient = declareHttpClientSSL(url);
			CloseableHttpResponse httpresponse = httpClient.execute(uriRequest);
			HttpEntity httpEntity = httpresponse.getEntity();
			String result = EntityUtils.toString(httpEntity, REQ_ENCODEING_UTF8);
			return result;
		} catch (ClientProtocolException e) {
			out.println(String.format("http请求失败，uri{%s},exception{%s}", new Object[] { url, e }));
		} catch (IOException e) {
			out.println(String.format("IO Exception，uri{%s},exception{%s}", new Object[] { url, e }));
		} finally {
			if (null != httpClient)
				httpClient.close();
		}
		return null;
	}

	// get 请求
	public static String httpGet(String url) throws Exception {
		return httpGet(url, null);
	}

	// post 请求
	public static String httpPost(String url, String params) throws Exception {
		HttpPost post = new HttpPost(url);
		post.addHeader("Content-Type", "application/json;charset=" + REQ_ENCODEING_UTF8);
		// 设置传输编码格式
		StringEntity stringEntity = new StringEntity(params, REQ_ENCODEING_UTF8);
		stringEntity.setContentEncoding(REQ_ENCODEING_UTF8);
		post.setEntity(stringEntity);
		HttpResponse httpresponse = null;
		CloseableHttpClient httpClient = null;
		try {
			httpClient = declareHttpClientSSL(url);
			httpresponse = httpClient.execute(post);
			HttpEntity httpEntity = httpresponse.getEntity();
			String result = EntityUtils.toString(httpEntity, REQ_ENCODEING_UTF8);
			return result;
		} catch (ClientProtocolException e) {
			out.println(String.format("http请求失败，uri{%s},exception{%s}", new Object[] { url, e }));
		} catch (IOException e) {
			out.println(String.format("IO Exception，uri{%s},exception{%s}", new Object[] { url, e }));
		} finally {
			if (null != httpClient)
				httpClient.close();
		}
		return null;
	}

	public static String httpPost(String url, String params, Header[] headers) throws Exception {
		HttpPost post = new HttpPost(url);
		if (null != headers)
			post.setHeaders(headers);
		if (post.getHeaders("Content-Type") == null)
			post.addHeader("Content-Type", "application/json;charset=" + REQ_ENCODEING_UTF8);
		// 设置传输编码格式
		StringEntity stringEntity = new StringEntity(params, REQ_ENCODEING_UTF8);
		stringEntity.setContentEncoding(REQ_ENCODEING_UTF8);
		post.setEntity(stringEntity);
		HttpResponse httpresponse = null;
		CloseableHttpClient httpClient = null;
		try {
			httpClient = declareHttpClientSSL(url);
			httpresponse = httpClient.execute(post);
			HttpEntity httpEntity = httpresponse.getEntity();
			String result = EntityUtils.toString(httpEntity, REQ_ENCODEING_UTF8);
			return result;
		} catch (ClientProtocolException e) {
			out.println(String.format("http请求失败，uri{%s},exception{%s}", new Object[] { url, e }));
		} catch (IOException e) {
			out.println(String.format("IO Exception，uri{%s},exception{%s}", new Object[] { url, e }));
		} finally {
			if (null != httpClient)
				httpClient.close();
		}
		return null;
	}

	/**
	 * post请求提交form-data上传文件
	 *
	 * @param url     上传地址
	 * @param headers 请求头
	 * @param files   上传文件
	 * @return
	 */
	public static String doPostUploadFile(String url, Map<String, String> params, Header[] headers, List<File> files)
			throws Exception {
		HttpPost httpPost = new HttpPost(url);
		if (null != headers)
			httpPost.setHeaders(headers);

		CloseableHttpResponse response = null;

		String respContent = null;

		long startTime = System.currentTimeMillis();

		// 设置请求头 boundary边界不可重复，重复会导致提交失败
		String boundary = "-------------------------" + UUID.randomUUID().toString();
		httpPost.setHeader("Content-Type", "multipart/form-data; boundary=" + boundary);
		httpPost.setHeader("accept-language", "zh-CN");

		// 创建MultipartEntityBuilder
		MultipartEntityBuilder builder = MultipartEntityBuilder.create();
		// 设置字符编码
		builder.setCharset(StandardCharsets.UTF_8);
		// 模拟浏览器
		builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
		// 设置边界
		builder.setBoundary(boundary);

		// 设置multipart/form-data流文件
		if (files != null && files.size() > 0) {
			for (File file : files) {
				builder.addPart("files", new FileBody(file));
			}
//            String fileName = file.getName();
//            builder.addPart("files", new FileBody(file));
			// application/octet-stream代表不知道是什么格式的文件
//            builder.addBinaryBody("media", file, ContentType.create("application/octet-stream"), fileName);
		}

		ContentType contentType = ContentType.create("text/plain", Charset.forName("UTF-8"));
		params.forEach((k, v) -> {
			builder.addTextBody(k, v, contentType);
		});

		HttpEntity entity = builder.build();
		httpPost.setEntity(entity);
		CloseableHttpClient httpClient = HttpClients.createDefault();
		try {
			response = httpClient.execute(httpPost);
			if (response != null && response.getStatusLine() != null
					&& response.getStatusLine().getStatusCode() < 400) {
				HttpEntity he = response.getEntity();
				if (he != null) {
					respContent = EntityUtils.toString(he, "UTF-8");
				}
			} else {
				logger.error("对方响应的状态码不在符合的范围内!");
				throw new RuntimeException();
			}
			return respContent;
		} catch (Exception e) {
			logger.error("网络访问异常,请求url地址={},响应体={},error={}", url, response, e);
			throw new RuntimeException();
		} finally {
			logger.info("统一外网请求参数打印,post请求url地址={},响应={},耗时={}毫秒", url, respContent,
					(System.currentTimeMillis() - startTime));
			try {
				if (response != null) {
					response.close();
				}
				if (null != httpClient) {
					httpClient.close();
				}
			} catch (IOException e) {
				logger.error("请求链接释放异常", e);
			}
		}
	}

	private static CloseableHttpClient declareHttpClientSSL(String url) {
		if (url.startsWith("https://")) {
			return sslClient();
		} else {
			return HttpClientBuilder.create().setConnectionManager(httpClientConnectionManager).build();
		}
	}

	/**
	 * 设置SSL请求处理
	 */
	private static CloseableHttpClient sslClient() {
		try {
			SSLContext ctx = SSLContext.getInstance("TLS");
			X509TrustManager tm = new X509TrustManager() {
				public X509Certificate[] getAcceptedIssuers() {
					return null;
				}

				public void checkClientTrusted(X509Certificate[] xcs, String str) {
				}

				public void checkServerTrusted(X509Certificate[] xcs, String str) {
				}
			};
			ctx.init(null, new TrustManager[] { tm }, null);
			SSLConnectionSocketFactory sslConnectionSocketFactory = SSLConnectionSocketFactory.getSocketFactory();
			return HttpClients.custom().setSSLSocketFactory(sslConnectionSocketFactory).build();
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException(e);
		} catch (KeyManagementException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 测试接口
	 * 
	 * @param args
	 * @throws Exception
	 */
	/*
	 * public static void main(String[] args) throws Exception {
	 *//**
		 * 使用的maven依赖 <dependency> <groupId>org.apache.httpcomponents</groupId>
		 * <artifactId>httpclient</artifactId> <version>4.5.3</version> </dependency>
		 * <dependency> <groupId>org.apache.httpcomponents</groupId>
		 * <artifactId>httpcore</artifactId> <version>4.4.9</version> </dependency>
		 * <dependency> <groupId>org.apache.httpcomponents</groupId>
		 * <artifactId>httpmime</artifactId> <version>4.5.12</version> </dependency>
		 *//*
			 * // 准备：档案服务IP地址，请联系管理员索要 // String ipAddr = "http://*************:8090";
			 * String ipAddr = "http://127.0.0.1:8090";
			 * 
			 * // 1、获取token String getTokenUrl = ipAddr + "/oa/getOAToken"; JSONObject
			 * params = new JSONObject(); params.put("username", "oa");
			 * params.put("password", "123456"); String res =
			 * HttpRequestUtil.httpPost(getTokenUrl, params.toString()); JSONObject
			 * jsonObject = JSONObject.fromObject(res);
			 * out.println(">>>>>>>>>>>>>>token result: " + jsonObject.toString());
			 * //{"resultCode":1000,"message":"获取令牌成功，请在有效期内使用","data":
			 * "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbk5hbWUiOiJvYSIsImV4cCI6MTY5MDUzNTIzNCwidXNlcklkIjoiMSJ9.V61XjkykIyYyp8vUe6s0wjSBz3w_IVUVAUMcARM9APw"
			 * ,"dataReserve1":null,"dataReserve2":null,"dataReserve3":null,"dataReserve4":
			 * null} String token = jsonObject.getString("data");
			 * 
			 * // 2、调用传递数据接口 HttpHead reqHeader = new HttpHead();
			 * reqHeader.setHeader("Access-Token", token); String boundary =
			 * "-------------------------" + UUID.randomUUID().toString();
			 * reqHeader.setHeader("Content-Type", "multipart/form-data; boundary=" +
			 * boundary); String sendDataUrl = ipAddr + "/oa/addArchiveByJsonData";
			 * //参数具体查看文档要求 Map<String, String> paramsMap = new HashMap<>(); UUID randomUUID
			 * = UUID.randomUUID(); //唯一id paramsMap.put("uniqueId", randomUUID.toString());
			 * //数据字符串 paramsMap.put("dataObjStr",
			 * "{ \"f_nd\": 2001, \"f_ajsxh\": 1, \"f_jnwjfs\": 1, \"f_bgqx\": \"10年\", \"f_flh\": \"DQ13\", \"f_gjh\": \"DQ13-2001-1\", \"f_ssbm\": \"档案馆\", \"f_flmc\": \"组织工作\", \"f_tm\": \"2001年度测试数据\", \"status\": 1, \"f_bgnx\": 10 }"
			 * ); //附件 File[] files = new File[] { new
			 * File("/Users/<USER>/Desktop/拼多多商家电子发票.pdf"), new
			 * File("/Users/<USER>/Desktop/W-4-155-1.pdf") }; String sendDataRes =
			 * HttpRequestUtil.doPostUploadFile(sendDataUrl, paramsMap,
			 * reqHeader.getAllHeaders(), files); JSONObject sendResultObj =
			 * JSONObject.fromObject(sendDataRes);
			 * out.println(">>>>>>>>>>>>>>sendData result: " + sendResultObj.toString());
			 * //>>>>>>>>>>>>>>sendData result:
			 * {"resultCode":1000,"message":"数据处理成功","data":null,"dataReserve1":null,
			 * "dataReserve2":null,"dataReserve3":null,"dataReserve4":null}
			 * 
			 * }
			 */

}
