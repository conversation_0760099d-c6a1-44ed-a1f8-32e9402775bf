<?xml version="1.0" encoding="UTF-8"?>
<configuration name="Flex/GIS/webservice应用及服务配置">
	<gistype name="服务类型，可选参数：supermap/arcgis">supermap</gistype>
	<tiledMap name="是否切片：true/false">true</tiledMap>
	
	<!-- 吉林 http://222.161.230.107:8090/iserver/services/map-jlzzpt/rest/maps/jlmap1 -->
	<basemap name="GIS图层基础服务(内网)">http://222.161.230.107:8090/iserver/services/map-jlzzpt/rest/maps/jlmap1</basemap>
	<!-- 吉林 http://10.30.31.22:8090/iserver/services/map-jlzzpt/rest/maps/jlmap1 -->
	<!--云南  http://10.30.31.22:8399/arcgis/rest/services/ynsyd/MapServer   -->
	<!-- 河南 http://10.64.10.146:8399/arcgis/rest/services/HNPT1/MapServer -->
	<basemapInner name="GIS图层基础服务(外网)">http://222.161.230.107:8090/iserver/services/map-jlzzpt/rest/maps/jlmap1</basemapInner>
	
	
	<!-- 吉林 http://222.161.230.107:8399/arcgis/rest/services/jlptmap/MapServer/-->
	<arcgisbasemap name="arcgis服务(内网)">http://222.161.230.107:8399/arcgis/rest/services/jlptmap/MapServer/</arcgisbasemap>
	<!-- 吉林 http://10.30.31.22:8399/arcgis/rest/services/jlptmap/MapServer/  -->
	<arcgisbasemapInner name="arcgis服务(外网)">http://222.161.230.107:8399/arcgis/rest/services/jlptmap/MapServer/</arcgisbasemapInner>
	
	<!-- 吉林 http://222.161.230.107:8090/iserver/services/map-JLDD/rest/maps/jld -->
	<queryTask name="图层资源查询服务(内网)">http://222.161.230.107:8090/iserver/services/map-JLDD/rest/maps/jld</queryTask>
	
	<!--吉林 http://10.30.31.22:8090/iserver/services/map-JLDD/rest/maps/jld     http://10.30.31.22:8090/iserver/services/map-jlzzpt/rest/maps/jlmap1(废弃) -->
	<!--云南 http://10.30.31.22:8399/arcgis/rest/services/bssmap/MapServer/0   -->
	<!-- 河南 http://10.64.10.146:8399/arcgis/rest/services/HNPT1/MapServer/0 -->
	<queryTaskInner name="图层资源查询服务(外网)">http://222.161.230.107:8090/iserver/services/map-JLDD/rest/maps/jld</queryTaskInner>
	
	
	
 
	<arcgisGeometryService name="分析服务(内网)">http://222.161.230.107:8399/arcgis/rest/services/Geometry/GeometryServer</arcgisGeometryService>
	<arcgisGeometryServiceInner name="分析服务(外网)">http://222.161.230.107:8399/arcgis/rest/services/Geometry/GeometryServer</arcgisGeometryServiceInner>
 
	<!-- 指挥调度专用接口 -->
	<argisZhddBaseWebservice name="指挥调度webservice">/ws/ZhdbService?wsdl</argisZhddBaseWebservice>
	
	<baseWebservice name="基础webservice服务">/ws/GisService?wsdl</baseWebservice>
	<zhddBaseWebservice name="指挥调度webservice服务01">/ws/YjService?wsdl</zhddBaseWebservice>
	<gisWebservice name="指挥调度webservice服务02">/ws/DirectService?wsdl</gisWebservice>
	<jltjBaseWebservice name="统计webservice服务">/ws/StatService?wsdl</jltjBaseWebservice>
	<evtServiceWebservice name="突发事件webservice服务">/ws/EVTService?wsdl</evtServiceWebservice>
	
	<tiledmap>false</tiledmap>
	<gisfilename name="指挥调度目录（Flex专用）">/JLYJFLEX/</gisfilename>
	<webroot name="系统访问地址(Flex专用)">/JLYJ</webroot>
	<webInnerUrl name="外网地址（Flex专用）">222.161.230.107</webInnerUrl>
	<defaultzonecode name="缺省地区编码">22000000</defaultzonecode>
	<evtSyResultYear name="是否只查询当年突发事件：true/false">true</evtSyResultYear>
   	<county name="地图里图层的名字">区县界@regionR@jlyj</county>
	<city name="地图里图层的名字">0</city>
	<queryZonecode name="图层里要查询的地区字段">DISTRICT</queryZonecode>
	<queryName name="图层里要查询的名称字段">NAME</queryName>
	<layerCode name="模糊检索用地区编码(前两位)">22</layerCode>
	<field_name name="查询字段，吉林：NAME/云南：Name">NAME</field_name>
	<field_address name="查询字段，吉林：ADDRESS/云南： Address">ADDRESS</field_address>
	<field_pointX name="查询字段，吉林：POINT_X/云南： POINT_X">POINT_X</field_pointX>
	<field_pointY name="查询字段，吉林：POINT_Y/云南： POINT_Y">POINT_Y</field_pointY>
</configuration>