<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
	http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<!-- For mail settings and future properties files -->

	<bean id="propertyConfigurer"
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<!--pzx 加载属性文件 -->
			<list>
				<value>classpath:jdbc.properties</value>
				<value>classpath:mail.properties</value>
			</list>
		</property>
	</bean>

	<!--pzx proxool连接池的配置 -->
	<!-- <bean id="dataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource"> 
		<property name="driverClassName"> <value>org.logicalcobwebs.proxool.ProxoolDriver</value> 
		</property> <property name="url"> <value>proxool.dbpool</value> </property> 
		</bean> -->
	<bean id="dataSource" class="org.logicalcobwebs.proxool.ProxoolDataSource">
		<property name="driver">
			<value>${jdbc.driverClassName}</value>
		</property>
		<property name="driverUrl">
			<value>${jdbc.url}</value>
		</property>
		<property name="user">
			<value>${jdbc.username}</value>
		</property>
		<property name="password">
			<value>${jdbc.password}</value>
		</property>
		<property name="alias">
			<value>spring</value>
		</property>
		<!-- 最少保持的空闲连接数 （默认2个） -->
		<property name="prototypeCount">
			<value>${jdbc.prototypeCount}</value>
		</property>
		<!-- 最大活动时间(超过此时间线程将被kill,默认为1分钟) -->
		<property name="maximumActiveTime">
			<value>${jdbc.maximumActiveTime}</value>
		</property>
		<property name="maximumConnectionCount">
			<value>${jdbc.maximumConnectionCount}</value>
		</property>
		<property name="minimumConnectionCount">
			<value>${jdbc.minimumConnectionCount}</value>
		</property>
		<!-- 如果为true,那么每个被执行的SQL语句将会在执行期被log记录(DEBUG LEVEL). -->
		<property name="trace">
			<value>true</value>
		</property>
		<!-- 详细信息设置 -->
		<property name="verbose">
			<value>true</value>
		</property>
		<!-- 测试的SQL执行语句 -->
		<property name="houseKeepingTestSql">
			<value>${jdbc.houseKeepingTestSql}</value>
		</property>
		<property name="simultaneousBuildThrottle">
			<value>${jdbc.simultaneousBuildThrottle}</value>
		</property>
	</bean>

	<!-- JNDI DataSource for J2EE environments -->
	<!--<jee:jndi-lookup id="dataSource" jndi-name="java:comp/env/jdbc/appfuse"/> 
		<bean id="dataSource" class="org.springframework.jndi.JndiObjectFactoryBean"> 
		<property name="jndiName"> <value>java:comp/env/henan</value> </property> 
		</bean> -->


	<!--pzx dbcp连接池配置 <bean id="dataSource" class="org.apache.commons.dbcp.BasicDataSource" 
		destroy-method="close"> <property name="driverClassName" value="${jdbc.driverClassName}" 
		/> <property name="url" value="${jdbc.url}" /> <property name="username" 
		value="${jdbc.username}" /> <property name="password" value="${jdbc.password}" 
		/> <property name="maxActive" value="100" /> <property name="maxWait" value="1000" 
		/> <property name="poolPreparedStatements" value="true" /> <property name="defaultAutoCommit" 
		value="true" /> </bean> -->


	<!-- 本文件写的是各个系统的数据库配置部分，包括数据库连接，事务的定义 -->
	<!-- 注意事务的写法 -->
	<bean id="txManager"
		class="org.springframework.orm.hibernate3.HibernateTransactionManager">
		<property name="sessionFactory" ref="sessionFactory" />
	</bean>

	<!-- Activiti处理引擎的配置，交由Spring管理 -->
	<!-- <bean id="processEngineConfiguration" class="org.activiti.spring.SpringProcessEngineConfiguration"> -->
	<!-- 配置数据源,和系统使用同一个数据源 -->
	<!-- <property name="dataSource" ref="dataSource" /> -->
	<!-- <property name="databaseSchemaUpdate" value="true" /> -->
	<!-- <property name="jobExecutorActivate" value="false" /> -->
	<!-- 统一的事务管理 -->
	<!-- <property name="transactionManager" ref="txManager" /> -->
	<!-- <property name="activityFontName" value="宋体" /> -->
	<!-- <property name="labelFontName" value="宋体" /> -->
	<!-- </bean> -->
	<!-- <bean id="processEngine" class="org.activiti.spring.ProcessEngineFactoryBean"> -->
	<!-- <property name="processEngineConfiguration" ref="processEngineConfiguration" 
		/> -->
	<!-- </bean> -->
	<!-- 由流程引擎对象，提供的方法，创建项目中使用的Activiti工作流的Service -->
	<!-- <bean id="repositoryService" factory-bean="processEngine" -->
	<!-- factory-method="getRepositoryService" /> -->
	<!-- <bean id="runtimeService" factory-bean="processEngine" -->
	<!-- factory-method="getRuntimeService" /> -->
	<!-- <bean id="taskService" factory-bean="processEngine" -->
	<!-- factory-method="getTaskService" /> -->
	<!-- <bean id="historyService" factory-bean="processEngine" -->
	<!-- factory-method="getHistoryService" /> -->
	<!-- <bean id="formService" factory-bean="processEngine" -->
	<!-- factory-method="getFormService" /> -->

	<!--事务的切点，哪些方法需要配置事务，事务的类型是什么 -->
	<tx:advice id="txAdvice" transaction-manager="txManager">
		<!-- the transactional semantics... -->
		<tx:attributes>
			<!-- all methods starting with 'get' are read-only -->
			<tx:method name="get*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="find*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="save*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="update*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="ed*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="remove*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="del*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<!-- other methods use the default transaction settings (see below) -->
			<tx:method name="d*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="m*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="h*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="j*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="l*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="m*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="n*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="o*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="p*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="r*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="s*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="t*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="x*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="y*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="z*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<!-- other methods use the default transaction settings (see below) -->
			<tx:method name="w*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="u*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="v*" read-only="false"
				rollback-for="ie.bsp.frame.exception.GeneralException" />
			<tx:method name="*" rollback-for="ie.bsp.frame.exception.GeneralException" />
		</tx:attributes>
	</tx:advice>
	<!-- ensure that the above transactional advice runs for any execution of 
		an operation defined by the FooService interface -->
	<aop:config proxy-target-class="true">
		<!--事务的切面，哪些类中的哪些方法需要事务管理，可用通配符 -->
		<aop:advisor pointcut="execution(* ie.bsp.core.dao.*Dao.*(..))"
			advice-ref="txAdvice" />
		<aop:advisor pointcut="execution(* ie.bsp.frame.dao.*Dao.*(..))"
			advice-ref="txAdvice" />
		<aop:advisor pointcut="execution(* ie.*.dao.*Dao.*(..))"
			advice-ref="txAdvice" />
		<aop:advisor pointcut="execution(* ie.*.*.dao.*Dao.*(..))"
			advice-ref="txAdvice" />
		<aop:advisor pointcut="execution(* ie.*.*.*.dao.*Dao.*(..))"
			advice-ref="txAdvice" />
		<aop:advisor pointcut="execution(* ie.*.service.*Service.*(..))"
			advice-ref="txAdvice" />
		<aop:advisor pointcut="execution(* ie.*.*.service.*Service.*(..))"
			advice-ref="txAdvice" />
		<aop:advisor pointcut="execution(* ie.*.*.*.service.*Service.*(..))"
			advice-ref="txAdvice" />
		<aop:advisor pointcut="execution(* ie.wsdl.*.*Service.*(..))"
			advice-ref="txAdvice" />
		<aop:advisor pointcut="execution(* ie.wsdl.*.*.*Service.*(..))"
			advice-ref="txAdvice" />
		<aop:advisor pointcut="execution(* *..*Service.*(..))"
			advice-ref="txAdvice" />
	</aop:config>
</beans>
