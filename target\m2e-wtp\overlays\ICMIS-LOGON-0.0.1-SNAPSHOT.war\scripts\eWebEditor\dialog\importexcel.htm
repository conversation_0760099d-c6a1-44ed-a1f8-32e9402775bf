<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj=bV["action"];var hf="";if(aj=="paste"){hf="("+lang["DlgWordPaste"]+")";}var Q=lang["DlgExcel"]+hf;document.write("<title>"+Q+"</title>");function ok(){if(!dz(true)){return;}var ea="";var bG=0;if(aj!="paste"){if($("d_sheet").length<=0){alert(lang["DlgExcelInvalidFile"]);return;}ea=$("d_file").value;bG=parseInt($("d_sheet").options[$("d_sheet").selectedIndex].value);}var dT="";if($("d_imgjpg").checked){dT="jpg";}else if($("d_imggif").checked){dT="gif";}else if($("d_imgpng").checked){dT="png";}var ba="";ba+="mode:"+($("d_modehtml").checked?"html":"img")+";";ba+="imgtype:"+dT+";";ba+="optimizemode:"+($("d_opt2").checked?"2":"1")+";";ba+="opt1vml:"+($("d_opt1vml").checked?"1":"0")+";";ba+="opt1space:"+($("d_opt1space").checked?"1":"0")+";";ba+="opt1table:"+($("d_opt1table").checked?"1":"0")+";";ba+="opt1overflow:"+($("d_opt1overflow").checked?"1":"0")+";";ba+="opt2image:"+($("d_opt2image").checked?"1":"0")+";";ba+="opt2space:"+($("d_opt2space").checked?"1":"0")+";";ba+="opt2table:"+($("d_opt2table").checked?"1":"0")+";";$("divProcessing").style.display="";if(aj!="paste"){U.ImportExcelSheet(ea,bG,ba);}else{U.PasteExcel(ba);}window.setTimeout("gU()",100);};function gU(){if(U.Status!="ok"){window.setTimeout("gU()",100);return;}if(ef()){$("divProcessing").style.display="none";return;}var aw=U.Style;if($("d_opt2").checked){aw="";}var J=U.Body;var eu=U.OriginalFiles;var fO=U.SavedFiles;if(eu){var dR=eu.split("|");var dZ=fO.split("|");for(var i=0;i<dR.length;i++){if(dZ[i]){var bO=dR[i];var ds=dZ[i];EWIN.addUploadFile(bO,ds);}}}if($("d_pos").checked){EWIN.setHTML(aw+J,true);}else{var ic="<SPAN id=eWebEditorTempInsertTag></SPAN>";EWIN.insertHTML(ic);var ia=EWIN.getHTML();var cp=ia.indexOf(ic);var dF=aw+ia.substring(0,cp)+J+ia.substr(cp+ic.length);EWIN.setHTML(dF,true);}U=null;$("divProcessing").style.display="none";parent.aT();};var lO="";function yc(){if(!dz(true)){return;}var ea=dX($("d_file").value);if((lO!=ea)){$("d_sheet").options.length=0;lO="";}if(ea==""){return;}if(ea.indexOf(":")<0){return;}if((lO!="")&&(lO==ea)){return;}if(!hw(ea,"xls|xlsx")){return;}var uE=U.GetExcelWorkSheetName(ea);if(ef()){return;}$("d_sheet").options[0]=new Option(lang["DlgExcelSheetAll"],"0");var ti=uE.split("\n");for(var i=0;i<ti.length;i++){$("d_sheet").options[$("d_sheet").options.length]=new Option(ti[i],i+1);}lO=ea;U=null;};function ir(index){var ie,K;for(var i=1;i<=2;i++){ie=$("group_opt"+i);K=ie.getElementsByTagName("INPUT");for(var j=0;j<K.length;j++){if(index==i){K[j].disabled=false;}else{K[j].disabled=true;}}K=ie.getElementsByTagName("SPAN");for(var j=0;j<K.length;j++){if(index==i){K[j].disabled=false;}else{K[j].disabled=true;}}}};function my(flag){if(flag==1){$("tab_modehtml").style.display="";$("tab_modeimg").style.display="none";}else{if($("tab_modehtml").style.display=="none"){return;}$("tab_modeimg").style.height=$("tab_modehtml").offsetHeight;$("tab_modeimg").style.width=$("tab_modehtml").offsetWidth;$("tab_modehtml").style.display="none";$("tab_modeimg").style.display="";}};function ah(){lang.TranslatePage(document);$("d_opt1").checked=true;ir(1);if(aj=="paste"){document.getElementById("d_pos").checked=false;}parent.bp(Q);lx();};function lx(){var t=$("divProcessing");t.style.left=($("tabDialogSize").offsetWidth+6-parseInt(t.style.width))/2+"px";t.style.top=($("tabDialogSize").offsetHeight-parseInt(t.style.height))/2+"px";}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <script type="text/javascript">

if(aj!="paste"){document.write("<tr>");document.write("	<td>");document.write("	<fieldset>");document.write("	<legend><span lang=DlgExcelLegend></span>:</legend>");document.write("	<table border=0 cellpadding=5 cellspacing=0 width='100%'>");document.write("	<tr><td>");document.write("		<table border=0 cellpadding=0 cellspacing=2 width='100%'>");document.write("		<tr>");document.write("			<td noWrap><span lang=DlgExcelFile></span>:</td>");document.write("			<td noWrap width='100%'><input type=file id='d_file' size=30 style='width:100%' onchange='yc()' onkeyup='yc()'></td>");document.write("		</tr>");document.write("		<tr>");document.write("			<td noWrap><span lang=DlgExcelSheet></span>:</td>");document.write("			<td noWrap><select id='d_sheet' size=1 style='width:100%'></select></td>");document.write("		</tr>");document.write("		</table>");document.write("	</td></tr>");document.write("	</table>");document.write("	</fieldset>");document.write("	</td>");document.write("</tr>");document.write("<tr><td height=5></td></tr>");}</script> <tr> <td> <fieldset> <legend><span lang=DlgExcelOptimize></span>: <input type=radio id=d_modehtml name=g_mode checked onclick="my(1)"><label for=d_modehtml><span lang=DlgWordModeHTML></span></label>&nbsp;<input type=radio id=d_modeimg name=g_mode onclick="my(2)"><label for=d_modeimg><span lang=DlgWordModeIMG></span></label></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=3 id=tab_modehtml> <tr><td colspan=5 noWrap><input type=radio name="d_optimize" id="d_opt1" checked onclick="ir(1)"><label for=d_opt1><span lang=DlgExcelOpt1></span></label></td></tr> <tr id=group_opt1> <td>&nbsp;&nbsp;&nbsp; </td> <td noWrap><input type=checkbox id=d_opt1vml><label for=d_opt1vml><span lang=DlgExcelOpt1VML></span></label></td> <td noWrap><input type=checkbox id=d_opt1space><label for=d_opt1space><span lang=DlgExcelOpt1Space></span></label></td> <td noWrap><input type=checkbox id=d_opt1table><label for=d_opt1table><span lang=DlgExcelOpt1Table></span></label></td> <td noWrap><input type=checkbox id=d_opt1overflow><label for=d_opt1overflow><span lang=DlgExcelOpt1Overflow></span></label></td> </tr> <tr><td colspan=5 noWrap><input type=radio name="d_optimize" id="d_opt2" onclick="ir(2)"><label for=d_opt2><span lang=DlgExcelOpt2></span></label></td></tr> <tr id=group_opt2> <td>&nbsp; </td> <td noWrap><input type=checkbox id=d_opt2image checked><label for=d_opt2image><span lang=DlgExcelOpt2Image></span></label></td> <td noWrap><input type=checkbox id=d_opt2space><label for=d_opt2space><span lang=DlgExcelOpt2Space></span></label></td> <td noWrap><input type=checkbox id=d_opt2table checked><label for=d_opt2table><span lang=DlgExcelOpt2Table></span></label></td> <td></td> </tr> </table> <table border=0 cellpadding=0 cellspacing=3 id=tab_modeimg style="display:none"> <tr> <td noWrap><span lang=DlgWordImgType></span>: <input type=radio id=d_imggif name=d_imgtype checked><label for=d_imggif>GIF</label> <input type=radio id=d_imgjpg name=d_imgtype><label for=d_imgjpg>JPG</label> <input type=radio id=d_imgpng name=d_imgtype><label for=d_imgpng>PNG</label></td> </tr> <tr><td><span lang=DlgWordImgAlt></span></td></tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <table border=0 cellpadding=0 cellspacing=0 width="100%"> <tr> <td noWrap><input type=checkbox id=d_pos checked><label for=d_pos><span lang=DlgComInsertReplace></span></label></td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel> </tr> </table> </td> </tr> </table> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:60px;top:85px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5 align=center><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgExcelImporting></span></font></marquee></td></tr></table> </div> </body> </html> 