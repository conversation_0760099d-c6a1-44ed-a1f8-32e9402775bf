/* CSS Document */
body,h1,h2,h3,h4,h5,h6,p,ul,ol,li,form,img,dl,dt,dd,table,th,td,blockquote,fieldset,div,strong,label,em{margin:0;padding:0;border:0;}
ul,ol,li{list-style:none;}
input,button{margin:0;font-size:12px;vertical-align:middle;}
body{font-size:12px;font-family:Arial, Helvetica, sans-serif;  text-align:center; margin:0 auto;}
table{border-collapse:collapse;border-spacing:0;}
a{color:#333;text-decoration:none;}
a:hover{color:#ef9b11; text-decoration:underline;}
em{ font-style:normal;}

#box-163css {MARGIN: 0px auto; WIDTH: 900px; POSITION: relative;}
.titleh2{ width:900px; margin:0 auto;text-align:center; padding-top:12px;}
#box-163css H3 {FONT-SIZE:14px; padding:5px 0; width:100%;}
#box-163css H3 A:hover {COLOR: #ba2636}
#Handler{BACKGROUND: url(../image/cz/bg01.gif) repeat-x 0px 48px; POSITION: relative; HEIGHT: 60px}
#NtesModuleTimelineContainer IMG {BORDER-RIGHT: #ccc 1px solid; BORDER-TOP: #ccc 1px solid; FLOAT: left; BORDER-LEFT: #ccc 1px solid; MARGIN-RIGHT: 5px; BORDER-BOTTOM: #ccc 1px solid}
#Handler LI {DISPLAY: inline; LIST-STYLE-TYPE: none}
#Handler SPAN {BACKGROUND: url(../image/cz/bg02.gif) no-repeat -58px 0px; VISIBILITY: hidden; WIDTH: 68px; COLOR: #fff; LINE-HEIGHT:16px; POSITION: absolute; HEIGHT: 22px; TEXT-ALIGN: center}
#Handler B {DISPLAY: inline-block; WIDTH: 18px; BOTTOM: 0px; POSITION: absolute; HEIGHT: 18px}
#NtesModuleTimelineContainer {OVERFLOW: hidden; POSITION: relative; HEIGHT:390px;}
#NtesModuleTimelineContainer BLOCKQUOTE {TEXT-INDENT: 24px; LINE-HEIGHT: 21px;}
#NtesModuleTimelineContainer BLOCKQUOTE A {COLOR: #ba2636}
#NtesModuleTimelineContainer DIV{BORDER-RIGHT: #c5c5c5 1px solid; PADDING-RIGHT: 10px; BORDER-TOP: #c5c5c5 1px solid; PADDING-LEFT: 10px; BACKGROUND: #f8f8f8; FILTER: progid:DXImageTransform.Microsoft.Shadow(color=gray, Direction=135, Strength=5); VISIBILITY: hidden; PADDING-BOTTOM: 10px; OVERFLOW: auto; BORDER-LEFT: #c5c5c5 1px solid; WIDTH: 870px; COLOR: #727171; PADDING-TOP: 10px; BORDER-BOTTOM: #c5c5c5 1px solid; POSITION: absolute; TOP: 10px; HEIGHT:350px; border-radius: 5px; webkit-box-shadow: 5px 2px 6px #ccc; moz-box-shadow: 5px 2px 6px #ccc; text-align:left;}
#NtesModuleTimelineContainer DIV img{ margin:0 auto; width:400px; float:none;}
.NTEStimelineOff {BACKGROUND: url(../image/cz/bg02.gif) no-repeat 0px 0px}
.NTEStimelineOn {BACKGROUND: url(../image/cz/bg02.gif) no-repeat 0px 0px}
.NTEStimelineOn {BACKGROUND-POSITION: -18px 0px}
#NTEStimelinePointer {
	DISPLAY: inline-block; BACKGROUND: url(../image/cz/bg02.gif) no-repeat -40px 0px; VISIBILITY: hidden; WIDTH: 12px; POSITION: absolute; TOP: 63px; HEIGHT: 8px
}
