<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj=bV["action"];var hf="";if(aj=="paste"){hf="("+lang["DlgQFPaste"]+")";}var Q=lang["DlgQF"]+hf;document.write("<title>"+Q+"</title>");var tG="";function ok(){if($("d_confusion").checked){if($("d_confusioncolor").checked){$("v_confusioncolor").value=dX($("v_confusioncolor").value);if(ik($("v_confusioncolor").value)==""){return cv($("v_confusioncolor"),lang["ErrColorInvalid"]);}}if($("d_confusionclass").checked){$("v_confusionclass").value=dX($("v_confusionclass").value);if($("v_confusionclass").value==""){return cv($("v_confusionclass"),lang["DlgQFConfusionClass"]+":"+lang["ErrInputNull"]);}}}if($("d_addlineheight").checked){$("v_addlineheight").value=dX($("v_addlineheight").value);if($("v_addlineheight").value==""){return cv($("v_addlineheight"),lang["DlgQFAddLineHeight"]+":"+lang["ErrInputNull"]);}}if($("d_confusion").checked&&$("d_confusionsuper").checked&&O.ae){if(!dz(true)){return;}U.QuickFormat();if(ef()){return;}var rM=U.Style;var rr=U.Body;U=null;if(rM){EWIN.setHTML(rM+EWIN.getHTML(),true);tG=ik(vI(rr));}}if($("d_confusion").checked){wD();}if($("d_delhidden").checked||$("d_confusion").checked){hb("hidden");hb("STYLE");hb("SCRIPT");}if(!$("d_keepall").checked){if(!$("d_keeptable").checked){hb("TABLE");}if(!$("d_keepimg").checked){hb("IMG");}if(!$("d_keepobject").checked){hb("OBJECT");hb("EMBED");hb("APPLET");}}var html=EWIN.getHTML(true);var P=/<!--([^a]|a)*?-->/gi;html=html.replace(P,"");P=/<br\s*\/?>\s*/gi;html=html.replace(P,"<br>");if(!$("d_keepall").checked){var oi=",P,BR,TABLE,TBODY,TH,TR,TD,IMG,APPLET,OBJECT,EMBED,PARAM,";if($("d_keepul").checked){oi+="UL,OL,LI,";}if($("d_keepa").checked){oi+="A,";}P=/<\/?([a-zA-Z]+)(\s[^>]*)?>/gi;html=html.replace(P,function($0,$1){if(oi.indexOf(","+$1.toUpperCase()+",")> -1){return $0;}else{return "";}});}if($("d_addbr2p").checked){P=/<br>/gi;html=html.replace(P,"</p><p>");}if($("d_delspace").checked){P=/(\s|&nbsp\;)+<\/p>/gi;html=html.replace(P,"</p>");P=/(\s|&nbsp\;)+<br>/gi;html=html.replace(P,"<br>");}if($("d_delline").checked){P=/<p(\s[^>]*)?>(&nbsp\;|\s)*<\/p>/gi;html=html.replace(P,"");P=/(<br>)+/gi;html=html.replace(P,"<br>");}if($("d_delallattr").checked){P=/(<[a-zA-Z]+)(\s[^>]*)>/gi;html=html.replace(P,function($0,$1,$2){var r="";var m=$2.match(/(src|href|type|name|value|rowsspan|colspan|classid|codebase)=(\'[^\']+\'|\"[^\"]+\"|[^\s]+)/gi);if(m){for(var i=0;i<m.length;i++){r+=" "+m;}}return $1+r+">";});}else{if($("d_delstyle").checked){P=/(<[^>]+?)(\sstyle\s*=\s*(\'[^\']*?\'|\"[^\"]*?\"))([^>]*>)/gi;html=html.replace(P,"$1$4");P=/(<[^>]+?)(\sclass\s*=\s*(\'[^\']*?\'|\"[^\"]*?\"|\w+))([^>]*>)/gi;html=html.replace(P,"$1$4");}if($("d_delon").checked){P=/(<[^>]+?)(\son[a-zA-Z]+\s*=\s*(\w+\([^\)]*?\)|\'[^\']*?\'|\"[^\"]*?\"))([^>]*)>/gi;html=html.replace(P,"$1$4");}}if($("d_addindent").checked){P=/(<p(\s[^>]*)?>)(&nbsp\;|\s)*/gi;html=html.replace(P,"$1");}if($("d_addtablebc").checked){P=/<table(\s[^>]*)?>/gi;html=html.replace(P,"<table style=\"BORDER-COLLAPSE:collapse\" border=1 bordercolor=\"#000000\" cellpadding=2 cellspacing=0>");}EWIN.setHTML(html,true);var K=A.F.getElementsByTagName("P");for(var i=0;i<K.length;i++){var t=K[i];if($("d_addmargin").checked){t.style.marginTop="0";t.style.marginBottom="0";}if($("d_addindent").checked){t.style.textIndent="2em";}if($("d_addjustify").checked){t.style.textAlign="justify";t.style.textJustify="inter-ideograph";t.align="";}if($("d_addlineheight").checked){t.style.lineHeight=$("v_addlineheight").value;}}parent.aT();};function wD(){op("SPAN");op("FONT");op("DIV");};function op(ai){while(wB(ai)){}};function wB(ai){var K=A.F.getElementsByTagName(ai);for(var i=0;i<K.length;i++){var t=K[i];if($("d_confusionauto").checked||$("d_confusionsuper").checked){var lT="";if($("d_confusionsuper").checked){lT=ik(nN(t,"BODY"));}else{lT=ik(nN(t,""));}if(!lT){lT=tG;}var mX=C.cG(t,"fontSize");if(mX){mX=mX.substr(0,1);}if((ik(C.cG(t,"color"))==lT)||(mX=="0")){cW(t);return true;}}else if($("d_confusioncolor").checked){if(ik(C.cG(t,"color"))==ik($("v_confusioncolor").value)){cW(t);return true;}}else if($("d_confusionclass").checked){if(t.className==$("v_confusionclass").value){cW(t);return true;}}}return false;};function ik(aG){var uj={"ALICEBLUE":"#F0F8FF","ANTIQUEWHITE":"#FAEBD7","AQUA":"#00FFFF","AQUAMARINE":"#7FFFD4","AZURE":"#F0FFFF","BEIGE":"#F5F5DC","BISQUE":"#FFE4C4","BLACK":"#000000","BLANCHEDALMOND":"#FFEBCD","BLUE":"#0000FF","BLUEVIOLET":"#8A2BE2","BROWN":"#A52A2A","BURLYWOOD":"#DEB887","CADETBLUE":"#5F9EA0","CHARTREUSE":"#7FFF00","CHOCOLATE":"#D2691E","CORAL":"#FF7F50","CORNFLOWERBLUE":"#6495ED","CORNSILK":"#FFF8DC","CRIMSON":"#DC143C","CYAN":"#00FFFF","DARKBLUE":"#00008B","DARKCYAN":"#008B8B","DARKGOLDENROD":"#B8860B","DARKGRAY":"#A9A9A9","DARKGREEN":"#006400","DARKKHAKI":"#BDB76B","DARKMAGENTA":"#8B008B","DARKOLIVEGREEN":"#556B2F","DARKORANGE":"#FF8C00","DARKORCHID":"#9932CC","DARKRED":"#8B0000","DARKSALMON":"#E9967A","DARKSEAGREEN":"#8FBC8F","DARKSLATEBLUE":"#483D8B","DARKSLATEGRAY":"#2F4F4F","DARKTURQUOISE":"#00CED1","DARKVIOLET":"#9400D3","DEEPPINK":"#FF1493","DEEPSKYBLUE":"#00BFFF","DIMGRAY":"#696969","DODGERBLUE":"#1E90FF","FIREBRICK":"#B22222","FLORALWHITE":"#FFFAF0","FORESTGREEN":"#228B22","FUCHSIA":"#FF00FF","GAINSBORO":"#DCDCDC","GHOSTWHITE":"#F8F8FF","GOLD":"#FFD700","GOLDENROD":"#DAA520","GRAY":"#808080","GREEN":"#008000","GREENYELLOW":"#ADFF2F","HONEYDEW":"#F0FFF0","HOTPINK":"#FF69B4","INDIANRED":"#CD5C5C","INDIGO":"#4B0082","IVORY":"#FFFFF0","KHAKI":"#F0E68C","LAVENDER":"#E6E6FA","LAVENDERBLUSH":"#FFF0F5","LAWNGREEN":"#7CFC00","LEMONCHIFFON":"#FFFACD","LIGHTBLUE":"#ADD8E6","LIGHTCORAL":"#F08080","LIGHTCYAN":"#E0FFFF","LIGHTGOLDENRODYELLOW":"#FAFAD2","LIGHTGREEN":"#90EE90","LIGHTGREY":"#D3D3D3","LIGHTPINK":"#FFB6C1","LIGHTSALMON":"#FFA07A","LIGHTSEAGREEN":"#20B2AA","LIGHTSKYBLUE":"#87CEFA","LIGHTSLATEGRAY":"#778899","LIGHTSTEELBLUE":"#B0C4DE","LIGHTYELLOW":"#FFFFE0","LIME":"#00FF00","LIMEGREEN":"#32CD32","LINEN":"#FAF0E6","MAGENTA":"#FF00FF","MAROON":"#800000","MEDIUMAQUAMARINE":"#66CDAA","MEDIUMBLUE":"#0000CD","MEDIUMORCHID":"#BA55D3","MEDIUMPURPLE":"#9370DB","MEDIUMSEAGREEN":"#3CB371","MEDIUMSLATEBLUE":"#7B68EE","MEDIUMSPRINGGREEN":"#00FA9A","MEDIUMTURQUOISE":"#48D1CC","MEDIUMVIOLETRED":"#C71585","MIDNIGHTBLUE":"#191970","MINTCREAM":"#F5FFFA","MISTYROSE":"#FFE4E1","MOCCASIN":"#FFE4B5","NAVAJOWHITE":"#FFDEAD","NAVY":"#000080","OLDLACE":"#FDF5E6","OLIVE":"#808000","OLIVEDRAB":"#6B8E23","ORANGE":"#FFA500","ORANGERED":"#FF4500","ORCHID":"#DA70D6","PALEGOLDENROD":"#EEE8AA","PALEGREEN":"#98FB98","PALETURQUOISE":"#AFEEEE","PALEVIOLETRED":"#DB7093","PAPAYAWHIP":"#FFEFD5","PEACHPUFF":"#FFDAB9","PERU":"#CD853F","PINK":"#FFC0CB","PLUM":"#DDA0DD","POWDERBLUE":"#B0E0E6","PURPLE":"#800080","RED":"#FF0000","ROSYBROWN":"#BC8F8F","ROYALBLUE":"#4169E1","SADDLEBROWN":"#8B4513","SALMON":"#FA8072","SANDYBROWN":"#F4A460","SEAGREEN":"#2E8B57","SEASHELL":"#FFF5EE","SIENNA":"#A0522D","SILVER":"#C0C0C0","SKYBLUE":"#87CEEB","SLATEBLUE":"#6A5ACD","SLATEGRAY":"#708090","SNOW":"#FFFAFA","SPRINGGREEN":"#00FF7F","STEELBLUE":"#4682B4","TAN":"#D2B48C","TEAL":"#008080","THISTLE":"#D8BFD8","TOMATO":"#FF6347","TURQUOISE":"#40E0D0","VIOLET":"#EE82EE","WHEAT":"#F5DEB3","WHITE":"#FFFFFF","WHITESMOKE":"#F5F5F5","YELLOW":"#FFFF00","YELLOWGREEN":"#9ACD32"};if(!aG){return "";}aG=aG.toUpperCase();var P;P=/#[0-9A-H]{6}/gi;if(P.test(aG)){return aG;}P=/#[0-9A-H]{3}/gi;if(P.test(aG)){var aL=aG.substr(1,1);var bq=aG.substr(2,1);var si=aG.substr(3,1);return "#"+aL+aL+bq+bq+si+si;}P=/[A-Z]+/gi;if(P.test(aG)){if(uj[aG]){return uj[aG];}}return "";};function vI(rr){var doc=$("ifr_temp").contentWindow.document;doc.open();doc.write(rr);doc.close();var t=doc.getElementById("eWebEditor_Temp_Span");return nN(t,"");};function nN(t,uN){var b=true;while(b){t=t.parentElement;if(!t){return "";}if(t.tagName==uN){return "";}var pr=C.cG(t,"backgroundColor");if((pr)&&(pr!="transparent")){return pr;}}return "";};function hb(flag){var b=false;while(!b){b=rS(A.F.body,flag);}};function rS(H,flag){var tX=H.children;for(var i=0;i<tX.length;i++){var o=tX[i];if(flag=="hidden"&&((C.cG(o,"display")=="none")||(C.cG(o,"visibility")=="hidden"))){cW(o);return false;}else if(o.tagName==flag){if(flag=="TABLE"&&$("d_keepimg").checked){var hX=o.getElementsByTagName("IMG");for(var j=0;j<hX.length;j++){o.parentNode.insertBefore(hX[j],o);}}cW(o);return false;}else{if(!rS(o,flag)){return false;}}}return true;};function cW(H){H.parentNode.removeChild(H);};function wR(H){if(H.checked){$("d_keepul").checked=true;$("d_keeptable").checked=true;$("d_keepimg").checked=true;$("d_keepobject").checked=true;$("d_keepa").checked=true;$("d_keepul").disabled=true;$("d_keeptable").disabled=true;$("d_keepimg").disabled=true;$("d_keepobject").disabled=true;$("d_keepa").disabled=true;}else{$("d_keepul").disabled=false;$("d_keeptable").disabled=false;$("d_keepimg").disabled=false;$("d_keepobject").disabled=false;$("d_keepa").disabled=false;}};function wO(H){if(H.checked){$("d_delstyle").checked=true;$("d_delon").checked=true;$("d_delstyle").disabled=true;$("d_delon").disabled=true;}else{$("d_delstyle").disabled=false;$("d_delon").disabled=false;}};function wQ(H){var b=(!H.checked);$("d_confusionauto").disabled=b;$("d_confusionsuper").disabled=b;$("d_confusioncolor").disabled=b;$("v_confusioncolor").disabled=b;$("d_confusionclass").disabled=b;$("v_confusionclass").disabled=b;};function ah(){if(aj=="paste"&&O.ae){$("d_confusionsuper").checked=true;}if(!O.ae){$("d_confusionsuper").disabled=true;}lang.TranslatePage(document);parent.bp(Q);}</script> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgQFKeep></span>:</legend> <table border=0 cellpadding=3 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2> <tr><td noWrap><input type=checkbox id=d_keepall onclick="wR(this)"><label for=d_keepall><span lang=DlgQFKeepAll></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_keepul checked><label for=d_keepul><span lang=DlgQFKeepUl></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_keeptable checked><label for=d_keeptable><span lang=DlgQFKeepTable></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_keepimg checked><label for=d_keepimg><span lang=DlgQFKeepImg></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_keepobject checked><label for=d_keepobject><span lang=DlgQFKeepObject></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_keepa checked><label for=d_keepa><span lang=DlgQFKeepA></span></label></td></tr> </table> </td></tr> </table> </fieldset> </td> <td width=5>&nbsp;</td> <td> <fieldset> <legend><span lang=DlgQFDel></span>:</legend> <table border=0 cellpadding=3 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2> <tr><td noWrap><input type=checkbox id=d_delline checked><label for=d_delline><span lang=DlgQFDelLine></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_delspace checked><label for=d_delspace><span lang=DlgQFDelSpace></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_delhidden checked><label for=d_delhidden><span lang=DlgQFDelHidden></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_delallattr onclick="wO(this)"><label for=d_delallattr><span lang=DlgQFDelAllAttr></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_delstyle checked><label for=d_delstyle><span lang=DlgQFDelStyle></span></label></td></tr> <tr><td noWrap><input type=checkbox id=d_delon checked><label for=d_delon><span lang=DlgQFDelOn></span></label></td></tr> </table> </td></tr> </table> </fieldset> </td> <td width=5>&nbsp;</td> <td> <fieldset> <legend><span lang=DlgQFAdd></span>:</legend> <table border=0 cellpadding=3 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2> <tr> <td noWrap><input type=checkbox id=d_addmargin checked><label for=d_addmargin><span lang=DlgQFAddMargin></span></label></td> <td rowspan=6>&nbsp;</td> <td noWrap><input type=checkbox id=d_confusion checked onclick="wQ(this)"><label for=d_confusion><span lang=DlgQFConfusion></span></label></td> </tr> <tr> <td noWrap><input type=checkbox id=d_addindent checked><label for=d_addindent><span lang=DlgQFAddIndent></span></label></td> <td noWrap><input type=radio id=d_confusionauto name=d_confusionopt checked><label for=d_confusionauto><span lang=DlgQFConfusionAuto></span></label></td> </tr> <tr> <td noWrap><input type=checkbox id=d_addjustify checked><label for=d_addjustify><span lang=DlgQFAddJustify></span></label></td> <td noWrap><input type=radio id=d_confusionsuper name=d_confusionopt><label for=d_confusionsuper><span lang=DlgQFConfusionSuper></span></label></td> </tr> <tr> <td noWrap><input type=checkbox id=d_addlineheight><label for=d_addlineheight><span lang=DlgQFAddLineHeight></span></label><input type=text id=v_addlineheight size=3 value="1.5"></td> <td noWrap><input type=radio id=d_confusioncolor name=d_confusionopt><label for=d_confusioncolor><span lang=DlgQFConfusionColor></span></label><input type=text id=v_confusioncolor size=6 value="#FFFFFF"></td> </tr> <tr> <td noWrap><input type=checkbox id=d_addbr2p><label for=d_addbr2p><span lang=DlgQFAddBR2P></span></label></td> <td noWrap><input type=radio id=d_confusionclass name=d_confusionopt><label for=d_confusionclass><span lang=DlgQFConfusionClass></span></label><input type=text id=v_confusionclass size=6></td> </tr> <tr> <td noWrap><input type=checkbox id=d_addtablebc><label for=d_addtablebc><span lang=DlgQFAddTableBC></span></label></td> <td noWrap></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td colspan=5 height=5></td></tr> <tr> <td colspan=5> <table border=0 cellpadding=0 cellspacing=0 width="100%"> <tr> <td noWrap></td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel> </tr> </table> </td> </tr> </table> </td></tr></table> <div style="display:none"> <iframe id=ifr_temp frameborder='0' src='dialog/blank.htm'></iframe> </div> </body> </html> 