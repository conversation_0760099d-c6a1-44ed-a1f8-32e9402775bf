<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj=bV["action"];var hf="";if(aj=="paste"){hf="("+lang["DlgWordPaste"]+")";}var Q=lang["DlgWord"]+hf;document.write("<title>"+Q+"</title>");function ok(){if(!dz(true)){return;}var ea="";if(aj!="paste"){ea=$("d_file").value;if(!hw(ea,"doc|docx")){alert(lang["DlgWordInvalidFile"]);return;}}var dT="";if($("d_imgjpg").checked){dT="jpg";}else if($("d_imggif").checked){dT="gif";}else if($("d_imgpng").checked){dT="png";}var ba="";ba+="mode:"+($("d_modehtml").checked?"html":"img")+";";ba+="imgtype:"+dT+";";ba+="optimizemode:"+($("d_opt2").checked?"2":"1")+";";ba+="opt1vml:"+($("d_opt1vml").checked?"1":"0")+";";ba+="opt1absolute:"+($("d_opt1absolute").checked?"1":"0")+";";ba+="opt1eq:"+($("d_opt1eq").checked?"1":"0")+";";ba+="opt1margin:"+($("d_opt1margin").checked?"1":"0")+";";ba+="opt1space:"+($("d_opt1space").checked?"1":"0")+";";ba+="opt2image:"+($("d_opt2image").checked?"1":"0")+";";ba+="opt2table:"+($("d_opt2table").checked?"1":"0")+";";ba+="opt2eq:"+($("d_opt2eq").checked?"1":"0")+";";ba+="opt2indent:"+($("d_opt2indent").checked?"1":"0")+";";ba+="opt2ptobr:"+($("d_opt2ptobr").checked?"1":"0")+";";ba+="pagewidth:"+($("d_pagewidth").checked?"1":"0")+";";ba+="pagemargin:"+($("d_pagemargin").checked?"1":"0")+";";ba+="pageeffect:"+($("d_pageeffect").checked?"1":"0")+";";ba+="pagescroll:"+($("d_pagescroll").checked?"1":"0")+";";$("divProcessing").style.display="";if(aj!="paste"){U.ImportWord(ea,ba);}else{U.PasteWord(ba);}window.setTimeout("gU()",100);};function gU(){if(U.Status!="ok"){window.setTimeout("gU()",100);return;}if(ef()){$("divProcessing").style.display="none";return;}var aw=U.Style;if($("d_opt2").checked){aw="";}var J=U.Body;var eu=U.OriginalFiles;var fO=U.SavedFiles;if(eu){var dR=eu.split("|");var dZ=fO.split("|");for(var i=0;i<dR.length;i++){if(dZ[i]){var bO=dR[i];var ds=dZ[i];EWIN.addUploadFile(bO,ds);}}}if($("d_pos").checked){EWIN.setHTML(aw+J,true);}else{var ic="<SPAN id=eWebEditorTempInsertTag></SPAN>";EWIN.insertHTML(ic);var ia=EWIN.getHTML();var cp=ia.indexOf(ic);var dF=aw+ia.substring(0,cp)+J+ia.substr(cp+ic.length);EWIN.setHTML(dF,true);}U=null;$("divProcessing").style.display="none";parent.aT();};function ir(index){var ie,K;for(var i=1;i<=2;i++){ie=$("group_opt"+i);K=ie.getElementsByTagName("INPUT");for(var j=0;j<K.length;j++){if(index==i){K[j].disabled=false;}else{K[j].disabled=true;}}K=ie.getElementsByTagName("SPAN");for(var j=0;j<K.length;j++){if(index==i){K[j].disabled=false;}else{K[j].disabled=true;}}}};function wS(H){if(H.checked){$("d_opt2indent").checked=false;}};function wU(H){if(H.checked){$("d_opt2ptobr").checked=false;}};function sd(b){if(b){$("d_pagemargin").disabled=false;$("d_pageeffect").disabled=false;$("d_pagescroll").disabled=false;}else{$("d_pagemargin").disabled=true;$("d_pageeffect").disabled=true;$("d_pagescroll").disabled=true;$("d_pagemargin").checked=false;$("d_pageeffect").checked=false;$("d_pagescroll").checked=false;}};function my(flag){if(flag==1){$("tab_modehtml").style.display="";$("tab_modeimg").style.display="none";}else{$("tab_modeimg").style.height=$("tab_modehtml").offsetHeight;$("tab_modeimg").style.width=$("tab_modehtml").offsetWidth;$("tab_modehtml").style.display="none";$("tab_modeimg").style.display="";}};function ah(){lang.TranslatePage(document);$("d_opt1").checked=true;ir(1);if(aj=="paste"){document.getElementById("d_pos").checked=false;$("d_pagewidth").checked=false;sd(false);}parent.bp(Q);lx();};function lx(){var t=$("divProcessing");t.style.left=($("tabDialogSize").offsetWidth+6-parseInt(t.style.width))/2+"px";t.style.top=($("tabDialogSize").offsetHeight-parseInt(t.style.height))/2+"px";}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <script type="text/javascript">if(aj!="paste"){document.write("<tr>");document.write("	<td>");document.write("	<fieldset>");document.write("	<legend><span lang=DlgWordFile></span>:</legend>");document.write("	<table border=0 cellpadding=5 cellspacing=0 width='100%'>");document.write("	<tr><td><input type=file id=d_file size=30 style='width:100%'></td></tr>");document.write("	</table>");document.write("	</fieldset>");document.write("	</td>");document.write("</tr>");document.write("<tr><td height=5></td></tr>");}</script> <tr> <td> <fieldset> <legend><span lang=DlgWordOptimize></span>: <input type=radio id=d_modehtml name=g_mode checked onclick="my(1)"><label for=d_modehtml><span lang=DlgWordModeHTML></span></label>&nbsp;<input type=radio id=d_modeimg name=g_mode onclick="my(2)"><label for=d_modeimg><span lang=DlgWordModeIMG></span></label></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td valign=top> <table border=0 cellpadding=0 cellspacing=3 id=tab_modehtml> <tr><td colspan=6><input type=radio name="d_optimize" id="d_opt1" checked onclick="ir(1)"><label for=d_opt1><span lang=DlgWordOpt1></span></label></td></tr> <tr id=group_opt1> <td>&nbsp;&nbsp;&nbsp; </td> <td noWrap><input type=checkbox id=d_opt1vml checked><label for=d_opt1vml><span lang=DlgWordOpt1VML></span></label></td> <td noWrap><input type=checkbox id=d_opt1absolute checked><label for=d_opt1absolute><span lang=DlgWordOpt1Absolute></span></label></td> <td noWrap><input type=checkbox id=d_opt1eq checked><label for=d_opt1eq><span lang=DlgWordOpt1EQ></span></label></td> <td noWrap><input type=checkbox id=d_opt1margin checked><label for=d_opt1margin><span lang=DlgWordOpt1Margin></span></label></td> <td noWrap><input type=checkbox id=d_opt1space><label for=d_opt1space><span lang=DlgWordOpt1Space></span></label></td> </tr> <tr><td colspan=6><input type=radio name="d_optimize" id="d_opt2" onclick="ir(2)"><label for=d_opt2><span lang=DlgWordOpt2></span></label></td></tr> <tr id=group_opt2> <td>&nbsp; </td> <td noWrap><input type=checkbox id=d_opt2image checked><label for=d_opt2image><span lang=DlgWordOpt2Image></span></label></td> <td noWrap><input type=checkbox id=d_opt2table checked><label for=d_opt2table><span lang=DlgWordOpt2Table></span></label></td> <td noWrap><input type=checkbox id=d_opt2eq checked><label for=d_opt2eq><span lang=DlgWordOpt2EQ></span></label></td> <td noWrap><input type=checkbox id=d_opt2indent onclick="wU(this)"><label for=d_opt2indent><span lang=DlgWordOpt2Indent></span></label></td> <td noWrap><input type=checkbox id=d_opt2ptobr onclick="wS(this)"><label for=d_opt2ptobr><span lang=DlgWordOpt2PtoBR></span></label></td> </tr> <tr><td colspan=6 height=1><hr size=1 color="#999999"></td></tr> <tr> <td colspan=2 align=right><span lang=DlgWordPage></span>:</td> <td noWrap><input type=checkbox id=d_pagewidth onclick="sd(this.checked)"><label for=d_pagewidth><span lang=DlgWordPageWidth></span></label></td> <td noWrap><input type=checkbox id=d_pagemargin><label for=d_pagemargin><span lang=DlgWordPageMargin></span></label></td> <td noWrap><input type=checkbox id=d_pageeffect><label for=d_pageeffect><span lang=DlgWordPageEffect></span></label></td> <td noWrap><input type=checkbox id=d_pagescroll><label for=d_pagescroll><span lang=DlgWordPageScroll></span></label></td> </tr> </table> <table border=0 cellpadding=0 cellspacing=3 id=tab_modeimg style="display:none"> <tr> <td noWrap><span lang=DlgWordImgType></span>: <input type=radio id=d_imggif name=d_imgtype checked><label for=d_imggif>GIF</label> <input type=radio id=d_imgjpg name=d_imgtype><label for=d_imgjpg>JPG</label> <input type=radio id=d_imgpng name=d_imgtype><label for=d_imgpng>PNG</label></td> </tr> <tr><td><span lang=DlgWordImgAlt></span></td></tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <table border=0 cellpadding=0 cellspacing=0 width="100%"> <tr> <td noWrap><input type=checkbox id=d_pos checked><label for=d_pos><span lang=DlgComInsertReplace></span></label></td> <td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel> </tr> </table> </td> </tr> </table> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:50px;top:30px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5 align=center><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgWordImporting></span></font></marquee></td></tr></table> </div> </body> </html> 