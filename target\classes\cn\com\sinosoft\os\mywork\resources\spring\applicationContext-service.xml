<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx-2.5.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop-2.5.xsd">

	<bean id="WorkflowService" class="cn.com.sinosoft.mywork.service.impl.WorkflowServiceImpl"
		scope="prototype">
		<property name="dao">
			<ref bean="commonBaseDaoHib" />
		</property>
		
		<property name="workflowBaseService">
			<ref bean="workflowBaseService" />
		</property>
	</bean>
	<bean id="workflowBaseService"
		class="cn.com.sinosoft.mywork.service.impl.WorkflowBaseServiceImpl"
		scope="prototype">
		<property name="processEngine">
			<ref bean="processEngine" />
		</property>
		<property name="dao">
			<ref bean="commonBaseDaoHib" />
		</property>
	</bean>	
</beans>