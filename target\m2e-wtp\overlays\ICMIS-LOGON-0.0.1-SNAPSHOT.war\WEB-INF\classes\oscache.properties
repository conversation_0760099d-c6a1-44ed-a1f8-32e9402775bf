# CACHE DIRECTORY
#
# This is the directory on disk where caches will be stored.
# it will be created if it doesn't already exist, but OSache
# must be able to write to here.
#
# If you want to disable file caching, just comment out or remove this line.
# Note: for Windows machines, this needs \ to be escaped
# ie Windows:
# cache.path=c:\\myapp\\cache
# or *ix:
# cache.path=/opt/myapp/cache
#
#cache.path=c:\\cachetagscache

# CACHE IN MEMORY
#
# If you want to disable memory caching, just comment out or remove this line.
# Note: disabling memory AND disk caching is possible but fairly stupid ;)
#
cache.memory=true

# DEBUGGING
#
# set this to true if you want to see log4j debugging messages
#
cache.debug=false

# CACHE KEY
#
# This is the key that will be used to store the cache in the application
# and session scope.
#
# Ff you want to set the cache key to anything other than the default
# uncomment this line and change the cache.key
#
# cache.key=__oscache_cache

# USE HOST DOMAIN NAME IN KEY
#
# Servers for multiple host domains may wish to add host name info to
# the generation of the key.  If this is true, then uncomment the
# following line.
#
# cache.useHostDomainInKey=true

# CACHE ALGORITHM
# Default cache algorithm to use. Note that in order to use an algorithm
# the cache size must also be specified. If the cache size is not specified,
# the cache algorithm will be Unlimited cache.
#cache.algorithm=com.opensymphony.module.oscache.base.algorithm.LRUCache

# CACHE SIZE
# Default cache size in number of item. If a size is specified but not
# an algorithm, the cache algorithm used will be LRUCache.
#cache.size=100

# CACHE UNLIMITED DISK
# Use unlimited disk cache or not
#cache.unlimited_disk=false