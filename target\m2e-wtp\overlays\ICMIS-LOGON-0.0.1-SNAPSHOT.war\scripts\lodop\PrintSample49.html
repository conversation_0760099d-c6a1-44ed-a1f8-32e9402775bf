﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>WEB打印控件LODOP的样例四十九:指定某些页整体旋转打印</title>

<script language="javascript" src="LodopFuncs.js"></script>
</head>
<body>

<h2><font color="#009999">演示</font><font color="#009999">指定某些页整体旋转打印：</font></h2>

<p>用函数<font color="#0000ff">SET_PRINT_STYLEA</font>可控制某内容所在页整体旋转一定角度：</p>

<p><font color="#0000ff">SET_PRINT_STYLEA(varItemNameID,strStyleName,varStyleValue)</font></p>
<p>参数含义:</p>
<p><font color="#0000ff">varItemNameID</font>内容对象序号，设置其所在页的整体旋转角度</p>      
<p><font color="#0000ff">strStyleName </font><font color="#000000">关键字是“AngleOfPageInside”</font></p>      
<p><font color="#0000ff">varStyleValue </font>旋转角度值，正数表示逆时针旋转，负数表示顺时针旋转，</p>      
<p>这里角度值可任意设置，但注意很多打印机不一定支持，90度和180度较常见。</p> 
<p><b>当打印机不支持这种旋转打印时，请仍旧采用“横向打印”（见<a href="PrintSample5.html" target="_blank">样例5</a>）解决。</b></p> 
例如： 
<p><font color="#0000ff">1:LODOP.SET_PRINT_STYLEA(2,&quot;AngleOfPageInside&quot;,90);</font></p>
<font face="楷体">
该语句让第2个打印对象所在页整体旋转90度。</font>
<p><font color="#0000ff">2:LODOP.SET_PRINT_STYLEA(0,&quot;AngleOfPageInside&quot;,-90);</font></p>
<font face="楷体">
该语句让最新打印对象所在页整体旋转-90度（顺时针旋转90度）。</font>

<p>下面演示循环打印几遍本文档，除第一页不旋转，后面的页分别旋转-90、90和180度：     
</p>

<p><font size="2">点<input type="button" value="打印预览" onClick="MyPreview()">看看。</font>     
</p>
<p><a href="PrintSampIndex.html">&lt;&lt;回样例目录</a></p>
<script language="javascript" type="text/javascript"> 
	function MyPreview() {	
		var LODOP=getLodop();  
		LODOP.PRINT_INIT("打印控件Lodop功能演示_部分页整体旋转输出");
		var strHTML=document.getElementsByTagName("html")[0].innerHTML;
		LODOP.ADD_PRINT_HTM(0,0,"100%","100%",strHTML);
		LODOP.NewPageA();
		LODOP.ADD_PRINT_HTM(0,0,"100%","100%",strHTML);
		LODOP.SET_PRINT_STYLEA(2,"AngleOfPageInside",-90);
		LODOP.NewPageA();
		LODOP.ADD_PRINT_HTM(0,0,"100%","100%",strHTML);
		LODOP.SET_PRINT_STYLEA(0,"AngleOfPageInside",90);
		LODOP.NewPageA();
		LODOP.ADD_PRINT_HTM(0,0,"100%","100%",strHTML);
		LODOP.SET_PRINT_STYLEA(0,"AngleOfPageInside",180);

		//--右下角加个页号对象：
		LODOP.ADD_PRINT_TEXT(553,593,165,22,"右下脚的页号：第#页/共&页");
		LODOP.SET_PRINT_STYLEA(0,"ItemType",2);
		LODOP.SET_PRINT_STYLEA(0,"Horient",1);
		LODOP.SET_PRINT_STYLEA(0,"Vorient",1);

		LODOP.PREVIEW();
	};
</script>
</body>
</html>