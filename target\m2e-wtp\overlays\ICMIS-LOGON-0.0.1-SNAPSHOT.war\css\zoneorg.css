/* CSS Document */
.orgcode_ul {
	 
	list-style: none;
	line-height: 30px;
}

.orgcode_ul li {
	width: 350px;
	float: left;
}

.divzoneorg_a {
	color: black;
	blr: expression(this .       onFocus =       this .       blur() )
}

.divzoneorg_a_choose {
	color: black;
	background-color: #2f8bbc;
	border: 1px #2f8bbc solid;
}

.div_other {
	border: 1px #78C1E1 solid;
	margin-top: 10px;
	margin-left: 10px;
	margin-right: 10px;
	margin-bottom: 10px;
	padding: 4;
	line-height: 22px;
	word-wrap:break-word;
}

.div_first {
	background-color: white;
	border: 0;
	margin:0;
	padding:4 0 4 0;
	color: #000000;
}
.but3_all{
	width:50px;
	height:20px;
	border:0;
	background:url(../image/all.gif);
}
.but3_search{
	background:url(../image/search.gif);
	width:50px;
	height:20px;
	border:0;
	
}

 
 