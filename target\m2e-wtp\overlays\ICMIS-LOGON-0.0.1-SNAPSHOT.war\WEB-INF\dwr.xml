<!DOCTYPE dwr PUBLIC
 "-//GetAhead Limited//DTD Direct Web Remoting 2.0//EN"
 "http://getahead.ltd.uk/dwr/dwr20.dtd">
<dwr>
	<allow>
		<convert converter="bean" match="java.lang.StackTraceElement" />
 		<convert converter="exception" match="java.lang.Exception" />
		<create creator="spring" javascript="CommonBaseDao">
			<param name="class" value="ie.bsp.frame.dao.CommonBaseDao" />
			<param name="beanName" value="commonBaseDaoHib" />
		</create>
		<!-- 删除附件 -->
		<create creator="spring" javascript="attachmentMgr">
			<param name="beanName" value="baseService" />
			<include method="delAttachment" />
		</create>
	</allow>
</dwr>
