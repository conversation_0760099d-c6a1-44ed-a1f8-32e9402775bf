<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">

<HTML><HEAD><TITLE>weboffice演示页面</TITLE>
<META http-equiv=Content-Type content="text/html; charset=UTF-8">
<LINK href="style.css" type=text/css rel=stylesheet>
<SCRIPT src="./main.js" type=text/javascript></SCRIPT>
<!-- --------------------=== 调用Weboffice初始化方法 ===--------------------- -->
<SCRIPT language=javascript event=NotifyCtrlReady for=WebOffice1>
/****************************************************
*
*	在装载完Weboffice(执行<object>...</object>)
*	控件后执行 "WebOffice1_NotifyCtrlReady"方法
*
****************************************************/
    WebOffice_Event_Flash("NotifyCtrlReady");
	WebOffice1_NotifyCtrlReady()
</SCRIPT>

<SCRIPT language=javascript event=NotifyWordEvent(eventname) for=WebOffice1>
<!--
WebOffice_Event_Flash("NotifyWordEvent");
 WebOffice1_NotifyWordEvent(eventname);
 
//-->
</SCRIPT>

<SCRIPT language=javascript event=NotifyToolBarClick(iIndex) for=WebOffice1>
<!--
  WebOffice_Event_Flash("NotifyToolBarClick");
 WebOffice1_NotifyToolBarClick(iIndex);
//-->
</SCRIPT>

<SCRIPT language=javascript>
__CreateJSPath = function (js) {
    var scripts = document.getElementsByTagName("script");
    var path = "";
    for (var i = 0, l = scripts.length; i < l; i++) {
        var src = scripts[i].src;
        if (src.indexOf(js) != -1) {
            var ss = src.split(js);
            path = ss[0];
            break;
        }
    }
    var href = location.href;
    href = href.split("#")[0];
    href = href.split("?")[0];
    var ss = href.split("/");
    ss.length = ss.length - 1;
    href = ss.join("/");
    if (path.indexOf("https:") == -1 && path.indexOf("http:") == -1 && path.indexOf("file:") == -1 && path.indexOf("\/") != 0) {
        path = href + "/" + path;
    }
    return path;
}

var bootPATH = __CreateJSPath("main.js");
/****************************************************
*
*		控件初始化WebOffice方法
*
****************************************************/
function WebOffice1_NotifyCtrlReady() {
	document.all.WebOffice1.SetWindowText("授权XX(可通过接口自定义)", 0);
	document.all.WebOffice1.OptionFlag |= 128;
	// 新建文档
	//document.all.WebOffice1.LoadOriginalFile("","doc");
	//打开sdk
	 
	document.all.WebOffice1.LoadOriginalFile(bootPATH+"WebOffice7.0标准版-开发接口SDK.doc", "doc");
	spnWebOfficeInfo.innerText="----   您电脑上安装的WebOffice版本为:V" + document.all.WebOffice1.GetOcxVersion() +"\t\t\t本实例是根据版本V6044编写";
}
var flag=false;
function menuOnClick(id){
	var id=document.getElementById(id);
	var dis=id.style.display;
	if(dis!="none"){
		id.style.display="none";
		
	}else{
		id.style.display="block";
	}
}
/****************************************************
*
*		接收office事件处理方法
*
****************************************************/
var vNoCopy = 0;
var vNoPrint = 0;
var vNoSave = 0;
var vClose=0;
function no_copy(){
	vNoCopy = 1;
}
function yes_copy(){
	vNoCopy = 0;
}


function no_print(){
	vNoPrint = 1;
}
function yes_print(){
	vNoPrint = 0;
}


function no_save(){
	vNoSave = 1;
}
function yes_save(){
	vNoSave = 0;
}
function EnableClose(flag){
 vClose=flag;
}
function CloseWord(){
	
  document.all.WebOffice1.CloseDoc(0); 
}

function WebOffice1_NotifyWordEvent(eventname) {
	if(eventname=="DocumentBeforeSave"){
		if(vNoSave){
			document.all.WebOffice1.lContinue = 0;
			alert("此文档已经禁止保存");
		}else{
			document.all.WebOffice1.lContinue = 1;
		}
	}else if(eventname=="DocumentBeforePrint"){
		if(vNoPrint){
			document.all.WebOffice1.lContinue = 0;
			alert("此文档已经禁止打印");
		}else{
			document.all.WebOffice1.lContinue = 1;
		}
	}else if(eventname=="WindowSelectionChange"){
		if(vNoCopy){
			document.all.WebOffice1.lContinue = 0;
			//alert("此文档已经禁止复制");
		}else{
			document.all.WebOffice1.lContinue = 1;
		}
	}else   if(eventname =="DocumentBeforeClose"){
	    if(vClose==0){
	    	document.all.WebOffice1.lContinue=0;
	    } else{
	    	//alert("word");
		    document.all.WebOffice1.lContinue = 1;
		  }
 }
	//alert(eventname); 
}
function dd(){

	document.all.WebOffice1.FullScreen=0;

}</SCRIPT>
<LINK href="./style.css" type=text/css rel=stylesheet>
<META content="MSHTML 6.00.2900.5921" name=GENERATOR></HEAD>
<BODY style="BACKGROUND: #ccc" onunload="return window_onunload()">
<CENTER>
<DIV 
style="PADDING-RIGHT: 0px; PADDING-LEFT: 0px; BACKGROUND: #fff; PADDING-BOTTOM: 0px; MARGIN: -10px 0px 0px; WIDTH: 1024px; PADDING-TOP: 10px; HEIGHT: 750px" 
align=center>
<FORM name=myform>
<TABLE class=TableBlock width="90%">
  <TBODY>
  <TR class=TableHeader>
    <TD colSpan=2 valign=middle>
      <font size=3> WebOffice演示页面</font> <font color="#ff0000" size=-1><Span ID=spnWebOfficeInfo align="right"></Span></font></TD></TR>
      <tr>
	<td width="100">
	事件列表：
	</td>
	<td >
	<textarea  name="commandtext" cols=100 rows=3></textarea><br>							    
	</td>
	</tr>
</TBODY></TABLE><BR>
<TABLE class=TableBlock width="90%">
  <TBODY>
  <TR>
    <TD class=leftTableData vAlign=top width="15%">
      <DIV class=menuItem onclick="menuOnClick('chc')">常用接口调用 </DIV>
      <DIV id=chc style="DISPLAY: none"><!-- ---------------=== 该处文件格式的value不可以自定义 ===------------------------- --><SELECT 
      id=doctype size=1 name=doctype> <OPTION value=doc selected>Word</OPTION> 
        <OPTION value=xls>Excel</OPTION> <OPTION value=wps>wps</OPTION></SELECT> <INPUT class=btn id=CreateFile onclick=newDoc() type=button value=新建文档 name=CreateFile> 
<INPUT class=btn onclick="return docOpen()" type=button value=打开本地文件 name=button> 
<INPUT class=btn id=showPrint onclick=showPrintDialog() type=button value=显示对话框 name=CreateFile4> 
<INPUT class=btn id=zhiPrints onclick=zhiPrint() type=button value=直接打印 name=zhiPrints> 
<INPUT class=btn id=CreateFile2 onclick=newSave() type=button value=保存 name=CreateFile2> 
<INPUT class=btn id=CreateFile3 onclick=SaveAsTo() type=button value=另存为 name=CreateFile3> 
 
      </DIV>
      <DIV class=menuItem onclick="menuOnClick('docSafe')">文档安全设置 </DIV>
      <DIV id=docSafe style="DISPLAY: none">保护密码： <INPUT style="WIDTH: 74px" 
       value=Password name=docPwd> <INPUT class=btn onclick="return ProtectFull()" type=button value=保护文档> <INPUT class=btn onclick="return UnProtect()" type=button value=解除保护 name=button3> 
<INPUT class=btn onclick="return notPrint()" type=button value=禁用打印> <INPUT class=btn onclick="return okPrint()" type=button value=启用打印 name=button3> 
<INPUT class=btn onclick="return notSave()" type=button value=禁止保存 name=button10> 
<INPUT class=btn onclick="return okSave()" type=button value=允许保存 name=button32> 
<INPUT class=btn onclick="return notCopy()" type=button value=禁止复制 name=button11> 
<INPUT class=btn onclick="return okCopy()" type=button value=允许复制 name=button33> 
<INPUT class=btn onclick="return notDrag()" type=button value=禁止拖动 name=but11> 
<INPUT class=btn onclick="return okDrag()" type=button value=允许拖动 name=but33> 
      事件控制方式： <INPUT class=btn onclick="return no_print()" type=button value=禁用打印> <INPUT class=btn onclick="return yes_print()" type=button value=启用打印 name=button3> 
<INPUT class=btn onclick="return no_save()" type=button value=禁止保存 name=button10> 
<INPUT class=btn onclick="return yes_save()" type=button value=允许保存 name=button32> 
<INPUT class=btn onclick="return no_copy()" type=button value=禁止复制 name=button11> 
<INPUT class=btn onclick="return yes_copy()" type=button value=允许复制 name=button33> 
      </DIV>
      <DIV class=menuItem onclick="menuOnClick('recension')">修订操作 </DIV>
      <DIV id=recension style="DISPLAY: none"><INPUT style="WIDTH: 74px" 
      maxLength=10 value=Test name=UserName> <INPUT class=btn onclick="return SetUserName()" type=button value=设置用户 name=button2> 
<INPUT class=btn onclick="return ProtectRevision()" type=button value=修订文档 name=button4> 
<INPUT class=btn onclick="return ExitRevisions()" type=button value=退出修订 name=button42> 
<INPUT class=btn onclick="return ShowRevisions()" type=button value=显示修订 name=button5> 
<INPUT class=btn onclick="return UnShowRevisions()" type=button value=隐藏修订 name=button6> 
<INPUT class=btn onclick="return AcceptAllRevisions()" type=button value=接受所有修订 name=button7> 
<INPUT class=btn onclick="return unAcceptAllRevisions()" type=button value=拒绝所有修订 name=button72> 
<INPUT class=btn onclick="return GetRevAllInfo()" type=button value=获取修订信息 name=button922> 
      </DIV>
      <DIV class=menuItem onclick="menuOnClick('markset')">书签操作 </DIV>
      <DIV id=markset style="DISPLAY: none"><INPUT class=btn onclick="return addBookmark()" type=button value=设置书签 name=button8> 
		<INPUT class=btn onclick="return taohong()" type=button value=套红 name=button8> 
      </DIV>
      <DIV class=menuItem onclick="menuOnClick('off03menu')">Office03菜单设置 </DIV>
      <DIV id=off03menu style="DISPLAY: none">
      <INPUT class=btn onclick="return hideAll('','','','')" type=button value=隐藏所有 name=button12> 
      <INPUT class=btn onclick="return notMenu()" type=button value=隐藏菜单 name=button12> 
<INPUT class=btn onclick="return okMenu()" type=button value=显示菜单 name=button12> 
<INPUT class=btn onclick="return notOfter()" type=button value=隐藏常用 name=button12> 
<INPUT class=btn onclick="return okOfter()" type=button value=显示常用 name=button12> 
<INPUT class=btn onclick="return notFormat()" type=button value=隐藏格式 name=button12> 
<INPUT class=btn onclick="return okFormat()" type=button value=显示格式 name=button12> 

      </DIV>
      <DIV class=menuItem onclick="menuOnClick('off03menuItem')">Office03菜单项 
      </DIV>
      <DIV id=off03menuItem style="DISPLAY: none"><INPUT class=btn onclick="return hideFileMenu()" type=button value=隐藏文件 name=button122> 
<INPUT class=btn onclick="return showFileMenu()" type=button value=显示文件 name=button123> 
<INPUT class=btn onclick="return hideEditMenu()" type=button value=隐藏编辑 name=button124> 
<INPUT class=btn onclick="return showEditMenu()" type=button value=显示编辑 name=button125> 
      </DIV>
      <DIV class=menuItem onclick="menuOnClick('off03tool')">Office03工具栏项 </DIV>
      <DIV id=off03tool style="DISPLAY: none"><INPUT class=btn onclick="return hideNewItem()" type=button value=隐藏新建 name=button1222> 
<INPUT class=btn onclick="return showNewItem()" type=button value=显示新建 name=button1232> 
<INPUT class=btn onclick="return hideOpenItem()" type=button value=隐藏打开 name=button1242> 
<INPUT class=btn onclick="return showOpenItem()" type=button value=显示打开 name=button1252> 
<INPUT class=btn onclick="return hideSaveItem()" type=button value=隐藏保存 name=button1242> 
<INPUT class=btn onclick="return showSaveItem()" type=button value=显示保存 name=button1252> 
      </DIV>
      <DIV class=menuItem onclick="menuOnClick('off07menu')">Office07菜单设置 </DIV>
      <DIV id=off07menu style="DISPLAY: none">
      <INPUT class=btn onclick="return hideAll('hideall','','','')" type=button value=隐藏全部 name=button12222>
      <INPUT class=btn onclick="return hideAll('showmenu','','','')" type=button value=显示全部 name=button12222>
      <INPUT class=btn onclick="return hideAll('','','','')" type=button value=隐藏功能区 name=button12222>
      <INPUT class=btn onclick="return hideAll('show','','','')" type=button value=显示功能区 name=button12222>
      <INPUT class=btn onclick="return beginMenu_onclick()" type=button value=隐藏开始 name=button12222> 
<INPUT class=btn onclick="return insertMenu_onclick()" type=button value=隐藏插入 name=button12322> 
<INPUT class=btn onclick="return pageMenu_onclick()" type=button value=隐藏页面布局 name=button12422> 
<INPUT class=btn onclick="return adducMenu_onclick()" type=button value=隐藏引用 name=button12522> 
<INPUT class=btn onclick="return emailMenu_onclick()" type=button value=隐藏邮件 name=button12422> 
<INPUT class=btn onclick="return checkMenu_onclick()" type=button value=隐藏审阅 name=button12522> 
<INPUT class=btn onclick="return viewMenu_onclick()" type=button value=隐藏视图 name=button125222> 
<INPUT class=btn onclick="return empolderMenu_onclick()" type=button value=隐藏开发工具 name=button124222> 
<INPUT class=btn onclick="return loadMenu_onclick()" type=button value=隐藏加载项 name=button125222> 
<INPUT class=btn onclick="return allHideMenu_onclick()" type=button value=隐藏全部 name=button1242222> 
<INPUT class=btn onclick="return nullityCopy_onclick()" type=button value=复制无效 name=button12422222> 
<INPUT class=btn onclick="return nullityAffix_onclick()" type=button value=粘贴无效 name=button12422223> 
<INPUT class=btn onclick="return affixCopy_onclick()" type=button value=恢复 name=button1252222> 
      </DIV>
      <DIV class=menuItem onclick="menuOnClick('webofficeTool')">weboffice工具栏 
      </DIV>
      <DIV id=webofficeTool style="DISPLAY: none"><INPUT language=javascript class=btn onclick="return bToolBar_onclick()" type=button value=工具栏(隐/显) name=bToolBar> 
<INPUT language=javascript class=btn onclick="return bToolBar_New_onclick()" type=button value=新建文档(隐/显) name=bToolBar_New> 
<INPUT language=javascript class=btn onclick="return bToolBar_Open_onclick()" type=button value=打开文档(隐/显) name=bToolBar_Open> 
<INPUT language=javascript class=btn onclick="return bToolBar_Save_onclick()" type=button value=保存文档(隐/显) name=bToolBar_Save> 
      </DIV>
       <DIV class=menuItem onclick="menuOnClick('tools')">自定义工具栏</DIV>
      <DIV id=tools style="DISPLAY: none"><INPUT class=btn onclick="return SetCustomToolBtn(0,'扩展钮一')" type=button value=添加扩展钮一 name=EnableClose1> 
      <INPUT class=btn onclick="return SetCustomToolBtn(1,'扩展钮二')" type=button value=添加扩展钮二 name=EnableClose1>
      <INPUT class=btn onclick="return SetCustomToolBtn(2,'扩展钮三')" type=button value=添加扩展钮三 name=EnableClose1>
      <INPUT class=btn onclick="return SetCustomToolBtn(3,'扩展钮四')" type=button value=添加扩展钮四 name=EnableClose1>
      <INPUT class=btn onclick="return SetCustomToolBtn(4,'扩展钮五')" type=button value=添加扩展钮五 name=EnableClose1>

      </DIV>

       <DIV class=menuItem onclick="menuOnClick('word')">屏蔽word关闭按钮 </DIV>
      <DIV id=word style="DISPLAY: none"><INPUT class=btn onclick="return EnableClose(0)" type=button value=禁用Word关闭 name=EnableClose1> 
<INPUT class=btn onclick="return EnableClose(1)" type=button value=启用Word关闭 name=EnableClose2> 
<INPUT class=btn onclick="return CloseWord()" type=button value=关闭Word name=closeWord> 
      </DIV>
      <DIV class=menuItem onclick="menuOnClick('face')">换肤</DIV>
      <DIV id=face style="DISPLAY: none"><INPUT class=btn onclick="return ChangeFace(1)" type=button value=温馨浪漫 name=EnableClose1> 
      <INPUT class=btn onclick="return ChangeFace(2)" type=button value=深沉儒雅 name=EnableClose1>
      <INPUT class=btn onclick="return ChangeFace(3)" type=button value=office风格 name=EnableClose1>
      </DIV>
      
      <DIV class=menuItem onclick="menuOnClick('base64')">base64</DIV>
      <DIV id=base64 style="DISPLAY: none"><INPUT class=btn id=CreateFile4 onclick=GetFileBase64() type=button value=获取文档的base64值 name=CreateFile4> 
			<INPUT class=btn id=CreateFile5 onclick=SaveBinaryFileFromBase64() type=button value=将base64值存回文件 name=CreateFile5>
			<textarea name="FileBase64" rows="10" cols="16"></textarea>
      </DIV>
      
			<DIV class=menuItem onclick="menuOnClick('other')">其它 </DIV>
      <DIV id=other style="DISPLAY: none"><INPUT language=javascript class=btn onclick="return bToolBar_FullScreen_onclick()" type=button value="全  屏" name=bToolBar_FullScreen> 
<INPUT class=btn id=CreateFile32 onclick=TestVBA() type=button value=VBA调用 name=CreateFile32> 
<INPUT class=btn onclick="return UnActiveExcel()" type=button value=退出Excel编辑状态 name=EnableClose1>
      </DIV>
      </TD>
    <TD class=TableData vAlign=top width="85%">
    	<!-- -----------------------------== 装载weboffice控件 ==--------------------------------- -->
      <SCRIPT src="LoadWebOffice.js"></SCRIPT>
			<!-- --------------------------------== 结束装载控件 ==----------------------------------- -->
			</TD></TR></TBODY></TABLE></FORM></DIV></CENTER></BODY></HTML>
