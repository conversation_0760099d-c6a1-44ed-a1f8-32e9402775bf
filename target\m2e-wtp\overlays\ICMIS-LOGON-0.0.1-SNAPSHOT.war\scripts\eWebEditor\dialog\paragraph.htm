<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var Q=lang["DlgPar"];document.write("<title>"+Q+"</title>");var jy="";var jK="";var jA="";var jz="";var ib="";var hI="";var jj="";var jJ="";var dG,dp;if(O.ae){dG=A.F.selection.createRange();dp=A.F.body.createTextRange();}else{dG=I.bH().getRangeAt(0);dp=A.F.createRange();}var jn=A.F.body.getElementsByTagName("P");var bo=new Array();for(var i=0;i<jn.length;i++){if(O.ae){dp.moveToElementText(jn[i]);if(dG.inRange(dp)){bo[bo.length]=jn[i];}else{if(((dG.compareEndPoints("StartToEnd",dp)<0)&&(dG.compareEndPoints("StartToStart",dp)>0))||((dG.compareEndPoints("EndToStart",dp)>0)&&(dG.compareEndPoints("EndToEnd",dp)<0))){bo[bo.length]=jn[i];}}}else{dp.selectNodeContents(jn[i]);if((dp.compareBoundaryPoints(Range.START_TO_END,dG)>=0)&&(dp.compareBoundaryPoints(Range.END_TO_START,dG)<=0)){bo[bo.length]=jn[i];}}}for(var i=0;i<bo.length;i++){if(i==0){jy=bo[i].style.marginTop;jK=bo[i].style.marginBottom;jA=bo[i].style.marginLeft;jz=bo[i].style.marginRight;ib=bo[i].style.textAlign;hI=bo[i].style.lineHeight;jj=bo[i].style.textIndent;jJ=bo[i].style.letterSpacing;}else{if(bo[i].style.marginTop!=jy){jy="";}if(bo[i].style.marginBottom!=jK){jK="";}if(bo[i].style.marginLeft!=jA){jA="";}if(bo[i].style.marginRight!=jz){jz="";}if(bo[i].style.textAlign!=ib){ib="";}if(bo[i].style.lineHeight!=hI){hI="";}if(bo[i].style.textIndent!=jj){jj="";}if(bo[i].style.letterSpacing!=jJ){jJ="";}}}function uB(){var sn=$("d_lineheightdrop").options[$("d_lineheightdrop").selectedIndex].value;if(sn=="other"){return;}else{$("d_lineheight").value=sn;}};function xo(){var aa=dX($("d_lineheight").value);switch(aa){case "":$("d_lineheightdrop").selectedIndex=1;break;case "1":$("d_lineheightdrop").selectedIndex=2;break;case "1.5":$("d_lineheightdrop").selectedIndex=3;break;case "2":$("d_lineheightdrop").selectedIndex=4;break;default:$("d_lineheightdrop").selectedIndex=0;break;}};function ah(){lang.TranslatePage(document);bb($("d_align"),ib.toLowerCase());bb($("d_lineheightdrop"),hI);$("d_margintop").value=jy;$("d_marginbottom").value=jK;$("d_marginleft").value=jA;$("d_marginright").value=jz;$("d_lineheight").value=hI;$("d_textindent").value=jj;$("d_letterspacing").value=jJ;parent.bp(Q);};function ok(){jy=$("d_margintop").value;jK=$("d_marginbottom").value;jA=$("d_marginleft").value;jz=$("d_marginright").value;ib=$("d_align").options[$("d_align").selectedIndex].value;hI=$("d_lineheight").value;jj=$("d_textindent").value;jJ=$("d_letterspacing").value;for(var i=0;i<bo.length;i++){try{bo[i].style.marginTop=jy}catch(e){};try{bo[i].style.marginBottom=jK}catch(e){};try{bo[i].style.marginLeft=jA}catch(e){};try{bo[i].style.marginRight=jz}catch(e){};try{bo[i].style.textAlign=ib;if(ib=="justify"){bo[i].style.textJustify="inter-ideograph";}else{bo[i].style.textJustify="";}}catch(e){};try{bo[i].style.lineHeight=hI}catch(e){};try{bo[i].style.textIndent=jj}catch(e){};try{bo[i].style.letterSpacing=jJ}catch(e){};}parent.aT();}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgPraMargin></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgPraMarginTop></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_margintop size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgPraMarginBottom></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_marginbottom size=10 value=""></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgPraMarginLeft></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_marginleft size=10 value=""></td> <td width="2%"></td> <td noWrap width="20%"><span lang=DlgPraMarginRight></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_marginright size=10 value=""></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr> <td> <fieldset> <legend><span lang=DlgPraOther></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgAlign></span>:</td> <td noWrap width="29%"> <select id=d_align size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='right' lang=DlgAlignRight></option> <option value='center' lang=DlgAlignCenter></option> <option value='justify' lang=DlgAlignFull></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgPraLineHeight></span>:</td> <td noWrap width="29%"> <div style="position:relative;"> <span style="margin-left:62px;width:18px;overflow:hidden;"> <select id=d_lineheightdrop style="width:80px;margin-left:-62px" onchange="uB()"> <option value="other" lang=DlgPraLHOther></option> <option value="" lang=DlgComDefault></option> <option value="1" lang=DlgPraLH1></option> <option value="1.5" lang=DlgPraLH15></option> <option value="2" lang=DlgPraLH2></option> </select> </span> <input style="width:62px;position:absolute;left:0px;" id=d_lineheight size=10 value="" onchange="doChangeLineHeightInput()"> </div> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgPraTextIndent></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_textindent size=10 value=""></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgPraWordSpacing></span>:</td> <td noWrap width="29%"><input style="width:80px" id=d_letterspacing size=10 value=""></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body></html> 