/* CSS Document */
.matter_ul {
	list-style: none;
	margin: 0 0 0 0;
	padding: 0;
	 
}
.gifli{
	background: url("../image/matter/gifli.gif");
	
}
.matter_ul li {
	float: left;
	margin: 0;
	padding: 0;
}
ul,li{margin:0;padding:0;list-style:none;float:left;}
.yj_text{
	width:500px;
	float:right;
	height:30px;
	_margin-top:9px;
	}
.yj_text_l{
	width:29px;
	height:30px;
	float:left;
	background:url(../image/left.gif) no-repeat;
	}
.yj_text_m{
	height:30px;
	width:440px;
	float:left;
	font-size:12px;
	overflow:hidden;
	background:url(../image/mid.gif) repeat-x;
	}
.yj_text_r{
	width:31px;
	height:30px;
	float:left;
	background:url(../image/rig.gif) no-repeat;
	}
.yj_text li{margin:0 5px;}
.yj_text a{color:#000;text-decoration:none;font-size:13px;margin-left:5px;}
