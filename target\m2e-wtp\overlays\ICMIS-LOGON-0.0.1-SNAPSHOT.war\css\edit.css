@charset "gb2312";
/* CSS Document */
*{ padding:0; margin:0 auto; font-size:12px;}

 
.clear { clear:both; height:0; overflow:hidden;}

.h1 { height:40px; line-height:40px; background:#dce9f4; border-bottom:1px solid #bbd4ea;}
.h1 font { float:left; margin-left:20px; color:#003b7f; font-size:14px;}
.h1 font span { color:#ff7200;}

.h2 { height:25px; line-height:20px; background:#dce9f4; border-bottom:1px solid #bbd4ea;}
.h2 font { float:left; margin-left:20px; color:#003b7f; font-size:14px;}
.h2 font span { color:#ff7200;}
.pad_0_6 { padding:0 6px;}

 
.text { height:20px; line-height:20px; overflow:hidden; padding:0 5px; border:1px solid #d2d2d2; color:#000000;}
.text2 { height:20px; line-height:20px; overflow:hidden; padding:0 5px; border:1px solid #d2d2d2; color:#818181;}
 
.table_bj { color:#0e4887; font-weight:bold; margin:20px 0 0 30px;}
.table { color:#0e4887; font-weight:bold;}
.table2 { color:#052448; margin:10px 0 0 30px;}

.v_m { vertical-align:middle;*vertical-align:baseline;_vertical-align:baseline;}
 

.btn_green3 { width:60px; height:25px; line-height:25px; background:url(../images/edit/btn_green3.gif) top; border:none; cursor:pointer; color:#000000;}
.btn_w2 { width:79px; height:25px; cursor:pointer; background:url(../images/btn_blue2.gif); font-size:12px; color:#363636; font-weight:bold; border:none; margin-top:8px;}

.btn_blue2_2 { width:44px; height:23px; line-height:23px; background:url(../images/edit/btn_blue2_2.jpg) top; border:none; cursor:pointer; color:#ffffff;}

.btn_hui3 { width:60px; height:25px; line-height:25px; background:url(../images/edit/btn_hui3.gif); border:none; cursor:pointer; color:#a4a4a4;}


.xian{ width:100%; height:1px; overflow:hidden; border-bottom:1px solid #4ea4ed;}

.biao_qie {
	background:url(../images/line_33.gif) repeat-x;
	float:left;
	height:36px;
	width:100%;
	overflow:hidden;}
.biao_qie .con { float:left; padding-left:15px;}
.biao_qie .con a {margin:0px 5px;
	float:left;
	line-height:36px;
	padding:2px 15px;
	color:#0b77bd;
	cursor:pointer; text-decoration:none;border: 0; }

.biao_qie .con a.on { border-top:#9dd0e9 solid 1px;
	border-left:#9dd0e9 solid 1px;
	border-right:#9dd0e9 solid 1px;
	background:#fff;
	float:left;
	margin-top:7px;
	padding:0px 15px;
	line-height:25px;
	color:#0b77bd;
	font-weight:bold;
	cursor:pointer;text-decoration:none;}

.select_thing { width:100%; float:left; background:#adcce9; position:relative;}

#plcon_11 { display:none;} 
#plcon_22 { display:none;}
#plcon_33 { display:none;}
#plcon_44 { display:none;}
#plcon_55 { display:none;}


.yema { font-size:12px; color:#5b5b5b;}
.yema a { color:#567eaf; padding:0 5px; line-height:16px; text-decoration:none;}
.yema a:hover { color:#ffffff; padding:0 5px; line-height:16px; background:#2378cd;}
.yema a.on{ color:#ffffff; padding:0 5px; line-height:16px; background:#2378cd;}


.hidden { color:#003b7f; text-decoration:none;float:right;margin:5px 15px 0 0; font-weight:100;}
.hidden:hover { text-decoration:underline;}


.date { width:20px; height:19px; line-height:25px; background:url(../images/edit/rili.jpg) top; border:none; cursor:pointer; font-weight:bold;}

