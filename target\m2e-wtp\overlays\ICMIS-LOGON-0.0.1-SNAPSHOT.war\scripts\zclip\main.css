@charset "utf-8";
/* CSS Document */
html,body,div,span,h1,h2,h3,h4,h5,h6,p,pre,a,code,em,img,small,strong,sub,sup,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label{margin:0;padding:0;border:0;outline:0;font-size:100%;vertical-align:baseline;background:transparent}
a{color:#007bc4/*#424242*/; text-decoration:none;}
a:hover{text-decoration:underline}
ol,ul{list-style:none}
table{border-collapse:collapse;border-spacing:0}
html{background:url(../images/bg.png)}
body{height:100%; font:12px/18px "Microsoft Yahei", Tahoma, Helvetica, Arial, Verdana, "\5b8b\4f53", sans-serif; color:#51555C; background: url(../images/body_bg.gif) repeat-x}
img{border:none}


#header{width:980px; height:92px; margin:0 auto; position:relative;}
#logo{width:240px; height:90px; background:url(../images/logo_demo.gif) no-repeat}
#logo h1{text-indent:-999em}
#logo h1 a{display:block; width:240px; height:90px}


#main{width:980px; min-height:600px; margin:30px auto 0 auto; border:1px solid #d3d3d3; background:#fff; -moz-border-radius:5px;-khtml-border-radius: 5px;-webkit-border-radius: 5px; border-radius:5px;}
h2.top_title{margin:4px 20px; padding-top:15px; padding-left:20px; padding-bottom:10px; border-bottom:1px solid #d3d3d3; font-size:18px; color:#a84c10; background:url(../images/arrL.gif) no-repeat 2px 14px}

#footer{height:60px;}
#footer p{ padding:10px 2px; line-height:24px; text-align:center}
#footer p a:hover{color:#51555C}
#stat{display:none}

.google_ad{width:728px; height:90px; margin:50px auto}
.ad_76090,.ad_demo{width:760px; height:90px; margin:40px auto}
.demo_topad{position:absolute; top:15px; right:0px; width:470px; height:60px;}


@media screen and (min-width: 320px) and (max-width : 480px) {
 html{-webkit-text-size-adjust: none;}
 #header{width:100%}
 #main{width:100%; margin:10px auto;  -moz-border-radius:0px;-khtml-border-radius: 0px;-webkit-border-radius: 0px; border-radius:0px;}
 .demo_topad{display:none}
 .google_ad{width:100%; margin:40px auto; text-align:center}
.ad_76090,.ad_demo{width:100%; height:auto; margin:40px auto;text-align:center}
.demo{width:98%; margin:10px auto}
}
