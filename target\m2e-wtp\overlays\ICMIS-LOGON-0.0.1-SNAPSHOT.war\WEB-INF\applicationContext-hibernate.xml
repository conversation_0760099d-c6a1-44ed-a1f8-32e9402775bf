<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
	http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<!-- hibernate的定义 -->
	<!-- 迁移到websphere5.1时，应使用LocalSessionFactoryBean -->
	<!-- <bean id="sessionFactory" class="org.springframework.orm.hibernate3.LocalSessionFactoryBean"> -->
	<bean id="nativeJdbcExtractor" lazy-init="true"
		class="org.springframework.jdbc.support.nativejdbc.SimpleNativeJdbcExtractor" />
	<bean id="lobHandler" lazy-init="true"
		class="org.springframework.jdbc.support.lob.OracleLobHandler">
		<property name="nativeJdbcExtractor">
			<ref bean="nativeJdbcExtractor" />
		</property>
	</bean>

	<bean id="sessionFactory"
		class="org.springframework.orm.hibernate3.annotation.AnnotationSessionFactoryBean">
		<!-- <bean id="sessionFactory" -->
		<!-- class="org.springframework.orm.hibernate4.LocalSessionFactoryBean"> -->
		<property name="dataSource" ref="dataSource" />
		<!--<property name="configLocation" value="classpath:hibernate.cfg.xml" 
			/> -->
		<property name="lobHandler" ref="lobHandler" />

		<property name="mappingLocations">
			<!-- <property name="mappingDirectoryLocations"> <property name="mappingResources"> -->
			<!--hibernate model的配置，可以使用通配符 <value>classpath*:ie/**/model/*.xml</value> -->
			<list>
				<value>classpath*:/**/model/*.hbm.xml</value>
				<!-- <value>classpath:cn/com/sinosoft/**/model/</value> -->
			</list>
		</property>
		<!-- <property name="mappingJarLocations"> <list> <value>WEB-INF/lib/sinosoft-core-1.0-classes.jar</value> 
			</list> </property> -->
		<property name="hibernateProperties">

			<props>
				<!--<prop key="hibernate.dialect">org.hibernate.dialect.Oracle9iDialect</prop> -->
				<prop key="hibernate.dialect">ie.bsp.MyOracleDialect</prop>
				<prop key="hibernate.show_sql">true</prop>
				<prop key="hibernate.cache.provider_class">org.hibernate.cache.EhCacheProvider</prop>
				<prop key="hibernate.cache.use_query_cache">true</prop>
				<prop key="hibernate.jdbc.fetch_size">100</prop>
				<prop key="hibernate.jdbc.batch_size">50</prop>

				<prop key="hibernate.query.factory_class">${hibernate.query.factory_class}</prop>

				<!-- Hibernate3.0的查询翻译器 -->
				<!-- <prop key="hibernate.query.factory_class">org.hibernate.hql.ast.ASTQueryTranslatorFactory</prop> -->
				<!-- Hibernate2.1的查询翻译器 -->
				<!-- <prop key="hibernate.query.factory_class">org.hibernate.hql.classic.ClassicQueryTranslatorFactory</prop> -->

				<!--c3p0连接池的配置 <prop key="hibernate.connection.provider_class">org.hibernate.connection.C3P0ConnectionProvider</prop> 
					<prop key="hibernate.c3p0.min_size">5</prop> <prop key="hibernate.c3p0.max_size">15</prop> 
					<prop key="hibernate.c3p0.timeout">3600</prop> <prop key="hibernate.c3p0.max_statements">50</prop> -->
			</props>
		</property>


	</bean>
	<!-- <bean id="baseTxProxy" abstract="true" class="org.springframework.transaction.interceptor.TransactionProxyFactoryBean"> 
		<property name="transactionManager"> <ref bean="txManager" /> </property> 
		<property name="transactionAttributes"> <props> <prop key="*">PROPAGATION_REQUIRED</prop> 
		<prop key="load*">PROPAGATION_REQUIRED,readOnly</prop> <prop key="get*">PROPAGATION_REQUIRED,readOnly</prop> 
		<prop key="find*">PROPAGATION_REQUIRED,readOnly</prop> </props> </property> 
		</bean> -->
</beans>
