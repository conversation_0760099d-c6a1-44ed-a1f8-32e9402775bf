<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/test">
  <process id="sealUserSbApplyNew" name="公章使用申请" isExecutable="true">
    <userTask id="dz" name="公章使用申请订正" activiti:assignee="${startUser}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditeitParentInput.ac" default="0&amp;pageState=edit"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <userTask id="ksfzrshnextzg" name="第一步：科室负责人" activiti:candidateGroups="OS100000104">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditParentInput.ac" default="1&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow4" sourceRef="dz" targetRef="ksfzrshnextzg"></sequenceFlow>
    <userTask id="usertask4" name="第二步：主管部门审批" activiti:assignee="${manager}" activiti:candidateGroups="OS100000105">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditParentInput.ac" default="2&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <userTask id="usertask6" name="第四步：所领导审批" activiti:assignee="${leader}" activiti:candidateGroups="OS100000106">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditParentInput.ac" default="4&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="exclusivegateway6" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow18" sourceRef="ksfzrshnextzg" targetRef="exclusivegateway6"></sequenceFlow>
    <sequenceFlow id="flow19" name="通过" sourceRef="exclusivegateway6" targetRef="usertask4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <startEvent id="startevent1" name="Start"></startEvent>
    <endEvent id="endevent1" name="End"></endEvent>
    <exclusiveGateway id="exclusivegateway8" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow22" sourceRef="usertask4" targetRef="exclusivegateway8"></sequenceFlow>
    <sequenceFlow id="flow23" name="通过" sourceRef="exclusivegateway8" targetRef="usertask8">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${separation=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow24" name="驳回" sourceRef="exclusivegateway6" targetRef="dz">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow25" sourceRef="startevent1" targetRef="ksfzrshnextzg"></sequenceFlow>
    <sequenceFlow id="flow26" name="驳回" sourceRef="exclusivegateway8" targetRef="dz">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="bureau" name="第五步：法人审核" activiti:candidateGroups="OS100000403">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditParentInput.ac" default="5&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow29" name="通过" sourceRef="bureau" targetRef="endevent1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow30" name="驳回" sourceRef="bureau" targetRef="dz">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="exclusivegateway9" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow31" sourceRef="usertask6" targetRef="exclusivegateway9"></sequenceFlow>
    <sequenceFlow id="flow32" sourceRef="exclusivegateway9" targetRef="endevent1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${step=='2'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow33" sourceRef="exclusivegateway9" targetRef="bureau">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${step=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow34" sourceRef="exclusivegateway9" targetRef="dz">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${step=='dz'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="usertask7" name="第二步：分稿部门意见" activiti:assignee="${otherManager}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditParentInput.ac" default="2&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow35" name="通过" sourceRef="exclusivegateway8" targetRef="usertask7">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${separation=='2'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="exclusivegateway10" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow36" sourceRef="usertask7" targetRef="exclusivegateway10"></sequenceFlow>
    <sequenceFlow id="flow37" name="通过" sourceRef="exclusivegateway10" targetRef="usertask8">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow38" name="驳回" sourceRef="exclusivegateway10" targetRef="dz">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="usertask8" name="第三步：办公室审批" activiti:candidateGroups="OS100000107">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapplynew/sealUseApplyNew_auditParentInput.ac" default="3&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="exclusivegateway11" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow39" sourceRef="usertask8" targetRef="exclusivegateway11"></sequenceFlow>
    <sequenceFlow id="flow40" name="通过" sourceRef="exclusivegateway11" targetRef="usertask6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow41" name="驳回" sourceRef="exclusivegateway11" targetRef="dz">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_sealUserSbApplyNew">
    <bpmndi:BPMNPlane bpmnElement="sealUserSbApplyNew" id="BPMNPlane_sealUserSbApplyNew">
      <bpmndi:BPMNShape bpmnElement="dz" id="BPMNShape_dz">
        <omgdc:Bounds height="83.0" width="121.0" x="91.0" y="289.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ksfzrshnextzg" id="BPMNShape_ksfzrshnextzg">
        <omgdc:Bounds height="76.0" width="121.0" x="91.0" y="99.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask4" id="BPMNShape_usertask4">
        <omgdc:Bounds height="76.0" width="142.0" x="381.0" y="99.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask6" id="BPMNShape_usertask6">
        <omgdc:Bounds height="79.0" width="118.0" x="901.0" y="97.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway6" id="BPMNShape_exclusivegateway6">
        <omgdc:Bounds height="40.0" width="40.0" x="295.0" y="118.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="startevent1" id="BPMNShape_startevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="1.0" y="119.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endevent1" id="BPMNShape_endevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="1133.0" y="120.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway8" id="BPMNShape_exclusivegateway8">
        <omgdc:Bounds height="40.0" width="40.0" x="574.0" y="118.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="bureau" id="BPMNShape_bureau">
        <omgdc:Bounds height="76.0" width="105.0" x="1029.0" y="191.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway9" id="BPMNShape_exclusivegateway9">
        <omgdc:Bounds height="40.0" width="40.0" x="1061.0" y="118.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask7" id="BPMNShape_usertask7">
        <omgdc:Bounds height="76.0" width="142.0" x="381.0" y="252.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway10" id="BPMNShape_exclusivegateway10">
        <omgdc:Bounds height="40.0" width="40.0" x="643.0" y="269.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask8" id="BPMNShape_usertask8">
        <omgdc:Bounds height="76.0" width="121.0" x="663.0" y="101.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway11" id="BPMNShape_exclusivegateway11">
        <omgdc:Bounds height="40.0" width="40.0" x="811.0" y="118.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow4" id="BPMNEdge_flow4">
        <omgdi:waypoint x="151.0" y="289.0"></omgdi:waypoint>
        <omgdi:waypoint x="151.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow18" id="BPMNEdge_flow18">
        <omgdi:waypoint x="212.0" y="137.0"></omgdi:waypoint>
        <omgdi:waypoint x="295.0" y="138.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow19" id="BPMNEdge_flow19">
        <omgdi:waypoint x="335.0" y="138.0"></omgdi:waypoint>
        <omgdi:waypoint x="381.0" y="137.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="54.0" width="24.0" x="348.0" y="121.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow22" id="BPMNEdge_flow22">
        <omgdi:waypoint x="523.0" y="137.0"></omgdi:waypoint>
        <omgdi:waypoint x="574.0" y="138.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow23" id="BPMNEdge_flow23">
        <omgdi:waypoint x="614.0" y="138.0"></omgdi:waypoint>
        <omgdi:waypoint x="663.0" y="139.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="100.0" x="605.0" y="111.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow24" id="BPMNEdge_flow24">
        <omgdi:waypoint x="315.0" y="158.0"></omgdi:waypoint>
        <omgdi:waypoint x="315.0" y="330.0"></omgdi:waypoint>
        <omgdi:waypoint x="212.0" y="330.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="54.0" width="24.0" x="319.0" y="189.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow25" id="BPMNEdge_flow25">
        <omgdi:waypoint x="36.0" y="136.0"></omgdi:waypoint>
        <omgdi:waypoint x="91.0" y="137.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow26" id="BPMNEdge_flow26">
        <omgdi:waypoint x="594.0" y="158.0"></omgdi:waypoint>
        <omgdi:waypoint x="593.0" y="330.0"></omgdi:waypoint>
        <omgdi:waypoint x="212.0" y="330.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="54.0" width="24.0" x="604.0" y="175.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow29" id="BPMNEdge_flow29">
        <omgdi:waypoint x="1134.0" y="229.0"></omgdi:waypoint>
        <omgdi:waypoint x="1150.0" y="228.0"></omgdi:waypoint>
        <omgdi:waypoint x="1150.0" y="155.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="32.0" x="1100.0" y="112.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow30" id="BPMNEdge_flow30">
        <omgdi:waypoint x="1081.0" y="267.0"></omgdi:waypoint>
        <omgdi:waypoint x="1081.0" y="331.0"></omgdi:waypoint>
        <omgdi:waypoint x="1033.0" y="331.0"></omgdi:waypoint>
        <omgdi:waypoint x="212.0" y="330.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="32.0" x="1023.0" y="309.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow31" id="BPMNEdge_flow31">
        <omgdi:waypoint x="1019.0" y="136.0"></omgdi:waypoint>
        <omgdi:waypoint x="1061.0" y="138.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow32" id="BPMNEdge_flow32">
        <omgdi:waypoint x="1101.0" y="138.0"></omgdi:waypoint>
        <omgdi:waypoint x="1133.0" y="137.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow33" id="BPMNEdge_flow33">
        <omgdi:waypoint x="1081.0" y="158.0"></omgdi:waypoint>
        <omgdi:waypoint x="1081.0" y="191.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow34" id="BPMNEdge_flow34">
        <omgdi:waypoint x="1081.0" y="158.0"></omgdi:waypoint>
        <omgdi:waypoint x="872.0" y="330.0"></omgdi:waypoint>
        <omgdi:waypoint x="212.0" y="330.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow35" id="BPMNEdge_flow35">
        <omgdi:waypoint x="594.0" y="158.0"></omgdi:waypoint>
        <omgdi:waypoint x="452.0" y="252.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="100.0" x="563.0" y="208.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow36" id="BPMNEdge_flow36">
        <omgdi:waypoint x="523.0" y="290.0"></omgdi:waypoint>
        <omgdi:waypoint x="643.0" y="289.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow37" id="BPMNEdge_flow37">
        <omgdi:waypoint x="683.0" y="289.0"></omgdi:waypoint>
        <omgdi:waypoint x="721.0" y="288.0"></omgdi:waypoint>
        <omgdi:waypoint x="723.0" y="177.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="100.0" x="728.0" y="221.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow38" id="BPMNEdge_flow38">
        <omgdi:waypoint x="663.0" y="309.0"></omgdi:waypoint>
        <omgdi:waypoint x="663.0" y="332.0"></omgdi:waypoint>
        <omgdi:waypoint x="212.0" y="330.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="100.0" x="614.0" y="339.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow39" id="BPMNEdge_flow39">
        <omgdi:waypoint x="784.0" y="139.0"></omgdi:waypoint>
        <omgdi:waypoint x="811.0" y="138.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow40" id="BPMNEdge_flow40">
        <omgdi:waypoint x="851.0" y="138.0"></omgdi:waypoint>
        <omgdi:waypoint x="901.0" y="136.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="100.0" x="850.0" y="149.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow41" id="BPMNEdge_flow41">
        <omgdi:waypoint x="831.0" y="158.0"></omgdi:waypoint>
        <omgdi:waypoint x="830.0" y="331.0"></omgdi:waypoint>
        <omgdi:waypoint x="212.0" y="330.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="100.0" x="842.0" y="219.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>