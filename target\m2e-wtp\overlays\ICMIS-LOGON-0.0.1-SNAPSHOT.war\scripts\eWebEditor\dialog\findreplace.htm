<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var bm=lang["DlgFR"];document.write("<title>"+bm+"</title>");var fc;var dC;if(F.as){if(EWEB.T.selection.type=="Control"){dC=EWEB.T.body.createTextRange();dC.collapse(true);}else{dC=EWEB.T.selection.createRange();}}function yq(){var vV=0;var xa=0;var vv=0;if($("d_matchcase").checked){xa=4;}if($("d_matchword").checked){vv=2;}vV=xa+vv;return(vV);};function sn(){if($("d_search").value.length<1){alert(lang["DlgFRNoKey"]);return false;}else{return true;}};function qo(){if(!sn()){return;}var mi=$("d_search").value;if(F.as){dC.collapse(false);if(dC.findText(mi,1000000000,yq())){dC.select();C.Release();C.Save(true);}else{var tS=confirm(lang["DlgFRRestart"]);if(tS){dC.expand("textedit");dC.collapse();dC.select();qo();}}}else{if(dC){fc=EWEB.aR.getSelection();if(fc.rangeCount>0){fc.removeAllRanges();}fc.addRange(dC);}var ta=($("d_matchcase").checked);var uG=($("d_matchword").checked);if(EWEB.aR.find(mi,ta,false,false,uG)){fc=EWEB.aR.getSelection();dC=fc.getRangeAt(0);}else{var tS=confirm(lang["DlgFRRestart"]);if(tS){if(fc){dC=null;fc.removeAllRanges();}qo();}}}};function yT(){if(!sn()){return;}var lp=$("d_replace").value;if(F.as){if(dC.text!=""){dC.text=lp;}}else{if(dC){dC.deleteContents();if(lp!=""){var aU=EWEB.T.createTextNode(lp);dC.insertNode(aU);dC.selectNode(aU);dC.collapse(false);}}}qo();};function xq(){if(!sn()){return;}var mi=$("d_search").value;var lp=$("d_replace").value;var qO=0;var sd="";if(F.as){dC.expand("textedit");dC.collapse();dC.select();while(dC.findText(mi,1000000000,yq())){dC.select();dC.text=lp;qO++;}}else{var ta=($("d_matchcase").checked);var uG=($("d_matchword").checked);fc=EWEB.aR.getSelection();fc.removeAllRanges();while(EWEB.aR.find(mi,ta,false,false,uG)){fc=EWEB.aR.getSelection();dC=fc.getRangeAt(0);dC.deleteContents();if(lp!=""){var aU=EWEB.T.createTextNode(lp);dC.insertNode(aU);dC.selectNode(aU);dC.collapse(false);}fc.removeAllRanges();fc.addRange(dC);qO++;}}if(qO==0){sd=lang["DlgFRNoFound"]}else{sd=qO+" "+lang["DlgFRReplaceOK"];}alert(sd);};function aq(){lang.ag(document);parent.ar(bm);} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table cellspacing="0" cellpadding="0" border="0" align=center> <tr> <td valign="top" align="left" nowrap width="60%"> <label for="d_search"><span lang=DlgFRSearchKey></span></label><br> <input type=text size=25 id="d_search"><br> <label for="d_replace"><span lang=DlgFRReplaceKey></span></label><br> <input type=text size=25 id="d_replace"><br> <input type=checkbox id="d_matchcase"><label for="d_matchcase"><span lang=DlgFRMatchCase></span></label><br> <input type=checkbox id="d_matchword"><label for="d_matchword"><span lang=DlgFRMatchWord></span></label> </td> <td width="5%"> <td rowspan="2" valign="bottom" width="35%"> <table border=0 cellpadding=0 cellspacing=5 width="100%" align=center> <tr><td><input type=button class="dlgBtnFind dlgBtn" name="btnFind" onClick="qo();" value="" lang=DlgFRFindNext></td></tr> <tr><td><input type=button class="dlgBtnFind dlgBtn" name="btnCancel" onClick="parent.bn()" value="" lang=DlgBtnClose></td></tr> <tr><td><input type=button class="dlgBtnFind dlgBtn" name="btnReplace" onClick="yT();" value="" lang=DlgFRReplace></td></tr> <tr><td><input type=button class="dlgBtnFind dlgBtn" name="btnReplaceall" onClick="xq();" value="" lang=DlgFRReplaceAll></td></tr> </table> </td> </tr> </table> </td></tr></table> </body> </html> 