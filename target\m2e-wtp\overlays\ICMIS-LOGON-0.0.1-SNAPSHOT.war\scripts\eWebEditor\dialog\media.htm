<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj="INSERT";var ak=lang["DlgComInsert"];var D;var aI="http://";var au="480";var at="400";var dQ="";var bt="";var cJ="";var cL="";var cg="true";var cX="file";var bz=((parseFloat(config.AllowMediaSize)>0)?true:false);pw();var Q=lang["DlgMedia"]+"("+ak+")";document.write("<title>"+Q+"</title>");function pw(){if(I.ay()!="Control"){return;}var bT;D=I.aX();if(D.tagName=="IMG"){bT=rX(D);if(bT=="mediaplayer6"||bT=="mediaplayer7"||bT=="realplayer"||bT=="quicktime"||bT=="flv"){aj="MODI";}}if(aj!="MODI"){return;}ak=lang["DlgComModify"];cX="url";dQ=bT;bt=bU(D,"align");au=jV(dE(D,"width"));at=jV(dE(D,"height"));cJ=bU(D,"vspace");cL=bU(D,"hspace");bg.Init(rZ(D));switch(bT){case "mediaplayer6":if(bg.ka=="object"){aI=bg.cl("filename");}else{aI=bg.cl("src");}cg=bg.cl("autostart");break;case "mediaplayer7":if(bg.ka=="object"){aI=bg.cl("url");}else{aI=bg.cl("src");}cg=bg.cl("autostart");break;case "realplayer":aI=bg.cl("src");cg=bg.cl("autostart");break;case "quicktime":aI=bg.cl("src");cg=bg.cl("autoplay");break;case "flv":var sy=bg.cl("flashvars");aI=sk(sy,"file");cg=sk(sy,"autostart");break;}if(cg=="-1"||cg=="1"){cg="true";}else if(cg=="0"){cg="false";}};function sk(ss,bn){var P=new RegExp('^[\\s\\S]*?\\b'+bn+'\\s*?=\\s*?([^&=]+)(?=[&$])[\\s\\S]*?$','gi');if(P.test(ss)){return ss.replace(P,'$1')}else{return '';}};function xM(dc){if(dc=="url"){$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_checkfromurl").checked=true;if(bz){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}}else{$("d_checkfromurl").checked=false;$("uploadfile").disabled=false;$("d_checkfromfile").checked=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}}};function UploadError(an){eW();xM('file');$("divProcessing").style.display="none";try{cv($("uploadfile"),jF(an,config.AllowMediaExt,config.AllowMediaSize));}catch(e){}};function UploadSaved(fw){if(config.BaseHref!=""){var cy=fw.substr(fw.lastIndexOf(".")+1);if((cy.toLowerCase()=="flv"&&$("d_plugin").value=="")||($("d_plugin").value=="flv")){fw=wf(nk(fw));}}$("d_fromurl").value=fw;cH();};function cH(){EWIN.insertHTML(of());parent.aT();};function ok(){$("d_width").value=du($("d_width").value);$("d_height").value=du($("d_height").value);$("d_vspace").value=du($("d_vspace").value);$("d_hspace").value=du($("d_hspace").value);if($("d_checkfromurl").checked){cH();}else{if(!hw($("uploadfile").value,config.AllowMediaExt)){UploadError("ext");return false;}fD();$("divProcessing").style.display="";document.myuploadform.submit();}};function fD(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_plugin").disabled=true;$("d_align").disabled=true;$("d_autostart").disabled=true;$("d_width").disabled=true;$("d_height").disabled=true;$("d_vspace").disabled=true;$("d_hspace").disabled=true;$("btn_ok").disabled=true;};function eW(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_plugin").disabled=false;$("d_align").disabled=false;$("d_autostart").disabled=false;$("d_width").disabled=false;$("d_height").disabled=false;$("d_vspace").disabled=false;$("d_hspace").disabled=false;$("btn_ok").disabled=false;};function xN(){$("tdPreview").innerHTML=of(true);};function of(uh,or){bt=$("d_align").value;au=$("d_width").value;at=$("d_height").value;cJ=$("d_vspace").value;cL=$("d_hspace").value;cg=$("d_autostart").value;dQ=$("d_plugin").value;if(or){aI=or;}else{if(uh){au="180";at="140";var R,v;if($("d_checkfromurl").checked){v=$("d_fromurl").value;R=v;if(config.BaseHref!=""){R=nk(R);}}else{v=$("uploadfile").value;R="file:///"+v;}aI=R;}else{aI=dX($("d_fromurl").value);}if(aI=="http://"||aI=="file:///"){return "";}}if(dQ==""){var n=aI.lastIndexOf(".");if(n>0){var cy=aI.substr(n+1).toLowerCase();switch(cy){case "flv":dQ="flv";break;case "asf":case "avi":case "mp3":case "mp4":case "wav":case "mpg":case "mpeg":case "mid":case "midi":case "aif":case "aifc":case "aiff":dQ="mediaplayer6";break;case "wmv":case "wma":dQ="mediaplayer7";break;case "ra":case "ram":case "rm":case "rmvb":dQ="realplayer";break;case "qt":case "mov":dQ="quicktime";break;}}}if(or){if(dQ==""){dQ="realplayer";}}if(dQ==""){alert(lang["DlgMediaMsgPlugin"]);return "";}var mT;if(dQ=="flv"){if(uh){mT="../plugin/flvplayer.swf";}else{mT=fH("plugin/flvplayer.swf");}}var dK='';if(au!=''){dK+=' width="'+au+'"';}if(at!=''){dK+=' height="'+at+'"';}if(bt!=''){dK+=' align="'+bt+'"';}if(cJ!=''){dK+=' vspace="'+cJ+'"';}if(cL!=''){dK+=' hspace="'+cL+'"';}var f="";switch(dQ){case "mediaplayer6":f='<object classid="clsid:22d6f312-b0f6-11d0-94ab-0080c74c7e95"'+dK+'>'+'<param name="filename" value="'+aI+'">'+'<param name="autostart" value="'+cg+'">'+'<param name="showcontrols" value="true">'+'<param name="loop" value="true">'+'<embed type="application/x-mplayer2" src="'+aI+'"'+dK+' autostart="'+cg+'" showcontrols="true" loop="true" pluginspage="http://microsoft.com/windows/mediaplayer/en/download/"></embed>'+'</object>';break;case "mediaplayer7":f='<object classid="clsid:6bf52a52-394a-11d3-b153-00c04f79faa6"'+dK+'>'+'<param name="url" value="'+aI+'">'+'<param name="autostart" value="'+cg+'">'+'<param name="uimode" value="full">'+'<embed type="application/x-mplayer2" src="'+aI+'"'+dK+' autostart="'+cg+'" uimode="full" pluginspage="http://microsoft.com/windows/mediaplayer/en/download/"></embed>'+'</object>';break;case "realplayer":f='<object classid="clsid:cfcdaa03-8be4-11cf-b84b-0020afbbccfa"'+dK+'>'+'<param name="src" value="'+aI+'">'+'<param name="autostart" value="'+cg+'">'+'<param name="controls" value="ImageWindow,ControlPanel,StatusBar">'+'<param name="console" value="Clip1">'+'<embed type="audio/x-pn-realaudio-plugin" src="'+aI+'"'+dK+' autostart="'+cg+'" controls="ImageWindow,ControlPanel,StatusBar" console="Clip1"></embed>'+'</object>';break;case "quicktime":f='<object classid="clsid:02bf25d5-8c17-4b23-bc80-d3488abddc6b" codebase="http://www.apple.com/qtactivex/qtplugin.cab"'+dK+'>'+'<param name="src" value="'+aI+'">'+'<param name="autoplay" value="'+cg+'">'+'<param name="controller" value="false">'+'<embed type="video/quicktime" src="'+aI+'"'+dK+' autoplay="'+cg+'" controller="false" pluginspage="http://www.apple.com/quicktime/download/"></embed>'+'</object>';break;case "flv":f='<object classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000"'+dK+'>'+'<param name="movie" value="'+mT+'">'+'<param name="flashvars" value="file='+aI+'&autostart='+cg+'">'+'<param name="quality" value="high">'+'<param name="allowfullscreen" value="true">'+'<embed type="application/x-shockwave-flash" src="'+mT+'"'+dK+' flashvars="file='+aI+'&autostart='+cg+'" quality="high" allowfullscreen="true" pluginspage="http://www.macromedia.com/go/getflashplayer"></embed>'+'</object>';break;default:break;}return f};function ah(){lang.TranslatePage(document);bb($("d_plugin"),dQ.toLowerCase());bb($("d_align"),bt.toLowerCase());bb($("d_autostart"),cg.toLowerCase());if(!bz){cX="url";}xM(cX);$("d_fromurl").value=aI;$("d_width").value=au;$("d_height").value=at;$("d_vspace").value=cJ;$("d_hspace").value=cL;var kP=$("TD_Right");var eU=$("Fieldset_Right");var h1=kP.offsetHeight;var h2=eU.offsetHeight;if(h1>h2){if(O.ae){eU.style.height=h1+"px";}else{eU.style.height=(h1-2)+"px";}}xN();parent.bp(Q);};function js(bG,hp,co){if(co=="tab_mfu"){DLGMFU.lY("media",$(co),DLGTab.gR[1].Width+"px",DLGTab.gR[1].Height+"px");}}</script> <script event="OnCancel(an)" for="eWebEditorMFU">if(an==""){parent.aP();}</script> <script event="OnUpload(an, aa)" for="eWebEditorMFU">if(an=="endall"||an=="endapart"){var f="";var dY=aa.split("|");for(var i=0;i<dY.length;i++){var a=dY[i].split("::");if(a.length==3&&a[1]!=""){var bO=a[0].substr(a[0].lastIndexOf("\\")+1);var fq=a[1];f+=of(false,fq)+"<br>";EWIN.addUploadFile(bO,fq);}}EWIN.insertHTML(f);parent.aT();}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <script type="text/javascript">if(config.MFUEnable=="1"){DLGTab.jB([[lang["DlgComTabNormal"],"tab_normal"],[lang["DlgComTabMFU"],"tab_mfu"]]);}</script> <table id="tab_normal" border=0 cellpadding=0 cellspacing=5 align=center> <tr valign=top><td> <table border=0 cellpadding=0 cellspacing=0 align=center width="100%"> <tr> <td> <fieldset> <legend><span lang=DlgMediaSource></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript">
if(bz){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"xM('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\">");document.write(jL("media"));document.write("</td>");document.write("</tr>");}</script> <tr> <td noWrap width="20%"><input type=radio id="d_checkfromurl" value="1" onclick="xM('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td noWrap width="80%"> <script type="text/javascript">if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:100%' size=10 value='http://'></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"xQ('media','fromurl')\" value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:100%' size=10 value='http://'>");}</script> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgMediaEffect></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgMediaPlugin></span>:</td> <td noWrap colspan=4> <select id=d_plugin size=1 style="width:100%"> <option value='' selected lang=DlgMediaPluginAuto></option> <option value='mediaplayer6' lang=DlgMediaPluginWMP6></option> <option value='mediaplayer7' lang=DlgMediaPluginWMP7></option> <option value='realplayer' lang=DlgMediaPluginReal></option> <option value='quicktime' lang=DlgMediaPluginQT></option> <option value='flv' lang=DlgMediaPluginFLV></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgAlign></span>:</td> <td noWrap width="29%"> <select id=d_align size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='right' lang=DlgAlignRight></option> <option value='top' lang=DlgAlignTop></option> <option value='middle' lang=DlgAlignMiddle></option> <option value='bottom' lang=DlgAlignBottom></option> <option value='absmiddle' lang=DlgAlignAbsmiddle></option> <option value='absbottom' lang=DlgAlignAbsbottom></option> <option value='baseline' lang=DlgAlignBaseline></option> <option value='texttop' lang=DlgAlignTexttop></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgMediaAutoStart></span>:</td> <td noWrap width="29%"> <select id=d_autostart size=1 style="width:80px"> <option value='' selected lang=DlgComDefault></option> <option value='true' lang=DlgComYes></option> <option value='false' lang=DlgComNo></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgMediaWidth></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_width size=10 value="480"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgMediaHeight></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_height size=10 value="400"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgComVSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_vspace size=10 value="" onkeydown="et(event);"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgComHSpace></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_hspace size=10 value="" onkeydown="et(event);"></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> </table> </td><td id="TD_Right"> <fieldset id="Fieldset_Right"> <legend><span lang=DlgComPreview></span></legend> <table border=0 cellpadding=0 cellspacing=5 width="200" height="160" valign=top id=tablePreview> <tr><td colspan=2 bgcolor=#FFFFFF align=center valign=middle id=tdPreview height="100%"> </td></tr> <tr><td id=tdPreviewSize></td><td align=right><input type=button class="dlgBtn" id=btnPreivew onclick="xN()" lang=DlgComPreview></td></tr> </table> </fieldset> </td></tr> <tr><td noWrap align=right colspan=2><input type=submit class="dlgBtnCommon dlgBtn" value='' id="d_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="$('tdPreview').innerHTML='';parent.aP();" lang=DlgBtnCancel></td></tr> </table> <div id="tab_mfu" style="display:none"></div> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:70px;top:50px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> </body> </html>