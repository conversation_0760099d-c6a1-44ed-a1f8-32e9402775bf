<%@page import="ie.weaf.toolkit.Util"%>
<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@ include file="/common/taglibs.jsp"%>
<%
	String fileTypeExts = Util.nulltostr(request.getParameter("fileTypeExts"));
	 
	if("".equals(fileTypeExts) || "null".equals(fileTypeExts)){
	    fileTypeExts = "*.*";
	}
	String fileTypeDesc = fileTypeExts.replace("*.", "").replace(";", " ").replace("*","");
	String size = Util.nulltostr(request.getParameter("size"));
	if("".equals(size) || "null".equals(size)){
	    size = "0";
	}
	int len = Util.nulltoZero(request.getParameter("len"));
	boolean multi = Boolean.valueOf(request.getParameter("multi"));
	
	String sysId = request.getParameter("sysId");
	String funccode = request.getParameter("funccode");
    String idConnect = request.getParameter("idConnect");
    
    String title = Util.nulltostr(request.getParameter("uploadFileInfo_title"));
    if("".equals(title)){
        title= "文件";
    }
%>
<html>
<head>
<title><%=title %> 上传</title>
<meta http-equiv="Content-Type" content="text/html; charset=GBK" />
<link rel="stylesheet" href="${ctx }/scripts/uploadify/uploadify.css" type="text/css"></link>
<script type="text/javascript" src="${ctx }/scripts/uploadify/jquery.uploadify.min.js"></script>
<script type="text/javascript">
	var obj;
	$(document).ready(function() {
		$('#file_upload').uploadify({
			//设置为true当选择文件后就直接上传了，为false需要点击上传按钮才上传 。
			auto : false,
			//按钮样式
			//buttonClass : null,
			//鼠标指针悬停在按钮上的样子
			//buttonCursor : "hand",
			//浏览按钮的图片的路径 。
			//buttonImage : null,
			//浏览按钮的文本。
			buttonText : "选择文件",
			//文件上传重复性检查程序，检查即将上传的文件在服务器端是否已存在，存在返回1，不存在返回0
			//checkExisting : false,
			//如果设置为true则表示启用SWFUpload的调试模式
			//debug : false,
			//文件上传对象的名称，如果命名为’the_files’，PHP程序可以用$_FILES['the_files']来处理上传的文件对象。
			//fileObjName : "Filedata",
			//上传文件的大小限制 ，如果为整数型则表示以KB为单位的大小，如果是字符串，则可以使用(B, KB, MB, or GB)为单位，比如’2MB’；如果设置为0则表示无限制
			fileSizeLimit : "<%=size%>",
			/*队列最多显示的任务数量，如果选择的文件数量超出此限制，将会出发onSelectError事件。
			注意此项并非最大文件上传数量，如果要限制最大上传文件数量，应设置uploadLimit。
			 */
			queueSizeLimit : <%=len%>,
			//最大上传文件数量，如果达到或超出此限制将会触发onUploadError事件。
			uploadLimit : <%=len%>,
			//设置为true时可以上传多个文件。
			multi : <%=multi%>,
			//这个属性值必须设置fileTypeExts属性后才有效，用来设置选择文件对话框中的提示文本，如设置fileTypeDesc为“请选择rar doc pdf文件”
			fileTypeDesc : "请选择<%=fileTypeDesc%>文件",
			//设置可以选择的文件的类型，格式如：’*.doc;*.pdf;*.rar’ 。
			fileTypeExts : "<%=fileTypeExts%>",
			//JSON格式上传每个文件的同时提交到服务器的额外数据，可在’onUploadStart’事件中使用’settings’方法动态设置。
			//formData : null,
			//设置浏览按钮的高度 ，默认值30
			//height : 30,
			/*用于设置上传队列的HTML模版，可以使用以下标签：
			instanceID – Uploadify实例的ID
			fileID – 列队中此文件的ID,或者理解为此任务的ID
			fileName – 文件的名称
			fileSize – 当前上传文件的大小
			插入模版标签时使用格式如：${fileName}
			 */
			//itemTemplate : false,
			//提交方式Post或Get
			//method : "Post",
			//设置哪些事件可以被重写，JSON格式，如：’overrideEvents’ : ['onUploadProgress']
			overrideEvents :  ['onSelectError','onUploadError','onDialogClose'],
			//如果为true，则每次上传文件时自动加上一串随机字符串参数，防止URL缓存影响上传结果
			preventCaching : true,
			//设置上传进度显示方式，percentage显示上传百分比，speed显示上传速度
			progressData : "percentage",
			//设置上传队列容器DOM元素的ID，如果为false则自动生成一个队列容器。
			queueID : "div1",
			//是否自动将已完成任务从队列中删除，如果设置为false则会一直保留此任务显示。
			removeCompleted : false,
			//如果设置了任务完成后自动从队列中移除，则可以规定从完成到被移除的时间间隔。
			//removeTimeout : 5,
			//如果设置为true，则单个任务上传失败后将返回错误，并重新加入任务队列上传。
			//requeueErrors : false,
			//文件上传成功后服务端应返回成功标志，此项设置返回结果的超时时间
			successTimeout : 0,
			//uploadify.swf 文件的相对路径。
			swf : '${ctx}/scripts/uploadify/uploadify.swf',
			//后台处理程序的相对路径
			uploader : '${ctx}/smbUploadifyFileUploadServlet?sysId=<%=sysId%>&funccode=<%=funccode%>&idConnect=<%=idConnect%>',//先上传到本地，再上传到共享目录，效率低
			//设置文件浏览按钮的宽度。
			//width : 120,

			//当Uploadify初始化过程中检测到当前浏览器不支持flash时触发。
			onFallback : function() {
				mini.alert("您的浏览器不支持flash");
			},
			/*
			文件上传队列处理完毕后触发。
			queueData对象包含如下属性：
			uploadsSuccessful – 上传成功的文件数量
			uploadsErrored – 上传失败的文件数量
			 */
			onQueueComplete : function(queueData) {
				//mini.alert("上传完毕！成功:"
				//		+ queueData.uploadsSuccessful
				//		+ (queueData.uploadsErrored > 0 ? " 失败:"
				//		+ queueData.uploadsErrored : ""));
				
			},
			/*选择文件后向队列中添加每个上传任务时如果失败都会触发。
			file – 文件对象
			errorCode – 错误代码如下：
			QUEUE_LIMIT_EXCEEDED – 任务数量超出队列限制；
			FILE_EXCEEDS_SIZE_LIMIT – 文件大小超出限制；
			ZERO_BYTE_FILE – 文件大小为0
			INVALID_FILETYPE – 文件类型不符合要求
			errorMsg – 错误提示，可通过’this.queueData.errorMsg’定制*/
			onSelectError : function(file, errorCode,
					errorMsg) {
				var msgText = "上传失败！<br />";
				switch (errorCode) {
					case SWFUpload.QUEUE_ERROR.QUEUE_LIMIT_EXCEEDED:
						msgText += "每次最多上传 " + this.settings.queueSizeLimit + "个文件";  
						break;
					case SWFUpload.QUEUE_ERROR.FILE_EXCEEDS_SIZE_LIMIT:
						msgText += "文件["+file.name+"]大小超过限制( " + this.settings.fileSizeLimit + " )"; 
						break;
					case SWFUpload.QUEUE_ERROR.ZERO_BYTE_FILE:
						msgText += "文件["+file.name+"]大小为0";  
						break;
					case SWFUpload.QUEUE_ERROR.INVALID_FILETYPE:
						msgText += "文件["+file.name+"]格式不正确，仅限 " + this.settings.fileTypeExts;  
						break;
					default:
						msgText += "错误代码：" + errorCode + "<br />" + errorMsg;  
				}
				mini.alert(msgText);
			},
			//文件上传出错时触发，参数由服务端程序返回。
			onUploadError : function(file, errorCode, errorMsg, errorString){
				// 手工取消不弹出提示  
		        if (errorCode == SWFUpload.UPLOAD_ERROR.FILE_CANCELLED  
		                || errorCode == SWFUpload.UPLOAD_ERROR.UPLOAD_STOPPED) {  
		            return;  
		        }  
		        var msgText = "上传失败<br />";  
		        switch (errorCode) {  
		            case SWFUpload.UPLOAD_ERROR.HTTP_ERROR:  
		                msgText += "HTTP 错误<br />" + errorMsg;  
		                break;  
		            case SWFUpload.UPLOAD_ERROR.MISSING_UPLOAD_URL:  
		                msgText += "上传文件["+file.name+"]丢失，请重新上传";  
		                break;  
		            case SWFUpload.UPLOAD_ERROR.IO_ERROR:  
		                msgText += "IO错误";  
		                break;  
		            case SWFUpload.UPLOAD_ERROR.SECURITY_ERROR:  
		                msgText += "安全性错误<br />" + errorMsg;  
		                break;  
		            case SWFUpload.UPLOAD_ERROR.UPLOAD_LIMIT_EXCEEDED:  
		                msgText += "每次最多上传 " + this.settings.uploadLimit + "个";  
		                break;  
		            case SWFUpload.UPLOAD_ERROR.UPLOAD_FAILED:  
		                msgText += errorMsg;  
		                break;  
		            case SWFUpload.UPLOAD_ERROR.SPECIFIED_FILE_ID_NOT_FOUND:  
		                msgText += "找不到指定文件["+file.name+"]，请重新操作";  
		                break;  
		            case SWFUpload.UPLOAD_ERROR.FILE_VALIDATION_FAILED:  
		                msgText += "参数错误";  
		                break;  
		            default:  
		                msgText += "文件:" + file.name + "<br />错误码:" + errorCode + "<br />"  
		                        + errorMsg + "<br />" + errorString;  
		        }
		        mini.alert(msgText);
			},
			//选择文件后向队列中添加每个上传任务时都会触发。
			onSelect : function(file){
				
			},
			/*
			当文件浏览框关闭时触发，如果将此事件被重写，则当向队列添加文件上传出错时不会弹出错误消息提示。
			queueData对象包含如下属性：
			filesSelected 文件选择对话框中共选择了多少个文件
			filesQueued 已经向队列中添加了多少个文件
			filesReplaced 已经向队列中替换了多少个文件
			filesCancelled 取消了多少个文件 
			filesErrored 出错了多少个文件
			*/
			onDialogClose : function(queueData){
				
			},
			/*当文件上传成功时触发
				file – 文件对象
				data – 服务端输出返回的信息
				response – 有输出时为true,如果无响应为false，如果返回的是false,当超过successTimeout设置的时间后假定为true
			*/
			onUploadSuccess : function(file, data, response){
				 
				if(response){
					$("#"+file.id).find(".data").html(" - <font color=\"green\">上传完毕，表单保存后生效</font>");
				}else{
					$("#"+file.id).find(".data").html(" - <font color=\"red\">上传失败</font>");
				}
				obj = this.getStats();
			},
			//当文件即将开始上传时立即触发
			onUploadStart : function(file){
				obj = this.getStats();
			},
			//当点击文件队列中文件的关闭按钮或点击取消上传时触发，file参数为被取消上传的文件对象
			onCancel : function(file){
				obj = this.getStats();
			}
			 
		});
	});
	function upload(){
		$('#file_upload').uploadify('upload','*');
	}
	function closeUpload(){
		/* 
		{"upload_errors":0,
			"queue_errors":0,
			"in_progress":1,
			"successful_uploads":0,
			"files_queued":1,
			"upload_cancelled":0}
		 */
		$('#file_upload').uploadify('stop');
		CloseWindow('cancel');
	}
</script>
</head>
<body style="padding: 2px;overflow: hidden;">
	<table cellpadding="4" style="margin-left: 13px;">
		<tr>
			<td><input id="file_upload" name="file_upload" type="file"
				multiple="true"></td>
			<td><a class="mini-button" iconCls="icon-upload"
				onclick="upload()">开始上传</a></td>
			<td><a class="mini-button" iconCls="icon-cancel"
				onclick="closeUpload()">关闭</a></td>
			<!-- 			<td><a -->
			<!-- 				href="javascript:$('#file_upload').uploadify('cancel', '*')">清空队列</a></td> -->
		</tr>
	</table>
	<div id="div1"
		style="border: 1px gray solid; overflow: auto; width: 450px; height: 340px; padding: 0; margin: 0 0 0 19px;"></div>
</body>
</html>
