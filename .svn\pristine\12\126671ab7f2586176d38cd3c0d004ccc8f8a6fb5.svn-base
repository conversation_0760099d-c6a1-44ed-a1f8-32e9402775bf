<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="mywork" extends="bsp-default" namespace="/cn/com/sinosoft/mywork">
		
		<!-- 跳转到办理页面 -->
		<action name="goToDo" class="WorkflowAction" method="goToDo">
			<param name="sessionGroup">workflow</param>
		</action>
	
		<!-- 跳转到我参与的流程详情页面 -->
		<action name="dispatcherView" class="WorkflowAction" method="dispatcherView">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/viewWorkflow.jsp
			</result>
		</action>
		
		<!-- 跳转到我参与的流程列表 -->
		<action name="qryInvolvedWork" class="WorkflowAction" method="qryInvolvedWork">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/qryInvolvedWork.jsp
			</result>
		</action>
		
		<!-- 查询我参与的流程列表数据 -->
		<action name="qryInvWorkList" class="commonQueryAction">
			<param name="sessionGroup">workflow</param>
			<param name="queryCode">QRY_INVWORKFLOW_LIST</param>
			<param name="resultName">qryList</param>
		</action>
		
		<!-- 查询审批历史记录 -->
		<!-- <action name="qryWorkflowList" class="commonQueryAction">
			<param name="sessionGroup">workflow</param>
			<param name="queryCode">QRY_WORKFLOW_LIST</param>
			<param name="resultName">qryList</param>
		</action> -->
		<action name="qryWorkflowList" class="commonQueryAction">
			<param name="sessionGroup">workflow</param>
			<param name="queryCode">QRY_WORKFLOW_LIST2</param>
			<param name="resultName">qryList</param>
		</action> 
		
		<!-- 查询流出已结束的历史记录 -->
		<action name="qryWorkflowEndList" class="commonQueryAction">
			<param name="sessionGroup">workflow</param>
			<param name="queryCode">QRY_WORKFLOW_END_LIST</param>
			<param name="resultName">qryList</param>
		</action>
		
		<!-- 跳转到显示流程图页面上 -->
		<action name="gotoShowPic" class="WorkflowAction" method="gotoShowPic">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/viewTaskPng.jsp
			</result>
		</action>
		
		<!-- 查看历史办理结果 -->
		<action name="gotoHistory" class="WorkflowAction" method="gotoHistory">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/workflowHistory.jsp
			</result>
		</action>
		
		<!--根据任务id 显示流程图 -->
		<action name="showProcessPng" class="WorkflowAction" method="showpic">
			<param name="sessionGroup">workflow</param>
		</action>
		
		<!--根据流程实例id 显示流程图 -->
		<action name="showByPidProcessPng" class="WorkflowAction" method="showpicByPid">
			<param name="sessionGroup">workflow</param>
		</action>
		
		<!-- 审批-->
		<action name="workflowAudit" class="WorkflowAction" method="workflowAudit">
			<param name="sessionGroup">workflow</param>
		</action>
	
		
	<!-- 跳转到审批页面 -->
		<action name="dispatcher" class="WorkflowAction" method="goToDo">
			<param name="sessionGroup">workflow</param>
			<!-- <result name="auditPage">
				/WEB-INF/pages/cn/com/sinosoft/workflow/edtAuditWorkflow.jsp
			</result>
			<result name="correctPage">
			/WEB-INF/pages/cn/com/sinosoft/workflow/edtCorrectWorkflow.jsp
			</result> -->
		</action>
		
		<!-- 跳转到部署流程定义 -->
		<action name="deploymentProcessDefinition" class="WorkflowAction" method="deploymentProcessDefinition">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/qryDeploymentProcessDefinition.jsp
			</result>
		</action>
		<!-- 跳转到上传流程定义页面 -->	
		<action name="uploadProcessDefinition" class="WorkflowAction" method="uploadProcessDefinition">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/uploadProcessDefinition.jsp
			</result>
		</action>
		
		
		<!-- 部署流程定义表单提交 -->
		<action name="deploymentProcessDefinition_submit" class="WorkflowAction" method="deploymentProcessDefinitionSubmit">
			<param name="sessionGroup">workflow</param>
		</action>
		
		<!-- 已部署的流程定义查询 -->
		<action name="qryActReProcdefList" class="commonQueryAction">
			<param name="sessionGroup">workflow</param>
			<param name="queryCode">QRY_ACT_RE_PROCDEF_LIST</param>
			<param name="resultName">qryList</param>
		</action>
		
		<!-- 跳转到查看流程图 -->
		<action name="doProImgDeploymentid" class="WorkflowAction" method="doProImgDeploymentid">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/viewProcessImg.jsp
			</result>
		</action>
		
		<!-- 根据部署id获取流程图 -->
		<action name="getProImgDeploymentid" class="WorkflowAction" method="getProImgDeploymentid">
			<param name="sessionGroup">workflow</param>
		</action>
		
		
		<!-- 删除流程定义 普通删除 -->
		<action name="delProcess" class="WorkflowAction" method="delProcess">
			<param name="sessionGroup">workflow</param>
		</action>
		
		<!-- 删除流程定义 级联删除 -->
		<action name="removeProcess" class="WorkflowAction" method="removeProcess">
			<param name="sessionGroup">workflow</param>
		</action>
		
		<!-- 启动流程实例 根据流程定义ID -->
		<action name="startProcess" class="WorkflowAction" method="startProcess">
			<param name="sessionGroup">workflow</param>
		</action>
		<!-- 启动流程实例 根据流程定义key -->
		<action name="startProcessByKey" class="WorkflowAction" method="startProcessByKey">
			<param name="sessionGroup">workflow</param>
		</action>
		
		
		
		<!-- 跳转到流程实例管理页面 -->
		<action name="gotoProcinst" class="WorkflowAction" method="gotoProcinst">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/qryProcinst.jsp
			</result>
		</action>
		
		<!-- 已部署的流程定义查询 -->
		<action name="qryProcinstList" class="commonQueryAction">
			<param name="sessionGroup">workflow</param>
			<param name="queryCode">QRY_ACT_RU_EXECUTION_LIST</param>
			<param name="resultName">qryList</param>
		</action>
		
		<!-- 删除流程实例-->
		<action name="delProcinst" class="WorkflowAction" method="delProcinst">
			<param name="sessionGroup">workflow</param>
		</action>
		
		<!-- 跳转到任务列表-->
		<action name="doTaskList" class="WorkflowAction" method="doTaskList">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/qryMytasklist.jsp
			</result>
		</action>
		
		<!-- 根据流程实例查询历史任务 -->
		<action name="taskList" class="commonQueryAction">
			<param name="sessionGroup">workflow</param>
			<param name="queryCode">QRY_ACT_HI_ACTINST_LIST</param>
			<param name="resultName">qryList</param>
		</action>
		
		<!-- 跳转到任务列表-->
		<action name="doMytasklist" class="WorkflowAction" method="doMytasklist">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/qryMytasklist.jsp
			</result>
		</action>
		
		<!-- 跳转到已经办理-->
		<action name="myAlreadyDo" class="WorkflowAction" method="myAlreadyDo">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/qryMyAlreadyDo.jsp
			</result>
		</action>
		
		<!-- 跳转到我的申请列表-->
		<action name="myApply" class="WorkflowAction" method="myApply">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/qryMyApply.jsp
			</result>
		</action>
		
		<!-- 查询我的待办任务 -->
		<action name="mytasklist" class="commonQueryAction">
			<param name="sessionGroup">workflow</param>
			<param name="queryCode">QRY_ACT_RU_TASK_LIST</param>
			<param name="resultName">qryList</param>
		</action>
		
		<!-- 查询我的申请 -->
		<action name="qryMyApply" class="commonQueryAction">
			<param name="sessionGroup">workflow</param>
			<param name="queryCode">QRY_MY_APPLY</param>
			<param name="resultName">qryList</param>
		</action>
		
		<!-- 查询我的已办 -->
		<action name="qryMyAlreadyDo" class="commonQueryAction">
			<param name="sessionGroup">workflow</param>
			<param name="queryCode">QRY_MY_ALREADY_DO</param>
			<param name="resultName">qryList</param>
		</action>
		
		<!-- 我的待办任务数-->
		<action name="getTasklistCount" class="WorkflowAction" method="getTasklistCount">
			<param name="sessionGroup">workflow</param>
		</action>
		
		<!-- 当前任务的连线驳回节点-->
		<action name="seqBackTask" class="WorkflowAction" method="seqBackTask">
			<param name="sessionGroup">workflow</param>
		</action>
		
		<!-- 测试订正页面 （可删除） -->
		<action name="testCorrect" class="WorkflowAction" method="testCorrect">
			<param name="sessionGroup">workflow</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/workflow/testCorrect.jsp
			</result>
		</action>
		<!-- 测试订正页面保存提交（可删除）-->
		<action name="testCorrectSubmit" class="WorkflowAction" method="testCorrectSubmit">
			<param name="sessionGroup">workflow</param>
		</action>
	 
		<!-- 跳转进入已办页面 -->
		<action name="toToHand" class="WorkflowAction" method="toToHand">
			<param name="sessionGroup">workflow</param>
		</action>
	</package>
</struts>