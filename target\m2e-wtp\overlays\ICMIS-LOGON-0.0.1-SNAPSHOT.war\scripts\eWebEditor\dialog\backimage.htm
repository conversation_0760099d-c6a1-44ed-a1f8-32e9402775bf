<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj="";var ak="";var D;var aE="";var cz="";var cE="";var cX="sys";var bz=((parseFloat(config.AllowImageSize)>0)?true:false);if(bV['action']=="other"){aj="OTHER";ak=lang["DlgComSet"];aE=ck.$("d_image").value;cz=ck.$("d_repeat").value;cE=ck.$("d_attachment").value;cX="url";}else{aj="INSERT";ak=lang["DlgComBody"];if(I.ay()=="Control"){D=I.aX();}else{D=I.dm();}D=wa(D);if(D){switch(D.tagName){case "TD":ak=lang["DlgComTableCell"];break;case "TR":case "TH":ak=lang["DlgComTableRow"];break;default:ak=lang["DlgComTable"];break;}aj="MODI";aE=D.style.backgroundImage;cz=D.style.backgroundRepeat;cE=D.style.backgroundAttachment;cX="url";aE=aE.replace(/\"/gi,"");aE=aE.substr(4,aE.length-5);}}var Q=lang["DlgBkImg"]+"("+ak+")";document.write("<title>"+Q+"</title>");function ah(){lang.TranslatePage(document);bb($("d_repeat"),cz.toLowerCase());bb($("d_attachment"),cE.toLowerCase());if(aE){for(var i=0;i<$("d_fromsys").options.length;i++){if(fH("sysimage/bg/"+$("d_fromsys").options[i].value)==aE){aE="";$("d_fromsys").selectedIndex=i;cX="sys";break;}}}$("d_fromurl").value=aE;if((!bz)&&(cX=="file")){cX="sys";}xM(cX);parent.bp(Q);};function wa(H){while(H!=null&&H.tagName!="TD"&&H.tagName!="TR"&&H.tagName!="TH"&&H.tagName!="TABLE"){H=H.parentNode;}return H;};function xM(an){switch(an){case "url":$("d_checkfromurl").checked=true;$("d_checkfromsys").checked=false;$("d_checkcancel").checked=false;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_fromsys").disabled=true;if(bz){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}break;case "file":$("d_checkfromurl").checked=false;$("d_checkfromsys").checked=false;$("d_checkcancel").checked=false;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_fromsys").disabled=true;if(bz){$("d_checkfromfile").checked=true;$("uploadfile").disabled=false;}break;case "sys":$("d_checkfromurl").checked=false;$("d_checkfromsys").checked=true;$("d_checkcancel").checked=false;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_fromsys").disabled=false;if(bz){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}break;case "cancel":$("d_checkfromurl").checked=false;$("d_checkfromsys").checked=false;$("d_checkcancel").checked=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_fromsys").disabled=true;if(bz){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}break;}};function UploadError(an){eW();xM('file');divProcessing.style.display="none";try{cv($("uploadfile"),jF(an,config.AllowImageExt,config.AllowImageSize));}catch(e){}};function UploadSaved(fw){$("d_fromurl").value=fw;cH();};function cH(){if($("d_fromurl").value==""){aE="";cz="";cE="";}else{if(aj=="OTHER"){aE=$("d_fromurl").value;}else{aE="url("+$("d_fromurl").value+")";}cz=$("d_repeat").options[$("d_repeat").selectedIndex].value;cE=$("d_attachment").options[$("d_attachment").selectedIndex].value;}switch(aj){case "MODI":D.style.backgroundImage=aE;D.style.backgroundRepeat=cz;D.style.backgroundAttachment=cE;break;case "OTHER":ck.$("d_image").value=aE;ck.$("d_repeat").value=cz;ck.$("d_attachment").value=cE;break;default:EWIN.setHTML("<table border=0 cellpadding=0 cellspacing=0 width='100%' height='100%'><tr><td valign=top id='eWebEditor_TempElement_Background'>"+EWIN.getHTML()+"</td></tr></table>",true);var fY=A.F.getElementById("eWebEditor_TempElement_Background");fY.style.backgroundImage=aE;fY.style.backgroundRepeat=cz;fY.style.backgroundAttachment=cE;fY.removeAttribute("id");break;}parent.aT();};function ok(){if($("d_checkfromurl").checked){cH();return;}if(bz){if($("d_checkfromfile").checked){if(!hw($("uploadfile").value,config.AllowImageExt)){UploadError("ext");return false;}fD();divProcessing.style.display="";document.myuploadform.submit();return;}}if($("d_checkfromsys").checked){$("d_fromurl").value=fH("sysimage/bg/"+$("d_fromsys").options[$("d_fromsys").selectedIndex].value);cH();return;}if($("d_checkcancel").checked){$("d_fromurl").value="";cH();return;}};function fD(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_checkfromsys").disabled=true;$("d_checkcancel").disabled=true;$("d_fromurl").disabled=true;$("d_fromsys").disabled=true;$("d_repeat").disabled=true;$("d_attachment").disabled=true;$("d_ok").disabled=true;};function eW(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_checkfromsys").disabled=false;$("d_checkcancel").disabled=false;$("d_fromurl").disabled=false;$("d_fromsys").disabled=false;$("d_repeat").disabled=false;$("d_attachment").disabled=false;$("d_ok").disabled=false;}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgBkImgSource></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript">if(bz){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"xM('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\" colspan=2>");document.write(jL("image"));document.write("</td>");document.write("</tr>");}</script> <tr> <td width="20%" noWrap><input type=radio id="d_checkfromurl" onclick="xM('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td width="80%" noWrap colspan=2> <script type="text/javascript">if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:100%' size=30 value=''></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"xQ('image','fromurl')\" value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:100%' size=30 value=''>");}</script> </td> </tr> <tr> <td width="20%" noWrap><input type=radio id="d_checkfromsys" onclick="xM('sys')"><label for=d_checkfromsys><span lang=DlgFromSys></span></label>:</td> <td noWrap> <select id="d_fromsys" size=1 style="width:80px"> <option value="snow.gif" lang=DlgBkImgSnow selected></option> <option value="nature.jpg" lang=DlgBkImgNature></option> <option value="clear.jpg" lang=DlgBkImgClear></option> <option value="glacier.jpg" lang=DlgBkImgGlacier></option> <option value="fiesta.jpg" lang=DlgBkImgFiesta></option> <option value="birthday.gif" lang=DlgBkImgBirthday></option> <option value="citrus.gif" lang=DlgBkImgCitrus></option> <option value="hearts.gif" lang=DlgBkImgHearts></option> <option value="flower.gif" lang=DlgBkImgFlower></option> <option value="gathering.jpg" lang=DlgBkImgGathering></option> <option value="christmas.gif" lang=DlgBkImgChristmas></option> <option value="ivy.gif" lang=DlgBkImgIvy></option> <option value="tech.gif" lang=DlgBkImgTech></option> <option value="maize.jpg" lang=DlgBkImgMaize></option> <option value="grid.gif" lang=DlgBkImgGrid></option> </select> </td> <td noWrap><input type=radio id="d_checkcancel" onclick="xM('cancel')"><label for=d_checkcancel><span lang=DlgBkImgCancelBg></span></label> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgBkImgEffect></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td width="20%" noWrap><span lang=DlgBkImgRepeat></span>:</td> <td width="29%" noWrap> <select id=d_repeat size=1 style="width:80px"> <option value='' lang=DlgComDefault selected></option> <option value='repeat' lang=DlgBkImgRepeatR></option> <option value='no-repeat' lang=DlgBkImgRepeatNo></option> <option value='repeat-x' lang=DlgBkImgRepeatX></option> <option value='repeat-y' lang=DlgBkImgRepeatY></option> </select> </td> <td width="2%">&nbsp;</td> <td width="20%" noWrap><span lang=DlgBkImgAttach></span>:</td> <td width="29%" noWrap> <select id=d_attachment size=1 style="width:80px"> <option value='' lang=DlgComDefault selected></option> <option value='scroll' lang=DlgBkImgAttachScroll></option> <option value='fixed' lang=DlgBkImgAttachFixed></option> </select> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="d_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:70px;top:45px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> </body> </html>