<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ=cc['action'];var aa=lang["DlgComInsert"];var oRange;var gX;var af;var ds="http://";var nu="http://";var it="";switch(aJ){case "other":ds=dP.gv.Href;it=dP.gv.Target;nu=wW(ds);break;default:af=C.cI();var el=na(af,"A");if(el){af=el;}if(af.tagName.toUpperCase()=="A"){aJ="MODI";aa=lang["DlgComModify"];it=dE(af,"target");ds=vH(af,"href");nu=wW(ds);}break;}var bm=lang["DlgHylnk"]+"("+aa+")";document.write("<title>"+bm+"</title>");function na(obj,iI){while(obj!=null&&obj.tagName!=iI){obj=obj.parentNode;if(!obj.tagName){obj=null;break;}}return obj;};function wW(aG){var re=/(.+:\/*)(.*)/gi;return aG.replace(re,"$1");};function xG(dO){nu=$("d_protocol").options[dO].value;ds=$("d_url").value;var re=/(.+:\/*)/gi;ds=ds.replace(re,"");$("d_url").value=nu+ds;};function aq(){lang.ag(document);aC($("d_protocol"),nu.toLowerCase());aC($("d_target"),it.toLowerCase());zO();$("d_url").value=ds;parent.ar(bm);};function zO(){$("d_anchor").options.length=1;var qb=EWEB.T.body.getElementsByTagName("A");for(i=0;i<qb.length;i++){if(qb[i].href.toUpperCase()==""){$("d_anchor").options[$("d_anchor").options.length]=new Option(qb[i].name,"#"+qb[i].name);}}};function ok(){ds=$("d_url").value;nu=$("d_protocol").options[$("d_protocol").selectedIndex].value;it=$("d_target").options[$("d_target").selectedIndex].value;if(ds==""){alert(lang["DlgHylnkNoUrl"]);$("d_url").focus();return;}switch(aJ){case "other":dP.gv.Href=ds;dP.gv.Target=it;dP.zR();break;case "MODI":var s_in=af.innerHTML;bq(af,"href",ds);af.innerHTML=s_in;nP(af,"href",ds);bq(af,"target",it);break;default:var wq='javascript:void(0);/*'+(new Date().getTime())+'*/';C.Restore();EWEB.T.execCommand('CreateLink',false,wq);var nn=EWEB.T.links;for(i=0;i<nn.length;i++){var gk=nn[i];if(dE(gk,'href')==wq){bq(gk,"href",ds);nP(gk,"href",ds);bq(gk,"target",it);}}}parent.bV();} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgHylnkLegend></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgHylnkProtocol></span>:</td> <td noWrap width="29%"> <select id=d_protocol onchange="xG(this.selectedIndex)" size=1 style="width:80px"> <option value='' lang=DlgComOther></option> <option value='file://'>file:</option> <option value='ftp://'>ftp:</option> <option value='gopher://'>gopher:</option> <option value='http://'>http:</option> <option value='https://'>https:</option> <option value='mailto:'>mailto:</option> <option value='news:'>news:</option> <option value='telnet:'>telnet:</option> <option value='wais:'>wais:</option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgHylnkTarget></span>:</td> <td noWrap width="29%"> <select id=d_target size=1 style="width:80px"> <option value='' lang=DlgHylnkTargetNone></option> <option value='_self' lang=DlgHylnkTargetSelf></option> <option value='_top' lang=DlgHylnkTargetTop></option> <option value='_blank' lang=DlgHylnkTargetBlank></option> <option value='_parent' lang=DlgHylnkTargetParent></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgHylnkUrl></span>:</td> <td noWrap width="80%" colspan=4><input type=text id=d_url size=10 value="" style="width:100%"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgHylnkAnchor></span>:</td> <td noWrap width="80%" colspan=4> <select id=d_anchor onchange="d_url.value=this.options[this.selectedIndex].value" size=1 style="width:100%"> <option value='' lang=DlgHylnkAnchorNone></option> </select> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="d_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.bn()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html> 