<?xml version="1.0" encoding="UTF-8"?>
<configuration name="系统初始化配置">
	<id name="系统标识">PM</id>
	<baseId name="基础平台系统标识">PM</baseId>
	<showKeyBtn name="登陆页是否显示[证书登陆]按钮，false的话显示[重置]按钮">false</showKeyBtn>
	<pwdEncrypt name="密码是否加密">true</pwdEncrypt>
	<pwdLevel name="密码强度(low/mid/high)">low</pwdLevel>
	<pwdDefault name="用户初始密码">88888888</pwdDefault>
	<captchaLevel name="验证码强度(low/mid/high/none)">none</captchaLevel>
	<logonFailureRangeInSeconds name="登录错误时间区间（秒）">180</logonFailureRangeInSeconds>
	<logonFailureThreshold name="登录错误次数">4</logonFailureThreshold>
	<footContent name="页脚显示">芜湖市疾病预防控制信息系统 技术支持：82661191-860 系统问题QQ交流群：316402475</footContent>
	<processServiceUrl name="工作流服务地址">http://192.168.100.120:8081/activiti-rest/service</processServiceUrl>
	<processServiceUser name="工作流服务地址访问账户">a2VybWl0OjExMTExMQ==</processServiceUser>
	<mqUrl name="MQ服务地址">failover:(tcp://192.168.100.151:8162)?initialReconnectDelay=1000</mqUrl>
	<gisType name="地图设置(目前只支持天地图)">tianditu</gisType>
	<smbMachine name="文件共享服务器地址">smb://administrator:yjzh-code@192.168.100.133/whjkFile</smbMachine>
	<!-- 邮箱配置 -->
	<mailSSL name="邮件是否采用ssl(只处理SSL的连接, 对于非SSL的连接不做处理)">true</mailSSL>
	<mailUser name="管理员邮箱"><EMAIL></mailUser>
	<mailPwd name="管理员邮箱密码">a53453</mailPwd>
	<mailSmtp name="邮箱类型">smtp.qq.com</mailSmtp>
	<!-- memcached缓存配置，服务端配置在class/config.properties-->
	<memcached_code_start name="数据字典是否启用缓存">false</memcached_code_start>
	<memcached_zone_start name="地区编码是否启用缓存">false</memcached_zone_start>
	
	<indexRightPage name="首页右侧欢迎页地址"></indexRightPage>
	
	<bjca.enable name="启用BJCA">false</bjca.enable>
	<bjca.webappName name="bjca应用服务名">SVSDefault</bjca.webappName>
	<bjca.profilePath name="bjca配置文件路径">C:\\BJCAROOT</bjca.profilePath>
</configuration>