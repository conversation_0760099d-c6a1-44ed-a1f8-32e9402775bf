<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title></title>
<script type="text/javascript">
	//解决iframe加载跨域链接时在ie下打不开的问题
	window.onload = function() {
		location.href = getRootPath()
				+ decodeURIComponent(location.hash.substring(1));
	}

	function getQueryString(name) {
		var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
		var r = window.location.search.substr(1).match(reg);
		if (r != null) {
			return unescape(r[2]);
		}
		return null;
	}
	//获取项目地址
	function getRootPath() {
		//获取当前网址，如： http://localhost:8083/proj/meun.jsp   
		var curWwwPath = window.document.location.href;
		//获取主机地址之后的目录，如： proj/meun.jsp   
		var pathName = window.document.location.pathname;
		var pos = curWwwPath.indexOf(pathName);
		//获取主机地址，如： http://localhost:8083   
		var localhostPath = curWwwPath.substring(0, pos);
		//获取带"/"的项目名，如：/proj   
		var projectName = pathName.substring(0,
				pathName.substr(1).indexOf('/') + 1);
		return (localhostPath + projectName);
	}
</script>
</head>
<body>
</body>
</html>
