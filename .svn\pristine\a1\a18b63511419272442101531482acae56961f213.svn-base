<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/test">
  <process id="sealUserSbApply" name="公章使用申请" isExecutable="true">
    <userTask id="usertask1" name="公章使用申请订正" activiti:candidateGroups="OS100000102">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapply/sealUseApply_auditeitParentInput.ac" default="1&amp;pageState=edit"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <userTask id="ksfzrshnextzg" name="第一步：科室负责人" activiti:candidateGroups="OS100000104">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapply/sealUseApply_auditParentInput.ac" datePattern="1&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow4" sourceRef="usertask1" targetRef="ksfzrshnextzg"></sequenceFlow>
    <userTask id="usertask4" name="第二步：主管部门审批" activiti:candidateGroups="OS100000105">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapply/sealUseApply_auditParentInput.ac"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <userTask id="usertask6" name="第三步：所长审批" activiti:candidateGroups="OS100000106">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/os/sealuseapply/sealUseApply_auditParentInput.ac" default="1"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="exclusivegateway6" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow18" sourceRef="ksfzrshnextzg" targetRef="exclusivegateway6"></sequenceFlow>
    <sequenceFlow id="flow19" name="通过" sourceRef="exclusivegateway6" targetRef="usertask4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <startEvent id="startevent1" name="Start"></startEvent>
    <exclusiveGateway id="exclusivegateway7" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow20" sourceRef="usertask6" targetRef="exclusivegateway7"></sequenceFlow>
    <endEvent id="endevent1" name="End"></endEvent>
    <sequenceFlow id="flow21" name="通过" sourceRef="exclusivegateway7" targetRef="endevent1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="exclusivegateway8" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow22" sourceRef="usertask4" targetRef="exclusivegateway8"></sequenceFlow>
    <sequenceFlow id="flow23" name="通过" sourceRef="exclusivegateway8" targetRef="usertask6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow24" name="驳回" sourceRef="exclusivegateway6" targetRef="usertask1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow25" sourceRef="startevent1" targetRef="ksfzrshnextzg"></sequenceFlow>
    <sequenceFlow id="flow26" name="驳回" sourceRef="exclusivegateway8" targetRef="usertask1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow27" name="驳回" sourceRef="exclusivegateway7" targetRef="usertask1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='dz'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_sealUserSbApply">
    <bpmndi:BPMNPlane bpmnElement="sealUserSbApply" id="BPMNPlane_sealUserSbApply">
      <bpmndi:BPMNShape bpmnElement="usertask1" id="BPMNShape_usertask1">
        <omgdc:Bounds height="55.0" width="121.0" x="150.0" y="198.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ksfzrshnextzg" id="BPMNShape_ksfzrshnextzg">
        <omgdc:Bounds height="55.0" width="121.0" x="150.0" y="91.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask4" id="BPMNShape_usertask4">
        <omgdc:Bounds height="55.0" width="142.0" x="410.0" y="91.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="usertask6" id="BPMNShape_usertask6">
        <omgdc:Bounds height="55.0" width="127.0" x="660.0" y="91.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway6" id="BPMNShape_exclusivegateway6">
        <omgdc:Bounds height="40.0" width="40.0" x="316.0" y="98.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="startevent1" id="BPMNShape_startevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="80.0" y="101.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway7" id="BPMNShape_exclusivegateway7">
        <omgdc:Bounds height="40.0" width="40.0" x="832.0" y="98.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endevent1" id="BPMNShape_endevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="917.0" y="101.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway8" id="BPMNShape_exclusivegateway8">
        <omgdc:Bounds height="40.0" width="40.0" x="580.0" y="98.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow4" id="BPMNEdge_flow4">
        <omgdi:waypoint x="210.0" y="198.0"></omgdi:waypoint>
        <omgdi:waypoint x="210.0" y="146.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow18" id="BPMNEdge_flow18">
        <omgdi:waypoint x="271.0" y="118.0"></omgdi:waypoint>
        <omgdi:waypoint x="316.0" y="118.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow19" id="BPMNEdge_flow19">
        <omgdi:waypoint x="356.0" y="118.0"></omgdi:waypoint>
        <omgdi:waypoint x="410.0" y="118.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="369.0" y="101.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow20" id="BPMNEdge_flow20">
        <omgdi:waypoint x="787.0" y="118.0"></omgdi:waypoint>
        <omgdi:waypoint x="832.0" y="118.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow21" id="BPMNEdge_flow21">
        <omgdi:waypoint x="872.0" y="118.0"></omgdi:waypoint>
        <omgdi:waypoint x="917.0" y="118.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="879.0" y="101.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow22" id="BPMNEdge_flow22">
        <omgdi:waypoint x="552.0" y="118.0"></omgdi:waypoint>
        <omgdi:waypoint x="580.0" y="118.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow23" id="BPMNEdge_flow23">
        <omgdi:waypoint x="620.0" y="118.0"></omgdi:waypoint>
        <omgdi:waypoint x="660.0" y="118.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="619.0" y="101.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow24" id="BPMNEdge_flow24">
        <omgdi:waypoint x="336.0" y="138.0"></omgdi:waypoint>
        <omgdi:waypoint x="336.0" y="225.0"></omgdi:waypoint>
        <omgdi:waypoint x="271.0" y="225.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="340.0" y="169.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow25" id="BPMNEdge_flow25">
        <omgdi:waypoint x="115.0" y="118.0"></omgdi:waypoint>
        <omgdi:waypoint x="150.0" y="118.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow26" id="BPMNEdge_flow26">
        <omgdi:waypoint x="600.0" y="138.0"></omgdi:waypoint>
        <omgdi:waypoint x="599.0" y="225.0"></omgdi:waypoint>
        <omgdi:waypoint x="271.0" y="225.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="610.0" y="169.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow27" id="BPMNEdge_flow27">
        <omgdi:waypoint x="852.0" y="138.0"></omgdi:waypoint>
        <omgdi:waypoint x="851.0" y="225.0"></omgdi:waypoint>
        <omgdi:waypoint x="271.0" y="225.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="14.0" width="24.0" x="861.0" y="169.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>