<!doctype html>
<html>
    <head> 
        <title>FlexPaper AdaptiveUI Debug Page</title>                
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="initial-scale=1,user-scalable=no,maximum-scale=1,width=device-width" />
        <style type="text/css" media="screen"> 
			html, body	{ height:100%; }
			body { margin:0; padding:0; overflow:auto; }   
			#flashContent { display:none; }
        </style> 
		
		<link rel="stylesheet" type="text/css" href="css/flexpaper.css" />
		<script type="text/javascript" src="js/jquery.min.js"></script>
		<script type="text/javascript" src="js/jquery.extensions.min.js"></script>
		<script type="text/javascript" src="js/flexpaper.js"></script>
		<script type="text/javascript" src="js/flexpaper_handlers_debug.js"></script>			
    </head> 
    <body>
    <div id="documentViewer" class="flexpaper_viewer" style="position:absolute;left:10px;top:10px;width:770px;height:500px"></div>

        <script type="text/javascript">
        function getDocumentUrl(document){
            return "php/services/view.php?doc={doc}&format={format}&page={page}".replace("{doc}",document);
        }

        var startDocument = "Paper";

        $('#documentViewer').FlexPaperViewer(
                { config : {

                    SWFFile : 'docs/Paper.pdf.swf',

                    Scale : 0.6,
                    ZoomTransition : 'easeOut',
                    ZoomTime : 0.5,
                    ZoomInterval : 0.2,
                    FitPageOnLoad : true,
                    FitWidthOnLoad : false,
                    FullScreenAsMaxWindow : false,
                    ProgressiveLoading : false,
                    MinZoomSize : 0.2,
                    MaxZoomSize : 5,
                    SearchMatchAll : false,

                    RenderingOrder : 'flash',

                    ViewModeToolsVisible : true,
                    ZoomToolsVisible : true,
                    NavToolsVisible : true,
                    CursorToolsVisible : true,
                    SearchToolsVisible : true,
                    WMode : 'window',
                    localeChain: 'en_US'
                }}
        );
        </script>

        <div style="position:absolute;left:790px;top:10px;font-family:Verdana;font-size:9pt;background-color:#CACACA;">
        <div style="font-size:15px;font-weight:bold;text-align:center;margin-top:10px;margin-bottom:10px;">FlexPaper Interactive API</div>
		<div style="padding: 5px 5px 5px 5px;background-color:#EFEFEF">
        <table border="0" width="360">
	        <tr><th colspan=3>Operations</th></tr>
	        <tr><td>load</td><td><input type=text style="width:150px;" id="txt_doc" value="Paper"></td><td><input type=submit value="Invoke" onclick="$FlexPaper('documentViewer').load({SWFFile : 'docs/'+$('#txt_doc').val()+'.pdf.swf',IMGFiles : 'docs/'+$('#txt_doc').val()+'.pdf_{page}.png',JSONFile : 'docs/'+$('#txt_doc').val()+'.pdf.js',PDFFile : 'pdf/'+$('#txt_doc').val()+'.pdf'})"></td></tr>
	        <tr><td>fitWidth</td><td></td><td><input type=submit value="Invoke" onclick="$FlexPaper('documentViewer').fitWidth()"></td></tr>
	        <tr><td>fitHeight</td><td></td><td><input type=submit value="Invoke" onclick="$FlexPaper('documentViewer').fitHeight()"></td></tr>
	        <tr><td>gotoPage</td><td><input type=text style="width:150px;" id="txt_pagenum" value="3"></td><td><input type=submit value="Invoke" onclick="$FlexPaper('documentViewer').gotoPage($('#txt_pagenum').val())"></td></tr>
	        <tr><td>getCurrPage</td><td></td><td><input type=submit value="Invoke" onclick="alert('Current page:' + $FlexPaper('documentViewer').getCurrPage())"></td></tr>
	        <tr><td>getTotalPages</td><td></td><td><input type=submit value="Invoke" onclick="alert('Total pages:' + $FlexPaper('documentViewer').getTotalPages())"></td></tr>
	        <tr><td>nextPage</td><td></td><td><input type=submit value="Invoke" onclick="$FlexPaper('documentViewer').nextPage()"></td></tr>
	        <tr><td>prevPage</td><td></td><td><input type=submit value="Invoke" onclick="$FlexPaper('documentViewer').prevPage()"></td></tr>
	        <tr><td>setZoom</td><td><input type=text style="width:150px;" id="txt_zoomfactor" value="1.30"></td><td><input type=submit value="Invoke" onclick="$FlexPaper('documentViewer').setZoom($('#txt_zoomfactor').val())"></td></tr>
	        <tr><td>searchText</td><td><input type=text style="width:150px;" id="txt_searchtext" value="text"></td><td><input type=submit value="Invoke" onclick="$FlexPaper('documentViewer').searchText($('#txt_searchtext').val())"></td></tr>
	        <tr><td>switchMode</td><td><input type=text style="width:150px;" id="txt_viewmode" value="Tile"></td><td><input type=submit value="Invoke" onclick="$FlexPaper('documentViewer').switchMode($('#txt_viewmode').val())"></td></tr>
	        <tr><td>printPaper</td><td></td><td><input type=submit value="Invoke" onclick="$FlexPaper('documentViewer').printPaper()"></td></tr>
        </table>
        <br/><br/>
        <table border="0" width="230">
        	<tr><th>Event Log</th></tr>
        	<tr><td><textarea rows=6 cols=28 id="txt_eventlog" style="width:350px;font-size:9px;" wrap="off"></textarea></td></tr>
        	<tr><td><input type=text style="width:350px;" id="txt_progress" value=""></td></tr>
	    </table>
        </div>
	</div>

    <script type="text/javascript">
        var url = window.location.href.toString();

        if(location.length==0){
            url = document.URL.toString();
        }

        if(url.indexOf("file:")>=0){
            jQuery('#documentViewer').html("<div style='position:relative;background-color:#ffffff;width:420px;font-family:Verdana;font-size:10pt;left:22%;top:20%;padding: 10px 10px 10px 10px;border-style:solid;border-width:5px;'><img src='http://flexpaper.devaldi.com/resources/warning_icon.gif'>&nbsp;<b>You are trying to use FlexPaper from a local directory.</b><br/><br/> FlexPaper needs to be copied to a web server before the viewer can display its document properlty.<br/><br/>Please copy the FlexPaper files to a web server and access the viewer through a http:// url.</div>");
        }
    </script>
   </body> 
</html> 