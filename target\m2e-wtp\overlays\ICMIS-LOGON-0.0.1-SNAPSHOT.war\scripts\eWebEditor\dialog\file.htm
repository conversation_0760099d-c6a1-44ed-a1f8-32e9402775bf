<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var Q=lang["DlgFile"];document.write("<title>"+Q+"</title>");var bz=((parseFloat(config.AllowFileSize)>0)?true:false);var vJ,vM;function xM(dc){if(dc=="url"){$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_checkfromurl").checked=true;if(bz){$("d_checkfromfile").checked=false;$("uploadfile").disabled=true;}}else{$("d_checkfromurl").checked=false;$("uploadfile").disabled=false;$("d_checkfromfile").checked=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}}};function UploadError(an){eW();xM('file');$("divProcessing").style.display="none";try{cv($("uploadfile"),jF(an,config.AllowFileExt,config.AllowFileSize));}catch(e){}};function UploadSaved(mO){$("d_fromurl").value=mO;cH();};function cH(){var url=$("d_fromurl").value;var vm=ha(url);var vy=fH("sysimage/icon16/"+vm);EWIN.insertHTML("<img border=0 src='"+vy+"'><a href='"+url+"' target=_blank>"+$("d_filename").value+"</a>");parent.aT();};function ok(){if($("d_checkfromurl").checked){lM($("d_fromurl").value,"/");cH();}else{if(!hw($("uploadfile").value,config.AllowFileExt)){UploadError("ext");return false;}lM($("uploadfile").value,"\\");fD();$("divProcessing").style.display="";document.myuploadform.submit();}};function fD(){$("d_checkfromfile").disabled=true;$("d_checkfromurl").disabled=true;$("d_fromurl").disabled=true;if(config.AllowBrowse=="1"){$("d_browse").disabled=true;}$("d_ok").disabled=true;};function eW(){$("d_checkfromfile").disabled=false;$("d_checkfromurl").disabled=false;$("d_fromurl").disabled=false;if(config.AllowBrowse=="1"){$("d_browse").disabled=false;}$("d_ok").disabled=false;};function ha(url){var gl;gl=url.substr(url.lastIndexOf(".")+1);gl=gl.toUpperCase();var ar;switch(gl){case "TXT":ar="txt.gif";break;case "CHM":case "HLP":ar="hlp.gif";break;case "DOC":case "DOCX":ar="doc.gif";break;case "PDF":ar="pdf.gif";break;case "MDB":ar="mdb.gif";break;case "GIF":ar="gif.gif";break;case "JPG":ar="jpg.gif";break;case "BMP":ar="bmp.gif";break;case "PNG":ar="png.gif";break;case "ASP":case "JSP":case "JS":case "PHP":case "PHP3":case "ASPX":ar="code.gif";break;case "HTM":case "HTML":case "SHTML":ar="htm.gif";break;case "ZIP":ar="zip.gif";break;case "RAR":ar="rar.gif";break;case "EXE":ar="exe.gif";break;case "AVI":ar="avi.gif";break;case "MPG":case "MPEG":case "ASF":ar="mp.gif";break;case "RA":case "RM":ar="rm.gif";break;case "MP3":ar="mp3.gif";break;case "MID":case "MIDI":ar="mid.gif";break;case "WAV":ar="audio.gif";break;case "XLS":ar="xls.gif";break;case "PPT":case "PPS":ar="ppt.gif";break;case "SWF":ar="swf.gif";break;default:ar="unknow.gif";break;}return ar;};function lM(url,kM){$("d_filename").value=url.substr(url.lastIndexOf(kM)+1);};function ah(){lang.TranslatePage(document);if(!bz){xM("url");}else{xM("file");}vJ=$("tab_normal").offsetWidth;vM=$("tab_normal").offsetHeight;parent.bp(Q);};function js(bG,hp,co){if(co=="tab_mfu"){DLGMFU.lY("file",$(co),"500px","250px");}if(DLGTab.gd[bG]){parent.mR(DLGTab.gd[bG].Width,DLGTab.gd[bG].Height);}else{parent.mR();}}</script> <script event="OnCancel(an)" for="eWebEditorMFU">

if(an==""){parent.aP();}</script> <script event="OnUpload(an, aa)" for="eWebEditorMFU">

if(an=="endall"||an=="endapart"){var f="";var dY=aa.split("|");for(var i=0;i<dY.length;i++){var a=dY[i].split("::");if(a.length==3&&a[1]!=""){var bO=a[0].substr(a[0].lastIndexOf("\\")+1);var fq=a[1];var lL=ha(fq);lL=fH('sysimage/icon16/'+lL);f+='<img border=0 src="'+lL+'"><a href="'+fq+'" target="_blank">'+bO+'</a><br>';EWIN.addUploadFile(bO,fq);}}EWIN.insertHTML(f);parent.aT();}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <script type="text/javascript">

if(config.MFUEnable=="1"){DLGTab.jB([[lang["DlgComTabNormal"],"tab_normal"],[lang["DlgComTabMFU"],"tab_mfu"]]);}</script> <table id="tab_normal" border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgFileSource></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=0 width="300px"><tr><td></td></tr></table> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <script type="text/javascript">if(bz){document.write("<tr>");document.write("<td noWrap width=\"20%\"><input type=radio id=\"d_checkfromfile\" value=\"1\" onclick=\"xM('file')\"><label for=d_checkfromfile>"+lang["DlgFromFile"]+"</label>:</td>");document.write("<td noWrap width=\"80%\">");document.write(jL("file"));document.write("</td>");document.write("</tr>");}</script> <tr> <td noWrap width="20%"><input type=radio id="d_checkfromurl" value="1" onclick="xM('url')"><label for=d_checkfromurl><span lang=DlgFromUrl></span></label>:</td> <td noWrap width="80%"> <script type="text/javascript">if(config.AllowBrowse=="1"){document.write("<table border=0 cellpadding=0 cellspacing=0 width='100%'><tr><td width='100%'><input type=text id='d_fromurl' style='width:100%' size=20 value='http://'></td><td><input class='dlgBtnBrowse' type=button id='d_browse' onclick=\"xQ('file','fromurl')\"  value='"+lang["DlgBtnBrowse"]+"' align=absmiddle></td></tr></table>");}else{document.write("<input type=text id='d_fromurl' style='width:100%' size=40 value='http://'>");}</script> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id=d_ok onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel></td></tr> </table> <div id="tab_mfu" style="display:none"></div> </td></tr></table> <div id=divProcessing style="width:200px;height:30px;position:absolute;left:70px;top:30px;display:none"> <table border=0 cellpadding=0 cellspacing=1 bgcolor="#000000" width="100%" height="100%"><tr><td bgcolor=#3A6EA5><marquee align="middle" behavior="alternate" scrollamount="5"><font color=#FFFFFF><span lang=DlgComUploading></span></font></marquee></td></tr></table> </div> <input type=hidden id=d_filename value=""> </body> </html>