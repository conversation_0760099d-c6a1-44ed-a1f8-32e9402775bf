<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aJ=cc['action'];var aa="";var cX="";var Bo;var t;var wO;var qv="";switch(aJ){case "forecolor":aa=lang["DlgSelCorForecolor"];cX=EWEB.T.queryCommandValue("forecolor");if(cX){if(F.as){cX=vk(cX);}else{cX=BU(cX);}}break;case "backcolor":aa=lang["DlgSelCorBackcolor"];var BN="backcolor";if(!F.as&& !F.AM){BN="hiliteColor";}cX=EWEB.T.queryCommandValue(BN);if(cX){if(F.as){cX=vk(cX);}else{cX=BU(cX);}}break;case "bgcolor":aa=lang["DlgSelCorBgcolor"];if(C.ai()=="Control"){t=C.ax();if(t.tagName!="TABLE"){t=null;}}else{t=Ai(C.cI());}if(t){switch(t.tagName){case "TD":aa+=" - "+lang["DlgComTableCell"];break;case "TR":case "TH":aa+=" - "+lang["DlgComTableRow"];break;default:aa+=" - "+lang["DlgComTable"];break;}cX=t.style.backgroundColor;}else{aa+=" - "+lang["DlgComBody"];}break;default:qv=cc['returnfieldflag'];cX=dP.$("d_"+qv).value;break;}var bm=lang["DlgSelCor"]+"("+aa+")";document.write("<title>"+bm+"</title>");if(!cX){cX="#000000"};function Ai(obj){while(obj!=null&&obj.tagName!="TD"&&obj.tagName!="TR"&&obj.tagName!="TH"&&obj.tagName!="TABLE"){obj=obj.parentElement;}return obj;};function vk(aQ){aQ=aQ.toString(16);switch(aQ.length){case 1:aQ="0"+aQ+"0000";break;case 2:aQ=aQ+"0000";break;case 3:aQ=aQ.substring(1,3)+"0"+aQ.substring(0,1)+"00";break;case 4:aQ=aQ.substring(2,4)+aQ.substring(0,2)+"00";break;case 5:aQ=aQ.substring(3,5)+aQ.substring(1,3)+"0"+aQ.substring(0,1);break;case 6:aQ=aQ.substring(4,6)+aQ.substring(2,4)+aQ.substring(0,2);break;default:aQ="";}return '#'+aQ;};function CI(s){if(s.length==1){return "0"+s;}else{return s;}};function BU(aQ){var vq=/(.*?)rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(aQ);if(vq==null){return "";}var Cv=parseInt(vq[2]);var Cc=parseInt(vq[3]);var DP=parseInt(vq[4]);var ky=CI(Cv.toString(16))+CI(Cc.toString(16))+CI(DP.toString(16));return '#'+ky.toUpperCase();};function aq(){lang.ag(document);$("Table_ShowColor").bgColor=cX;$("Span_RGB").innerHTML=cX;$("D_SelColor").value=cX;var qj="cbox_"+cX.substr(1);var ju=$(qj);if(ju){ah(ju);}parent.ar(bm);};var mO=cX;var yL='';var vS='120';var hexch=new Array('0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F');function ih(n){var h,l;n=Math.round(n);l=n%16;h=Math.floor((n/16))%16;return(hexch[h]+hexch[l]);};function vG(c,l){var r,g,b;r='0x'+c.substring(1,3);g='0x'+c.substring(3,5);b='0x'+c.substring(5,7);if(l>120){l=l-120;r=(r*(120-l)+255*l)/120;g=(g*(120-l)+255*l)/120;b=(b*(120-l)+255*l)/120;}else{r=(r*l)/120;g=(g*l)/120;b=(b*l)/120;}return '#'+ih(r)+ih(g)+ih(b);};function nl(){var i;if(yL!=mO){yL=mO;for(i=0;i<=30;i++)$("Table_Gray").rows[i].bgColor=vG(mO,240-i*8);}var aQ=vG($("Span_RGB").innerHTML,$("Span_GRAY").innerHTML);$("D_SelColor").value=aQ;$("Table_ShowColor").bgColor=aQ;var qj="cbox_"+aQ.substr(1);var ju=$(qj);if(ju){ah(ju,true);}else{if(hk){$(hk).className="boxNormal";hk=""}}};var hk="";function bS(obj){if(obj.id==hk){obj.className="boxSelected";}else{obj.className="boxNormal";}};function aT(obj){if(obj.id==hk){obj.className="boxSelected";}else{obj.className="boxOver";}};function ah(ju,zU){if(ju.id==hk){return;}if(hk){$(hk).className="boxNormal";}hk=ju.id;ju.className="boxSelected";var aQ="#"+hk.substr(5);if(!zU){mO=aQ;}$("D_SelColor").value=aQ;$("Table_ShowColor").bgColor=$("D_SelColor").value;};function ok(){cX=$("D_SelColor").value;if(!oV(cX)){alert(lang["ErrColorInvalid"]);return;}switch(aJ){case "forecolor":if(F.as){EWIN.fq('color',cX);}else{EWIN.fq('foreColor',cX);}break;case "backcolor":if(F.as){EWIN.format('BackColor',cX);}else{EWIN.format('hiliteColor',cX);}break;case "bgcolor":if(t){t.style.backgroundColor=cX;}else{EWIN.setHTML("<table border=0 cellpadding=0 cellspacing=0 width='100%' height='100%'><tr><td valign=top bgcolor='"+cX+"'>"+EWIN.getHTML()+"</td></tr></table>",true);}break;default:dP.$("d_"+qv).value=cX;dP.$("s_"+qv).style.backgroundColor=cX;break;}parent.bV();};function Ae(e){var dZ=(F.as)?window.event.srcElement:e.target;$("Span_GRAY").innerHTML=dZ.title;nl();};function zy(e){$("Span_GRAY").innerHTML=vS;nl();};function As(e){vS=event.srcElement.title;nl();};function zZ(e){var dZ=(F.as)?window.event.srcElement:e.target;$("Span_RGB").innerHTML=dZ.bgColor;nl();};function yd(e){$("Span_RGB").innerHTML=mO;nl();};function ya(e){var dZ=(F.as)?window.event.srcElement:e.target;mO=dZ.bgColor;nl();} </script> <style> .boxNormal{border:1px solid #d4d0c8}.boxOver{border-width:1px;border-color:#ffffff #000000 #000000 #ffffff;border-style:solid}.boxSelected{border-width:1px;border-color:#000000 #ffffff #ffffff #000000;border-style:inset} </style> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr valign=top> <td> <table border=0 cellpadding=0 cellspacing=10><tr><td> <table id=border=0 cellpadding=3 cellspacing=0> <tr> <td id="cbox_000000" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#000000"></td> <td id="cbox_993300" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#993300"></td> <td id="cbox_333300" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#333300"></td> <td id="cbox_003300" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#003300"></td> <td id="cbox_003366" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#003366"></td> </tr> <tr> <td id="cbox_000080" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#000080"></td> <td id="cbox_333399" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#333399"></td> <td id="cbox_333333" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#333333"></td> <td id="cbox_800000" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#800000"></td> <td id="cbox_FF6600" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#FF6600"></td> </tr> <tr> <td id="cbox_808000" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#808000"></td> <td id="cbox_008000" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#008000"></td> <td id="cbox_008080" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#008080"></td> <td id="cbox_0000FF" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#0000FF"></td> <td id="cbox_666699" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#666699"></td> </tr> <tr> <td id="cbox_808080" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#808080"></td> <td id="cbox_FF0000" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#FF0000"></td> <td id="cbox_FF9900" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#FF9900"></td> <td id="cbox_99CC00" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#99CC00"></td> <td id="cbox_339966" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#339966"></td> </tr> <tr> <td id="cbox_33CCCC" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#33CCCC"></td> <td id="cbox_3366FF" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#3366FF"></td> <td id="cbox_800080" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#800080"></td> <td id="cbox_999999" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#999999"></td> <td id="cbox_FF00FF" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#FF00FF"></td> </tr> <tr> <td id="cbox_FFCC00" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#FFCC00"></td> <td id="cbox_FFFF00" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#FFFF00"></td> <td id="cbox_00FF00" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#00FF00"></td> <td id="cbox_00FFFF" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#00FFFF"></td> <td id="cbox_00CCFF" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#00CCFF"></td> </tr> <tr> <td id="cbox_993366" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#993366"></td> <td id="cbox_C0C0C0" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#C0C0C0"></td> <td id="cbox_FF99CC" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#FF99CC"></td> <td id="cbox_FFCC99" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#FFCC99"></td> <td id="cbox_FFFF99" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#FFFF99"></td> </tr> <tr> <td id="cbox_CCFFCC" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#CCFFCC"></td> <td id="cbox_CCFFFF" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#CCFFFF"></td> <td id="cbox_99CCFF" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#99CCFF"></td> <td id="cbox_CC99FF" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#CC99FF"></td> <td id="cbox_FFFFFF" onmouseover="aT(this)" onmouseout="bS(this)" onclick="ah(this)" class="boxNormal"><img src="../sysimage/space.gif" width=16 height=16 style="border:1px solid gray;background-color:#FFFFFF"></td> </tr> </table> </td></tr></table> </td> <td> <table border=0 cellPadding=0 cellSpacing=10 align=center> <tr> <td> <table border=0 cellPadding=0 cellSpacing=0 id=Table_Color style="CURSOR:hand" onmouseover="zZ(event)" onmouseout="yd(event)" onclick="ya(event)"> <script type="text/javascript"> function zE(r,g,b,n){r=((r*16+r)*3*(15-n)+0x80*n)/15;g=((g*16+g)*3*(15-n)+0x80*n)/15;b=((b*16+b)*3*(15-n)+0x80*n)/15;document.write('<td BGCOLOR=#'+ih(r)+ih(g)+ih(b)+' height=8 width=8></td>');};var cnum=new Array(1,0,0,1,1,0,0,1,0,0,1,1,0,0,1,1,0,1,1,0,0);for(i=0;i<16;i++){document.write('<tr>');for(j=0;j<30;j++){n1=j%5;n2=Math.floor(j/5)*3;n3=n2+3;zE((cnum[n3]*n1+cnum[n2]*(5-n1)),(cnum[n3+1]*n1+cnum[n2+1]*(5-n1)),(cnum[n3+2]*n1+cnum[n2+2]*(5-n1)),i);}document.writeln('</tr>');} </script> </table> </td> <td> <table border=0 cellPadding=0 cellSpacing=0 id=Table_Gray style="CURSOR:hand" onmouseover="Ae(event)" onmouseout="zy(event)" onclick="As(event)"> <script type="text/javascript"> for(i=255;i>=0;i-=8.5)document.write('<tr BGCOLOR=#'+ih(i)+ih(i)+ih(i)+'><td TITLE='+Math.floor(i*16/17)+' height=4 width=20></td></tr>'); </script> </table> </td> </tr> </table> <table border=0 cellPadding=0 cellSpacing=10 align=center> <tr> <td noWrap align=middle rowSpan=2><span lang=DlgSelCorSel></span> <table border=1 cellPadding=0 cellSpacing=0 height=30 id=Table_ShowColor width=40 bgcolor=""><tbody><tr><td></td></tr></tbody></table> </td> <td noWrap rowSpan=2><span lang=DlgSelCorBase></span>: <span id=Span_RGB></span><br><span lang=DlgSelCorLight></span>: <span id=Span_GRAY>120</span><br><span lang=DlgSelCorCode></span>: <input id=D_SelColor size=7 value=""></td> <td><input id="btn_ok" type=submit class="dlgBtnCommon dlgBtn" lang=DlgBtnOK value="" onclick="ok()"></td> </tr> <tr> <td noWrap><input type=button class="dlgBtnCommon dlgBtn" onclick="parent.bn();" lang=DlgBtnCancel value=""></td> </tr> </table> </td> </tr></table> </td></tr></table> </body></html>