<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script type="text/javascript" src="dialog.js"></script> <script type="text/javascript">document.write("<link href='../language/"+lang.ax+".css' type='text/css' rel='stylesheet'>");document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var aj=bV['action'];var ak=lang["DlgComInsert"];var D;var hk="2";var lq="2";var bt="";var ez="1";var jM="";var jo="collapse";var jp="3";var jm="2";var au="";var at="";var bx="#000000";var aS="#FFFFFF";var aE="";var cz="";var cE="";var fx="";var fA="%";var iJ=true;var gF=false;var eh="100";var fn="%";var iS=false;var gP=true;var ec="";if(aj=="modify"){if(I.ay()=="Control"){D=I.aX();if(D.tagName!="TABLE"){D=null;}}else{D=je(I.dm(),"TABLE");}if(D){aj="MODI";ak=lang["DlgComModify"];hk=D.rows.length;lq=sK(D);bt=bU(D,"align");ez=bU(D,"border");jM=D.style.textAlign;jo=D.style.borderCollapse;jp=bU(D,"cellPadding");jm=bU(D,"cellSpacing");au=dE(D,"width");at=dE(D,"height");bx=bU(D,"borderColor");aS=dE(D,"background-color");aE=D.style.backgroundImage;cz=D.style.backgroundRepeat;cE=D.style.backgroundAttachment;fx=D.style.borderStyle;aE=aE.replace(/\"/gi,"");aE=aE.substr(4,aE.length-5);}}function je(H,hQ){while(H!=null&&H.tagName!=hQ){H=H.parentNode;if(!H|| !H.tagName){H=null;break;}}return H;};var Q=lang["DlgTab"]+"("+ak+")";document.write("<title>"+Q+"</title>");function ah(){lang.TranslatePage(document);bb($("d_align"),bt.toLowerCase());bb($("d_aligntext"),jM.toLowerCase());bb($("d_bordercollapse"),jo.toLowerCase());bb($("d_borderstyle"),fx.toLowerCase());if(aj=="MODI"){if(au==""){iJ=false;gF=true;eh="100";fA="%";}else{iJ=true;gF=false;if(au.substr(au.length-1)=="%"){eh=au.substring(0,au.length-1);fA="%";}else{fA="";eh=parseInt(au);if(isNaN(eh)){eh="";}}}if(at==""){iS=false;gP=true;ec="100";fn="%";}else{iS=true;gP=false;if(at.substr(at.length-1)=="%"){ec=at.substring(0,at.length-1);fn="%";}else{fn="";ec=parseInt(at);if(isNaN(ec)){ec="";}}}}switch(fA){case "%":$("d_widthunit").selectedIndex=1;break;default:fA="";$("d_widthunit").selectedIndex=0;break;}switch(fn){case "%":$("d_heightunit").selectedIndex=1;break;default:fn="";$("d_heightunit").selectedIndex=0;break;}$("d_row").value=hk;$("d_col").value=lq;$("d_border").value=ez;$("d_cellspacing").value=jm;$("d_cellpadding").value=jp;$("d_widthvalue").value=eh;$("d_widthvalue").disabled=gF;$("d_widthunit").disabled=gF;$("d_heightvalue").value=ec;$("d_heightvalue").disabled=gP;$("d_heightunit").disabled=gP;$("d_bordercolor").value=bx;$("s_bordercolor").style.backgroundColor=bx;$("d_bgcolor").value=aS;$("s_bgcolor").style.backgroundColor=aS;$("d_widthcheck").checked=iJ;$("d_heightcheck").checked=iS;$("d_image").value=aE;$("d_repeat").value=cz;$("d_attachment").value=cE;parent.bp(Q);};function eH(H,io){var b=false;if(H.value!=""){H.value=parseFloat(H.value);if(H.value!="0"){b=true;}}if(b==false){cv(H,io);return false;}return true;};function sK(cV){var rg=0;if(cV!=null){for(var i=0;i<cV.rows.length;i++){if(cV.rows[i].cells.length>rg){rg=cV.rows[i].cells.length;}}}return rg;};function wu(cV){if(cV){var uV=cV.insertRow(-1);for(var i=0;i<cV.rows[0].cells.length;i++){var qe=uV.insertCell(-1);qe.innerHTML="&nbsp;";}}};function wt(cV){if(cV){for(var i=0;i<cV.rows.length;i++){var qe=cV.rows[i].insertCell(-1);qe.innerHTML="&nbsp;"}}};function uG(cV){if(cV){cV.deleteRow(-1);}};function uM(cV){if(cV){for(var i=0;i<cV.rows.length;i++){cV.rows[i].deleteCell(-1);}}};function ok(){bx=$("d_bordercolor").value;aS=$("d_bgcolor").value;if(!eH($("d_row"),lang["DlgTabInvalidRow"])){return;}if(!eH($("d_col"),lang["DlgTabInvalidCol"])){return;}var au="";if($("d_widthcheck").checked){if(!eH($("d_widthvalue"),lang["DlgTabInvalidWidth"])){return;}au=$("d_widthvalue").value+$("d_widthunit").value;}var at="";if($("d_heightcheck").checked){if(!eH($("d_heightvalue"),lang["DlgTabInvalidHeight"])){return;}at=$("d_heightvalue").value+$("d_heightunit").value;}hk=$("d_row").value;lq=$("d_col").value;bt=$("d_align").options[$("d_align").selectedIndex].value;ez=$("d_border").value;jM=$("d_aligntext").options[$("d_aligntext").selectedIndex].value;jo=$("d_bordercollapse").options[$("d_bordercollapse").selectedIndex].value;jp=$("d_cellpadding").value;jm=$("d_cellspacing").value;aE=$("d_image").value;cz=$("d_repeat").value;cE=$("d_attachment").value;fx=$("d_borderstyle").options[$("d_borderstyle").selectedIndex].value;if(aE!=""){aE="url("+aE+")";}if(aj=="MODI"){var hz=hk-D.rows.length;if(hz>0){for(var i=0;i<hz;i++){wu(D);}}else{for(var i=0;i>hz;i--){uG(D);}}var hz=lq-sK(D);if(hz>0){for(var i=0;i<hz;i++){wt(D);}}else{for(var i=0;i>hz;i--){uM(D);}}D.style.width=au;bm(D,"width","");D.style.height=at;bm(D,"height","");bm(D,"align",bt);bm(D,"border",ez);D.style.textAlign=jM;D.style.borderCollapse=jo;bm(D,"cellSpacing",jm);bm(D,"cellPadding",jp);bm(D,"borderColor",bx);D.style.backgroundColor=aS;bm(D,"bgColor","");D.style.backgroundImage=aE;D.style.backgroundRepeat=cz;D.style.backgroundAttachment=cE;D.style.borderStyle=fx;}else{var eJ='<table';if(bt!=''){eJ+=' align="'+bt+'"';}if(ez!=''){eJ+=' border="'+ez+'"';}if(jp!=''){eJ+=' cellpadding="'+jp+'"';}if(jm!=''){eJ+=' cellspacing="'+jm+'"';}if(bx!=''){eJ+=' bordercolor="'+bx+'"';}var aw='';if(au!=''){aw+='width:'+au+';';}if(at!=''){aw+='height:'+at+';';}if(aS!=''){aw+='background-color:'+aS+';';}if(aE!=''){aw+='background-image:'+aE+';';}if(cz!=''){aw+='background-repeat:'+cz+';';}if(cE!=''){aw+='background-attachment:'+cE+';';}if(fx!=''){aw+='border-style:'+fx+';';}if(jM!=''){aw+='text-align:'+jM+';';}if(jo!=''){aw+='border-collapse:'+jo+';';}if(aw!=''){eJ+=' style="'+aw+'"';}eJ+='>';for(var i=1;i<=hk;i++){eJ+='<tr>';for(var j=1;j<=lq;j++){eJ+='<td>&nbsp;</td>';}eJ+='</tr>';}eJ+='</table>';EWIN.insertHTML(eJ);}parent.aT();}</script> </head> <body onload="ah()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table border=0 cellpadding=0 cellspacing=0 align=center> <tr> <td> <fieldset> <legend><span lang=DlgTabRowsCols></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgTabRows></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_row size=10 value="" onkeydown="et(event)"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabCols></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_col size=10 value="" onkeydown="et(event)"></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgTabLayout></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgAlign></span>:</td> <td noWrap width="29%"> <select id="d_align" style="width:80px"> <option value='' lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='center' lang=DlgAlignCenter></option> <option value='right' lang=DlgAlignRight></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabBorder></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_border size=10 value="" onkeydown="et(event)"></td> </tr> <tr> <td noWrap width="20%"><span lang=DlgAlignText></span>:</td> <td noWrap width="29%"> <select id="d_aligntext" style="width:80px"> <option value='' lang=DlgComDefault></option> <option value='left' lang=DlgAlignLeft></option> <option value='center' lang=DlgAlignCenter></option> <option value='right' lang=DlgAlignRight></option> <option value='justify' lang=DlgAlignFull></option> </select> </td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabBorderCollapse></span>:</td> <td noWrap width="29%"> <select id="d_bordercollapse" style="width:80px"> <option value='' lang=DlgComDefault></option> <option value='separate' lang=DlgTabBCseparate></option> <option value='collapse' lang=DlgTabBCcollapse></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgTabCellspacing></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_cellspacing size=10 value="" onkeydown="et()"></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabCellpadding></span>:</td> <td noWrap width="29%"><input style="width:80px" type=text id=d_cellpadding size=10 value="" onkeydown="et()"></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgTabSize></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap><input id="d_widthcheck" type="checkbox" onclick="$('d_widthvalue').disabled=(!this.checked);$('d_widthunit').disabled=(!this.checked);" value="1"> <label for=d_widthcheck><span lang=DlgTabChkWidth></span></label></td> <td noWrap align=right> <input id="d_widthvalue" type="text" value="" size="5"> <select id="d_widthunit" size=1 style="width:65px"> <option value='px' lang=DlgComPx></option><option value='%' lang=DlgComPer></option> </select> </td> </tr> <tr> <td noWrap><input id="d_heightcheck" type="checkbox" onclick="$('d_heightvalue').disabled=(!this.checked);$('d_heightunit').disabled=(!this.checked);" value="1"> <label for=d_heightcheck><span lang=DlgTabChkHeight></span></label></td> <td noWrap align=right> <input id="d_heightvalue" type="text" value="" size="5"> <select id="d_heightunit" size=1 style="width:65px"> <option value='px' lang=DlgComPx></option><option value='%' lang=DlgComPer></option> </select> </td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr> <td> <fieldset> <legend><span lang=DlgTabStyle></span></legend> <table border=0 cellpadding=5 cellspacing=0 width="100%"> <tr><td> <table border=0 cellpadding=0 cellspacing=2 width="100%"> <tr> <td noWrap width="20%"><span lang=DlgColorBorder></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bordercolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bordercolor onclick="fX('bordercolor')" align=absmiddle></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabBorderStyle></span>:</td> <td noWrap width="29%"> <select id=d_borderstyle size=1 style="width:80px"> <option value="" lang=DlgComDefault></option> <option value="solid" lang=DlgLineSolid></option> <option value="dotted" lang=DlgLineDotted></option> <option value="dashed" lang=DlgLineDashed></option> <option value="double" lang=DlgLineDouble></option> <option value="groove" lang=DlgLineGroove></option> <option value="ridge" lang=DlgLineRidge></option> <option value="inset" lang=DlgLineInset></option> <option value="outset" lang=DlgLineOutset></option> </select> </td> </tr> <tr> <td noWrap width="20%"><span lang=DlgColorBg></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_bgcolor size=7 value=""><img border=0 src="images/rect.gif" width=18 style="cursor:hand" id=s_bgcolor onclick="fX('bgcolor')" align=absmiddle></td> <td width="2%">&nbsp;</td> <td noWrap width="20%"><span lang=DlgTabBgImage></span>:</td> <td noWrap width="29%"><input style="width:62px" type=text id=d_image size=7 value=""><input type=hidden id=d_repeat><input type=hidden id=d_attachment><img border=0 src="images/rectimg.gif" width=18 style="cursor:hand" id=s_bgimage onclick="sv()" align=absmiddle></td> </tr> </table> </td></tr> </table> </fieldset> </td> </tr> <tr><td height=5></td></tr> <tr><td noWrap align=right><input type=submit class="dlgBtnCommon dlgBtn" value='' id="btn_ok" onclick="ok()" lang=DlgBtnOK>&nbsp;&nbsp;<input type=button class="dlgBtnCommon dlgBtn" value='' onclick="parent.aP()" lang=DlgBtnCancel></td></tr> </table> </td></tr></table> </body> </html>