﻿

/*----------------------------------- button ----------------------------------*/

.mini-button
{
	background:#b7c691 url(images/button/button.gif) repeat-x;
	border:solid 1px #888;
    color:#222;
}
body a:hover.mini-button
{
	background:#ffd294 url(images/button/hover.gif) repeat-x;
	border:solid 1px #608058;	         
}
body .mini-button-pressed, body a:hover.mini-button-pressed,
body .mini-button-checked, body a:hover.mini-button-checked,
body a.mini-button-popup, body a:hover.mini-button-popup
{
	background:#ffcf8b url(images/button/pressed.gif) repeat-x;	
	border-top:solid 1px #608058;
}
body .mini-button-disabled, body a:hover.mini-button-disabled
{
    border: 1px solid #bfbfbf;
    color:#808080;
    background:#cecece url(images/button/disabled.gif) repeat-x 0 0px;       
}



/*----------------------------------- textbox ----------------------------------*/
.mini-textbox-border
{
    background:white;
	border-color:#608058;        
}
body .mini-textbox-focus .mini-textbox-border
{
    border-color: #608058;
}


/*----------------------------------- buttonedit ----------------------------------*/

.mini-buttonedit-border
{
    background:white;
	border-color:#608058;      
}
body .mini-buttonedit-focus .mini-buttonedit-border
{
    border-color: #608058;
}
.mini-buttonedit-button
{
	background:url(images/buttonedit/btn.gif) no-repeat;		
	border:#608058 1px solid; 
	padding:0;
}
.mini-buttonedit-button-hover,
.mini-buttonedit-hover .mini-buttonedit-button
{
    background:url(images/buttonedit/btn-hover.gif) repeat-x 0 50%;	
	border:#608058 1px solid;
}
.mini-buttonedit-button-pressed,
.mini-buttonedit-popup .mini-buttonedit-button
{
    background:url(images/buttonedit/btn-pressed.gif) repeat-x 0 50%;	
	border:#608058 1px solid;
}



/*------------------------- menu --------------------------------*/
.mini-menu
{
	background:#f4f4ee url(images/menu/menu_bg.gif) repeat-y 0 0;	
	border-color: #758d5e;	
    color:black;
}
.mini-menuitem
{
    line-height:20px;    
}
.mini-menuitem-hover
{
    border-color:#758d5e;
	background:#ffeec2;	
}
.mini-menu-popup
{
    border-color:#758d5e;
	background:#ffeec2;    
}
.mini-menuitem-selected
{
    border-color:#758d5e;     
    background:#ffad55 url(images/menu/pressed.gif) repeat-x 0 0;
}
.mini-menuitem-text, .mini-menuitem-text a
{
    color:black;
}
.mini-separator
{
    border-top:solid 1px #758d5e;
}
/* menu horizontal */
.mini-menu-horizontal .mini-menu-inner
{
    background:#f4f4ee;
}
.mini-menu-horizontal .mini-menuitem-hover
{
    border:1px solid #758d5e;
	background:#ffeec2;
}
.mini-menu-horizontal  .mini-menu-popup
{
    border:1px solid #758d5e;
	background:#ffeec2;	
    border-bottom:0px;
}

/*---------------------- calendar -----------------------------*/
.mini-calendar
{    
    border:1px solid #293e24;
}
.mini-calendar-header
{   
    background:#b7c691 url(images/calendar/header.gif) repeat-x 0 0;    
    border-bottom:solid 1px #d5e1b1;    
}
.mini-calendar-footer
{
    background:#e3ebc5;
    border-top:solid 1px #d5e1b1;
}
.mini-calendar-tadayButton, .mini-calendar-clearButton,
.mini-calendar-okButton, .mini-calendar-cancelButton
{
	background:url(images/button/button.gif) repeat-x 0px 50%;
	border:solid 1px #608058;	
    color:#222;
}
.mini-calendar-menu-selected, a:hover.mini-calendar-menu-selected
{
    color:#333;
    background:#ffcf8b url(images/button/pressed.gif) repeat-x;
    border:solid 1px #608058;
}

.mini-calendar .mini-calendar-selected
{
    color:#333;
    background:#ffc076;
    border:0;
}
.mini-calendar .mini-calendar-today
{
    border:1px solid #C00000;
}
.mini-calendar-daysheader td{
    border-bottom:solid 1px #d5e1b1;
}
.mini-calendar td.mini-calendar-weeknumber{
    color:#b7c691;
}
a:hover.mini-calendar-menu-month{
    background:#e3ebc5;
    border:solid 1px #608058;
}
a:hover.mini-calendar-menu-year
{
    background:#e3ebc5;
    border:solid 1px #608058;
}


/*----------------------------------- toolbar ----------------------------------*/

.mini-toolbar
{
    border:solid 1px #758d5e;
    padding:5px;
    background:#f2f0e4 url(images/toolbar/header.gif) repeat-y 0 0;
}
.separator
{
    border-left:solid 1px #758d5e;    
}

/*------------------------- panel -----------------------*/
.mini-panel
{    
    border:1px solid #758d5e;     
}
.mini-panel-header
{
    height:25px;
    background:#b5c48f url(images/panel/header.gif) repeat-x 0 0px;
    color:#000000;
    font-weight:bold;
    border-bottom:solid 1px #758d5e;
}
.mini-panel-header-inner
{
   padding-top:4px;
}
.mini-panel-toolbar
{
    border-bottom:solid 1px #c3cf9d;
    background:#e2e8c9;
}

.mini-panel-footer
{
    border-top:solid 1px #c3cf9d;
    background:#e2e8c9;
}

/*----------------------------- window -------------------------*/
.mini-window .mini-panel-header
{
    background:#b5c48f url(images/window/header.gif) repeat-x 0 0px;
}
.mini-window .mini-panel-footer
{
    background:#e2e8c9;
}

/*------------------- navbar ------------------*/
.mini-outlookbar
{
    border:1px solid #758d5e;
}
.mini-outlookbar-border
{
	border:1px solid #758d5e;
}
.mini-outlookbar .mini-outlookbar-groupHeader
{
    background:#b5c48f url(images/navbar/header.gif) repeat-x 0 0;    
    border-color:#758d5e;
}
.mini-outlookbar .mini-outlookbar-groupTitle
{
    color:Black;
    font-weight:normal;
}
.mini-outlookbar .mini-outlookbar-group 
{
    border-color:#99bce8;
}
.mini-outlookbar .mini-outlookbar-groupBody
{    
    border-color:#99bce8;
}
/* view2 */
.mini-outlookbar-view2 .mini-outlookbar-groupHeader
{
    border:solid 1px #758d5e; 
}
.mini-outlookbar-view2 .mini-outlookbar-groupBody
{    
    background:#e2e8c9;
    border:solid 1px #758d5e;
    border-top:0;
}
/* view3 */
.mini-outlookbar-view3 .mini-outlookbar-group
{
    border:solid 1px #99bce8; 
}

.mini-outlookbar .mini-tools-collapse
{
    width:15px;	
}
.mini-outlookbar .mini-outlookbar-expand .mini-tools-collapse
{
    background:url(images/navbar/expand.gif) no-repeat 50% 50%;   
}
.mini-outlookbar .mini-outlookbar-collapse .mini-tools-collapse
{
    background:url(images/navbar/collapse.gif) no-repeat 50% 50%;   
}


/*----------------------- splitter -----------------------*/
.mini-splitter
{
    border:solid 1px #758d5e;     
}
.mini-splitter .mini-splitter-pane1{
    border-color:#758d5e;
}
.mini-splitter .mini-splitter-pane2{
    border-color:#758d5e;
}

/*----------------------- layout -----------------------*/
.mini-layout
{
    
}
.mini-layout-region
{
    border:1px solid #758d5e;
}
.mini-layout-region-header
{
    background:url(images/layout/header.gif) repeat-x 0 0;
    border-bottom:solid 1px #758d5e;
}
.mini-layout-proxy
{
    border-color:#758d5e;
    background:#d1dcb0;
}
.mini-layout-proxy-hover
{
    background:#ffd294 url(images/layout/hover.gif) repeat-x 0 0;
}

.mini-layout-region-west .mini-tools-collapse
{
    background:url(images/layout/west.gif) no-repeat 50% 50%;
}
.mini-layout-region-east .mini-tools-collapse
{
    background:url(images/layout/east.gif) no-repeat 50% 50%;
}
.mini-layout-region-north .mini-tools-collapse
{
    background:url(images/layout/north.gif) no-repeat 50% 50%;
}
.mini-layout-region-south .mini-tools-collapse
{
    background:url(images/layout/south.gif) no-repeat 50% 50%;
}

.mini-layout-proxy-west .mini-tools-collapse
{
    background:url(images/layout/east.gif) no-repeat 50% 50%;
}
.mini-layout-proxy-east .mini-tools-collapse
{
    background:url(images/layout/west.gif) no-repeat 50% 50%;
}
.mini-layout-proxy-north .mini-tools-collapse
{
    background:url(images/layout/south.gif) no-repeat 50% 50%;
}
.mini-layout-proxy-south .mini-tools-collapse
{
    background:url(images/layout/north.gif) no-repeat 50% 50%;
}

/*---------------------- listbox -----------------------------*/
.mini-listbox
{    
    border:#608058 1px solid;
}
.mini-listbox-inner td
{
    line-height:20px;       
}
.mini-listbox-item td{
	border-top:white 1px dotted;
	border-bottom:white 1px dotted;	
}
.mini-listbox-item-hover td{
    background:#e3f0bb;
	border-top:#e3f0bb 1px dotted;
	border-bottom:#e3f0bb 1px dotted;
}
.mini-listbox-item-selected td{
	background:#ffc076;
	border-top:#ffc076 1px dotted;
	border-bottom:#ffc076 1px dotted;
}
.mini-listbox-header
{
    background:#b8c692 url(images/listbox/header.gif) repeat-x 0 0;    
    border-bottom:solid 1px #608058;
}

/*------------------- treegrid --------------------*/
.mini-treegrid-border
{
    border-color:#608058;
}

.mini-treegrid-header
{
    border-color:#608058;
}
.mini-treegrid-headerInner
{
    background:#b8c692 url(images/listbox/header.gif) repeat-x 0 0;  
}
.mini-treegrid td
{
    border-color:#a4b26d;    
}
.mini-treegrid-header td
{
    border-color:#e3f0bb;
}

.mini-treegrid-selectedNode
{
	background:#ffc076;
}
.mini-treegrid-hoverNode
{
    background:#e3f0bb;
}
/*
.mini-treegrid-expand .mini-treegrid-ec-icon
{
	background-image:url(images/treegrid/expand.gif);
}
.mini-treegrid-collapse .mini-treegrid-ec-icon
{
	background-image:url(images/treegrid/collapse.gif);
}*/
.mini-treegrid-leaf
{
    background-image:url(images/treegrid/file.png);
}
.mini-treegrid-folder
{
    background-image:url(images/treegrid/folder.gif);
}

/*---------------------- tabs -----------------------------*/
.mini-tabs-scrollCt
{
    border-color:#758d5e;
    background:#f2f0e4 url(images/toolbar/header.gif) repeat-y 0 0;
}
.mini-tabs-leftButton, .mini-tabs-rightButton
{
    border:solid 1px #b5afaf;
    background-color:#EBEBEE;
}
a:hover.mini-tabs-leftButton,a:hover.mini-tabs-rightButton
{
    border:solid 1px #758d5e;
    background-color:#E1E8FD;
}
/* top */
.mini-tabs-bodys
{
    border:solid 1px #758d5e;
    border-top:0;
}
.mini-tabs-space
{
    border-bottom:solid 1px #758d5e;
}
.mini-tabs-space2
{
    border-bottom:solid 1px #758d5e;
}

.mini-tab
{
    background: #b5c48f url(images/tabs/tab.gif) repeat-x 0 0;  
    border: 1px solid #758d5e;
    color: #333;    
    
}
.mini-tab-hover
{    
    background:#ffd091 url(images/tabs/hover.gif) repeat-x 0 0; 
}
.mini-tab-active
{
    border-bottom:solid 1px white;
    background:#e2e8c9;  
}

/* bottom */
.mini-tabs-header-bottom .mini-tabs-space,
.mini-tabs-header-bottom .mini-tabs-space2
{
    border:0;
    border-top: 1px solid #758d5e;
}
.mini-tabs-header-bottom .mini-tabs-bodys
{    
    border:solid 1px #758d5e;
    border-bottom:0;
}
.mini-tabs-header-bottom .mini-tab-active
{
    border-top:solid 1px white;
    border-bottom:solid 1px #758d5e;
}
.mini-tabs-body-bottom
{
    border:solid 1px #758d5e;
    border-bottom:0;
}
/* left */
.mini-tabs-header-left .mini-tabs-space,
.mini-tabs-header-left .mini-tabs-space2
{
    border:0;
    border-right: 1px solid #758d5e;
}
.mini-tabs-header-left .mini-tabs-bodys
{
    border:solid 1px #758d5e;
    border-left:0;
}
.mini-tabs-header-left .mini-tab-active
{    
    border:solid 1px #758d5e;
    border-right:solid 1px white;
}
.mini-tabs-body-left
{
    border:solid 1px #758d5e;
    border-left:0;
}


/* right */
.mini-tabs-header-right .mini-tabs-space,
.mini-tabs-header-right .mini-tabs-space2
{
    border:0;
    border-left: 1px solid #758d5e;
}
.mini-tabs-header-right .mini-tabs-bodys
{    
    border:solid 1px #758d5e;
    border-right:0;
}
.mini-tabs-header-right .mini-tab-active
{    
    border:solid 1px #758d5e;
    border-left:solid 1px white;
}
.mini-tabs-body-right
{
    border:solid 1px #758d5e;
    border-right:0;
}
/*---------------------- tree -----------------------------*/

.mini-tree-node-hover .mini-tree-nodeshow
{	    
	background:#ffeec2;
	border:solid 1px #758d5e;		
}
.mini-tree-selectedNode .mini-tree-nodeshow
{
    background:#ffad55 url(images/tree/pressed.gif) repeat-x 0px 50%;
	border:solid 1px #758d5e;	
}
/*
.mini-tree-collapse .mini-tree-node-ecicon
{
    background-image:url(images/tree/collapse.gif);
}
.mini-tree-expand .mini-tree-node-ecicon
{
    background-image:url(images/tree/expand.gif);
}
.mini-tree-treeLine .mini-tree-indent{
    background:transparent url(images/tree/treeline.gif) repeat-y 9px 0; 
}
.mini-tree-treeLine .mini-tree-node-ecicon
{
    background:transparent url(images/tree/treeNodeLine.gif) no-repeat -1px 0; 
}
.mini-tree-treeLine .mini-tree-expand .mini-tree-node-ecicon{
    background:transparent url(images/tree/expandLine.gif) no-repeat -4px -5px;
}
.mini-tree-treeLine .mini-tree-collapse .mini-tree-node-ecicon{
    background:transparent url(images/tree/collapseLine.gif) no-repeat -4px -7px;
}
.mini-tree-treeLine .mini-tree-node-ecicon-last
{
    background:transparent url(images/tree/lastline.gif) no-repeat -1px -8px; 
}

.mini-tree-treeLine .mini-tree-expand .mini-tree-node-ecicon-first
{
    background:transparent url(images/tree/firstExpandNode.gif) no-repeat -4px -10px;
}
.mini-tree-treeLine .mini-tree-expand .mini-tree-node-ecicon-last
{
    background:transparent url(images/tree/lastExpandNode.gif) no-repeat -3px -8px;
}
.mini-tree-treeLine .mini-tree-collapse .mini-tree-node-ecicon-first
{
    background:transparent url(images/tree/firstCollapseNode.gif) no-repeat -4px -8px;
}
.mini-tree-treeLine .mini-tree-collapse .mini-tree-node-ecicon-last
{
    background:transparent url(images/tree/lastCollapseNode.gif) no-repeat -4px -6px;
}

.mini-tree-treeLine .mini-tree-expand .mini-tree-node-ecicon-firstAndlast
{
    background-position:50% 70%;
    background-image:url(images/tree/firstAndlastexpand.gif);
}
.mini-tree-treeLine .mini-tree-collapse .mini-tree-node-ecicon-firstAndlast
{
    background-position:40% 60%;
    background-image:url(images/tree/firstAndlastcollapse.gif);
}
*/



/*------------------- grid --------------------*/
.mini-grid-headerCell, .mini-grid-topRightCell
{
    background:#b8c692 url(images/grid/header.gif) repeat-x 0 0;
    border-right:#A5ACB5 1px solid;
    border-bottom:#A5ACB5 1px solid;
}
.mini-grid-footer, .mini-grid-pager
{
    background:#cfd9a3;
}
.mini-grid-detailCell
{    
    padding: 8px 10px 10px;
    border-right:#a4b26d 1px solid;
    border-bottom:#a4b26d 1px solid;
}
.mini-grid-summaryCell
{
    padding-right:8px;
    border-top:#a4b26d 1px solid;
    border-right:0;
}
.mini-grid-cell, .mini-grid-headerCell,
.mini-grid-filterCell, .mini-grid-summaryCell
{
    border-right:#a4b26d 1px solid;
    border-bottom:#a4b26d 1px solid;    
}
.mini-grid-filterRow .mini-grid-table, .mini-grid-summaryRow .mini-grid-table {
    background: none repeat scroll 0 0 #eff4da;
}
.mini-grid-detailRow {
    background: none repeat scroll 0 0 #eff4da;
}

/*------------------- popup --------------------*/
.mini-popup{
    border:1px solid #608058;
}

/*------------------- pager --------------------*/
.mini-pager-first
{    
    background:url(images/pager/first.gif) no-repeat;
}
.mini-pager-prev
{
    background-image:url(images/pager/prev.gif);
}
.mini-pager-next
{
    background-image:url(images/pager/next.gif);
}
.mini-pager-last
{
    background-image:url(images/pager/last.gif);
}
/*---------------------- progressbar -----------------------------*/
.mini-progressbar
{
    border:1px solid #608058;
}
.mini-progressbar-border
{
    border:1px solid #608058;
}
.mini-progressbar-bar
{
    background: url("images/button/button.gif") repeat-x scroll 0 0 #b7c691;
}
.mini-progressbar-text
{ 
    color:#222; 
}