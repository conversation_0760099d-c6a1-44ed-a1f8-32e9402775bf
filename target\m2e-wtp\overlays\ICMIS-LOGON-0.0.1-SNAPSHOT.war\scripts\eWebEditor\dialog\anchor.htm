<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN"> <html> <head> <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"> <meta http-equiv="Content-Type" content="text/html; charset=gb2312"> <script type="text/javascript" src="dialog.js"> </script> <script type="text/javascript"> document.write("<link href='../skin/"+config.Skin+"/dialog.css' type='text/css' rel='stylesheet'>");var bm=lang["DlgAnchor"];document.write("<title>"+bm+"</title>");var gX;var af;var iX="";af=C.cI();if(af.tagName.toUpperCase()=="A"){if(af.href.toUpperCase()==""){iX=af.name;wp(af);}}function aq(){$("d_anchor").value=iX;zq();lang.ag(document);parent.ar(bm);};function zq(){$("d_allanchor").options.length=0;var ia=EWEB.T.body.getElementsByTagName("A");for(i=0;i<ia.length;i++){if(ia[i].href.toUpperCase()==""){$("d_allanchor").options[$("d_allanchor").options.length]=new Option(ia[i].name,ia[i].name);}}};function xM(ay){var ia=EWEB.T.body.getElementsByTagName("A");for(i=0;i<ia.length;i++){if(ia[i].href.toUpperCase()==""){if(ia[i].name==ay){return ia[i];}}}return null;};function ok(){var iX=fk($("d_anchor").value);if(iX==""){bX($("d_anchor"),lang["DlgAnchorNoName"]);return;}gX=C.ai();af=C.cI();var xn=false;if(af.tagName.toUpperCase()=="A"){if(af.href.toUpperCase()==""){af.name=iX;xn=true;}}if(!xn){var html="";if(gX=="Control"){var gk=EWEB.T.createElement('A');gk.name=iX;var t=C.ax();t.parentNode.insertBefore(gk,t);t.parentNode.removeChild(t);gk.appendChild(t);}else{var wf='javascript:void(0);/*'+(new Date().getTime())+'*/';EWEB.T.execCommand('CreateLink',false,wf);var nn=EWEB.T.links;for(i=0;i<nn.length;i++){var gk=nn[i];if(gk.getAttribute('href',2)==wf){gk.name=iX;gk.removeAttribute("href",0);}}}}parent.bV();};function AH(){var dO=$("d_allanchor").selectedIndex;if(dO<0){alert(lang["DlgAnchorNoSelected"]);return;}var lV=xM($("d_allanchor").options[dO].value);if(lV){wp(lV);}};function zo(){var dO=$("d_allanchor").selectedIndex;if(dO<0){alert(lang["DlgAnchorNoSelected"]);return;}var lV=xM($("d_allanchor").options[dO].value);if(lV){R.cE(lV,true);$("d_allanchor").options[dO]=null;}};function wp(bz){C.Release();C.vR(bz);C.Save(true);} </script> </head> <body onload="aq()"> <table border=0 cellpadding=0 cellspacing=5 id=tabDialogSize><tr><td> <table cellspacing="0" cellpadding="5" border="0" align=center> <tr valign="top"> <td noWrap align="left"> <span lang=DlgAnchorName></span>:<br> <input type=text size=20 id="d_anchor" style="width:150px;"><br> <span lang=DlgAnchorOther></span>:<br> <select id="d_allanchor" size=8 style="width:150px;" onchange="$('d_anchor').value=this.options[this.selectedIndex].value;"></select> </td> <td noWrap height="100%"> <table border=0 cellpadding=0 cellspacing=0 height="100%"> <tr> <td height="50%" valign=top> <input type=button class="dlgBtnCommon dlgBtn" style="margin-top:15px" name="btnOK" onClick="ok()" value="" lang=DlgBtnOK><br> <input type=button class="dlgBtnCommon dlgBtn" style="margin-top:5px" name="btnCancel" onClick="parent.bn()" value="" lang=DlgBtnCancel><br> </td> </tr> <tr> <td height="50%" valign=bottom> <input type=button class="dlgBtnCommon dlgBtn" name="btnMove" onClick="AH()" value="" lang=DlgBtnGoto><br> <input type=button class="dlgBtnCommon dlgBtn" style="margin-top:5px" name="btnDel" onClick="zo()" value="" lang=DlgBtnDel><br> </td> </tr> </table> </td> </tr> </table> </td></tr></table> </body> </html>