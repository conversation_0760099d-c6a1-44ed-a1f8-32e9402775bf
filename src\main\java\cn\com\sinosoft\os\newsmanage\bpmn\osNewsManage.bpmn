<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/test">
  <process id="NewsManage" name="新闻发表审核" isExecutable="true">
    <userTask id="office" name="第二步：办公室审核" activiti:candidateGroups="OS200000104">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/ne/newsmanage/newsManage_auditParentInput.ac" default="2&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="exclusivegateway2" name="Exclusive Gateway"></exclusiveGateway>
    <userTask id="personConfirm" name="第一步：个人确认" activiti:assignee="${startUser}" activiti:candidateGroups="OS200000105">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/ne/newsmanage/newsManage_auditParentInput.ac" default="1&amp;pageState=view"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <userTask id="correction" name="订正" activiti:assignee="${startUser}">
      <extensionElements>
        <activiti:formProperty id="GOTO_INFO" name="/cn/com/sinosoft/ne/newsmanage/newsManage_auditeditParentInput.ac" default="0&amp;pageState=edit"></activiti:formProperty>
      </extensionElements>
    </userTask>
    <sequenceFlow id="flow5" name="驳回" sourceRef="exclusivegateway2" targetRef="correction">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='correction'}]]></conditionExpression>
    </sequenceFlow>
    <endEvent id="endevent1" name="End"></endEvent>
    <exclusiveGateway id="exclusivegateway3" name="Exclusive Gateway"></exclusiveGateway>
    <sequenceFlow id="flow6" sourceRef="personConfirm" targetRef="exclusivegateway2">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow7" name="通过" sourceRef="exclusivegateway3" targetRef="endevent1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow9" name="驳回" sourceRef="exclusivegateway3" targetRef="correction">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='correction'}]]></conditionExpression>
    </sequenceFlow>
    <startEvent id="startevent1" name="Start"></startEvent>
    <sequenceFlow id="flow10" sourceRef="startevent1" targetRef="personConfirm"></sequenceFlow>
    <sequenceFlow id="flow11" name="通过" sourceRef="exclusivegateway2" targetRef="office">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flag=='1'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow12" sourceRef="office" targetRef="exclusivegateway3"></sequenceFlow>
    <sequenceFlow id="flow13" sourceRef="correction" targetRef="personConfirm"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_NewsManage">
    <bpmndi:BPMNPlane bpmnElement="NewsManage" id="BPMNPlane_NewsManage">
      <bpmndi:BPMNShape bpmnElement="office" id="BPMNShape_office">
        <omgdc:Bounds height="71.0" width="125.0" x="592.0" y="182.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway2" id="BPMNShape_exclusivegateway2">
        <omgdc:Bounds height="40.0" width="40.0" x="478.0" y="197.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="personConfirm" id="BPMNShape_personConfirm">
        <omgdc:Bounds height="78.0" width="125.0" x="290.0" y="179.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="correction" id="BPMNShape_correction">
        <omgdc:Bounds height="55.0" width="105.0" x="446.0" y="310.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endevent1" id="BPMNShape_endevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="890.0" y="199.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusivegateway3" id="BPMNShape_exclusivegateway3">
        <omgdc:Bounds height="40.0" width="40.0" x="788.0" y="197.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="startevent1" id="BPMNShape_startevent1">
        <omgdc:Bounds height="35.0" width="35.0" x="151.0" y="201.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow5" id="BPMNEdge_flow5">
        <omgdi:waypoint x="498.0" y="237.0"></omgdi:waypoint>
        <omgdi:waypoint x="498.0" y="310.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="16.0" width="32.0" x="508.0" y="259.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow6" id="BPMNEdge_flow6">
        <omgdi:waypoint x="415.0" y="218.0"></omgdi:waypoint>
        <omgdi:waypoint x="478.0" y="217.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow7" id="BPMNEdge_flow7">
        <omgdi:waypoint x="828.0" y="217.0"></omgdi:waypoint>
        <omgdi:waypoint x="890.0" y="216.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="16.0" width="32.0" x="837.0" y="217.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow9" id="BPMNEdge_flow9">
        <omgdi:waypoint x="808.0" y="237.0"></omgdi:waypoint>
        <omgdi:waypoint x="807.0" y="337.0"></omgdi:waypoint>
        <omgdi:waypoint x="551.0" y="337.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="16.0" width="32.0" x="819.0" y="274.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow10" id="BPMNEdge_flow10">
        <omgdi:waypoint x="186.0" y="218.0"></omgdi:waypoint>
        <omgdi:waypoint x="290.0" y="218.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow11" id="BPMNEdge_flow11">
        <omgdi:waypoint x="518.0" y="217.0"></omgdi:waypoint>
        <omgdi:waypoint x="592.0" y="217.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="16.0" width="32.0" x="529.0" y="218.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow12" id="BPMNEdge_flow12">
        <omgdi:waypoint x="717.0" y="217.0"></omgdi:waypoint>
        <omgdi:waypoint x="788.0" y="217.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow13" id="BPMNEdge_flow13">
        <omgdi:waypoint x="446.0" y="337.0"></omgdi:waypoint>
        <omgdi:waypoint x="352.0" y="337.0"></omgdi:waypoint>
        <omgdi:waypoint x="352.0" y="257.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>