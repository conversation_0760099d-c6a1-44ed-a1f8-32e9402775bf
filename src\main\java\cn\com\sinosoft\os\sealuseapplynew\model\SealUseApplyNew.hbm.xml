<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="cn.com.sinosoft.os.sealuseapplynew.model.SealUseApplyNew" table="OS_SEAL_USE_APPLY_NEW">
		<id name="id" column="ID" type="java.lang.String">
		</id>
		<property name="taskName" column="TASK_NAME" type="java.lang.String"/>
		<property name="urgencyDegree" column="URGENCY_DEGREE" type="java.lang.String"/>
		<property name="useSealDesc" column="USE_SEAL_DESC" type="java.lang.String"/>
		<property name="useSealNum" column="USE_SEAL_NUM" type="java.lang.String"/>
		<property name="sendToUnit" column="SEND_TO_UNIT" type="java.lang.String"/>
		<property name="piId" column="PI_ID" type="java.lang.String"/>
		<property name="auditState" column="AUDIT_STATE" type="java.lang.String"/>
		<property name="addZone" column="ADD_ZONE" type="java.lang.String"/>
		<property name="addOrg" column="ADD_ORG" type="java.lang.String"/>
		<property name="addDep" column="ADD_DEP" type="java.lang.String"/>
		<property name="addUser" column="ADD_USER" type="java.lang.String"/>
		<property name="addTime" column="ADD_TIME" type="java.util.Date"/>
		<property name="modyZone" column="MODY_ZONE" type="java.lang.String"/>
		<property name="modyOrg" column="MODY_ORG" type="java.lang.String"/>
		<property name="modyDep" column="MODY_DEP" type="java.lang.String"/>
		<property name="modyUser" column="MODY_USER" type="java.lang.String"/>
		<property name="modyTime" column="MODY_TIME" type="java.util.Date"/>
		<property name="state" column="STATE" type="java.lang.String"/>
		<property name="appPerson" column="APP_PERSON" type="java.lang.String"/>
		<property name="dataSource" column="DATA_SOURCE" type="java.lang.String"/>
		<property name="dataModyTime" column="DATA_MODY_TIME" type="java.util.Date"/>
		<property name="sealType" column="SEAL_TYPE" type="java.lang.String"/>
		<property name="tmpModelId" column="TMPMODELID" type="java.lang.String"/>
		<property name="competentDep" column="COMPETENT_DEP" type="java.lang.String"/>
		<property name="isSeal" column="IS_SEAL" type="java.lang.String"/>
		
		<!-- 新加字段 -->
		<property name="applyReasonType" column="APPLY_REASON_TYPE" type="java.lang.String"/>
		<property name="detailContent" column="DETAIL_CONTENT" type="java.lang.String"/>
		<property name="isFile" column="IS_FILE" type="java.lang.String"/>
		<property name="chapterType" column="CHAPTER_TYPE" type="java.lang.String"/>
		<property name="isRide" column="IS_RIDE" type="java.lang.String"/>
		<property name="rideNumber" column="RIDE_NUMBER" type="java.lang.String"/>
		<property name="phoneNumber" column="PHONE_NUMBER" type="java.lang.String"/>
		<property name="applyType" column="APPLY_TYPE" type="java.lang.String"/>
		<property name="mentorName" column="MENTOR_NAME" type="java.lang.String"/>
		<property name="isBothOther" column="IS_BOTH_OTHER" type="java.lang.String"/>
		<property name="printName" column="PRINT_NAME" type="java.lang.String"/>
		<property name="otherSeal" column="OTHER_SEAL" type="java.lang.String"/>
		<property name="leaderAudit" column="LEADER_AUDIT" type="java.lang.String"/>
		<property name="executeState" column="EXECUTE_STATE" type="java.lang.String"/>
		<property name="leader" column="LEADER" type="java.lang.String"/>
		<property name="sealNumber" column="SEAL_NUMBER" type="java.lang.String"/>
		<property name="corporateSealNumber" column="CORPORATE_SEAL_NUMBER" type="java.lang.String"/>
		<property name="officialNumber" column="OFFICIAL_NUMBER" type="java.lang.String"/>
		<property name="partyNumber" column="PARTY_NUMBER" type="java.lang.String"/>
	</class>
</hibernate-mapping>