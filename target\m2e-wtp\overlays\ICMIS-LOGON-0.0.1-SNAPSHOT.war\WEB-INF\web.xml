<?xml version="1.0" encoding="UTF-8"?>
<web-app id="WebApp_ID" version="2.4"
	xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">
	<display-name>ICMIS</display-name>

	<!-- context param start -->

	<context-param>
		<param-name>csstheme</param-name>
		<param-value>simplicity</param-value>
	</context-param>
	<!-- Define the basename for a resource bundle for I18N -->
	<context-param>
		<param-name>javax.servlet.jsp.jstl.fmt.localizationContext</param-name>
		<param-value>ApplicationResources</param-value>
	</context-param>

	<context-param>
		<param-name>overtime</param-name>
		<param-value>300</param-value>
	</context-param>

	<!-- Fallback locale if no bundles found for browser's preferred locale -->
	<!-- Force a single locale using param-name 'javax.servlet.jsp.jstl.fmt.locale' -->
	<context-param>
		<param-name>javax.servlet.jsp.jstl.fmt.fallbackLocale</param-name>
		<param-value>en</param-value>
	</context-param>
	<!-- Context Configuration locations for Spring XML files -->
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			classpath*:applicationContext*.xml,
			classpath*:/applicationContext*.xml,
			/WEB-INF/applicationContext*.xml,
			classpath*:**/applicationContext*.xml,
			classpath:activiti-context.xml
		</param-value>
	</context-param>
	<context-param>
		<param-name>extremecomponentsPreferencesLocation</param-name>
		<param-value>/ec.properties</param-value>
	</context-param>
	<!-- context param end -->

	<!-- filter start -->
	<!-- 该过滤器用于实现单点登出功能，可选配置。 -->
<!-- 	<filter> -->
<!-- 		<filter-name>CAS Single Sign Out Filter</filter-name> -->
<!-- 		<filter-class>org.jasig.cas.client.session.SingleSignOutFilter</filter-class> -->
<!-- 	</filter> -->
		<!-- 该过滤器负责用户的认证工作，必须启用它 -->
<!-- 	<filter> -->
<!-- 		<filter-name>CAS Authentication Filter</filter-name> -->
<!-- 		<filter-class>org.jasig.cas.client.authentication.AuthenticationFilter</filter-class> -->
<!-- 		<init-param> -->
<!-- 			<param-name>casServerLoginUrl</param-name> -->
<!-- 			<param-value>http://124.205.5.138:82/cas/login</param-value> -->
<!-- 		</init-param> -->
 		<!-- <init-param> <param-name>renew</param-name> <param-value>false</param-value>  -->
<!-- 			</init-param> -->  
<!-- 		<init-param> -->
<!-- 			<param-name>gateway</param-name> -->
<!-- 			<param-value>false</param-value> -->
<!-- 		</init-param> -->
<!-- 		<init-param> -->
<!-- 			<param-name>serverName</param-name> -->
<!-- 			<param-value>http://172.16.0.24:8080</param-value> -->
<!-- 		</init-param> -->
<!-- 	</filter> -->
	 

	<filter>
		<filter-name>saveUserInfo2Thread</filter-name>
		<filter-class>ie.bsp.modules.auth.filter.UserThreadLocalFilter</filter-class>
	</filter>
	<filter>
		<filter-name>eXtremeExport</filter-name>
		<filter-class>ie.bsp.ui.table.filter.ExportFilter</filter-class>
	</filter>
	<filter>
		<filter-name>cacheFilter</filter-name>
		<filter-class>com.opensymphony.oscache.web.filter.CacheFilter</filter-class>
	</filter>
	<filter>
		<filter-name>encodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
		<init-param>
			<param-name>forceEncoding</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter>
		<filter-name>lazyLoadingFilter</filter-name>
		<filter-class>
			org.springframework.orm.hibernate3.support.OpenSessionInViewFilter</filter-class>
	</filter>
	<filter>
		<filter-name>struts</filter-name>
		<filter-class>
			org.apache.struts2.dispatcher.ng.filter.StrutsPrepareAndExecuteFilter</filter-class>
		<init-param>
			<param-name>actionPackages</param-name>
			<param-value>com.*.*.*.controler</param-value>
		</init-param>
	</filter>
	<!-- 增加在service层获取Session数据的方法 -->
	<filter>
		<filter-name>servletContextThreadLocalFilter</filter-name>
		<filter-class>ie.bsp.core.filters.ServletContextThreadLocalFilter</filter-class>
	</filter>

	<!-- filter end -->


	<!-- filter-mapping start -->
	<filter-mapping>
		<filter-name>encodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<filter-mapping>
		<filter-name>saveUserInfo2Thread</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>eXtremeExport</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>lazyLoadingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>struts</filter-name>
		<url-pattern>*.ac</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>struts</filter-name>
		<url-pattern>*.jsp</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>struts</filter-name>
		<url-pattern>*.inc</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>servletContextThreadLocalFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>




	 


	<!-- listener start -->
	<listener>
		<listener-class>ie.listenerss.UserListeners</listener-class>
	</listener>
	<listener>
		<listener-class>
			ie.bsp.spring.container.ContainerContextLoaderListener</listener-class>
	</listener>
	<listener>
		<listener-class>
			org.springframework.web.util.IntrospectorCleanupListener</listener-class>
	</listener>
	<listener>
		<listener-class>
			org.springframework.web.context.request.RequestContextListener</listener-class>
	</listener>
	<!-- listener end -->


	<!-- servlet start -->
	<!--Proxool连接池关闭的问题 -->
	<servlet>
		<servlet-name>proxoolServlet</servlet-name>
		<servlet-class>ie.bsp.config.ProxoolServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>
	
	
	
	
	<!-- 连接池监控 -->
	<servlet>
		<servlet-name>Admin</servlet-name>
		<servlet-class>org.logicalcobwebs.proxool.admin.servlet.AdminServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>Admin</servlet-name>
		<url-pattern>/Admin</url-pattern>
	</servlet-mapping>
	
	
	<!-- 图片上传 -->
<servlet>
	<servlet-name>uploadPhotoServlet</servlet-name>
	<servlet-class>ie.js.upload.UploadPhotoServlet</servlet-class>
</servlet>
<!-- 图片裁剪 -->
<servlet>
	<servlet-name>jcropPhotoServlet</servlet-name>
	<servlet-class>ie.js.upload.JcropPhotoServlet</servlet-class>
</servlet>
<!-- 图片删除 -->
<servlet>
	<servlet-name>delPhotoServlet</servlet-name>
	<servlet-class>ie.js.upload.DelPhotoServlet</servlet-class>
</servlet>
<!-- 文件上传到共享目录 -->
<servlet>
	<servlet-name>smbUploadifyFileUploadServlet</servlet-name>
	<servlet-class>ie.js.upload.SmbUploadifyFileUploadServlet</servlet-class>
</servlet>
<!-- 共享目录文件下载 -->
<servlet>
	<servlet-name>smbFileDownloadServlet</servlet-name>
	<servlet-class>ie.js.upload.SmbFileDownloadServlet</servlet-class>
</servlet>
<!-- 共享目录文件删除 -->
<servlet>
	<servlet-name>smbFileDeleteServlet</servlet-name>
	<servlet-class>ie.js.upload.SmbFileDeleteServlet</servlet-class>
</servlet>		


<servlet-mapping>
	<servlet-name>uploadPhotoServlet</servlet-name>
	<url-pattern>/uploadPhotoServlet</url-pattern>
</servlet-mapping>
<servlet-mapping>
	<servlet-name>jcropPhotoServlet</servlet-name>
	<url-pattern>/jcropPhotoServlet</url-pattern>
</servlet-mapping>
<servlet-mapping>
	<servlet-name>delPhotoServlet</servlet-name>
	<url-pattern>/delPhotoServlet</url-pattern>
</servlet-mapping>
<servlet-mapping>
	<servlet-name>smbUploadifyFileUploadServlet</servlet-name>
	<url-pattern>/smbUploadifyFileUploadServlet</url-pattern>
</servlet-mapping>
<servlet-mapping>
	<servlet-name>smbFileDownloadServlet</servlet-name>
	<url-pattern>/smbFileDownloadServlet</url-pattern>
</servlet-mapping>
<servlet-mapping>
	<servlet-name>smbFileDeleteServlet</servlet-name>
	<url-pattern>/smbFileDeleteServlet</url-pattern>
</servlet-mapping>

	<servlet>
		<servlet-name>dwr-invoker</servlet-name>
		<servlet-class>org.directwebremoting.servlet.DwrServlet</servlet-class>
		<init-param>
			<param-name>debug</param-name>
			<param-value>true</param-value>
		</init-param>
		<init-param>
			<param-name>logLevel</param-name>
			<param-value>warn</param-value>
		</init-param>
		<init-param>
			<param-name>crossDomainSessionSecurity</param-name>
			<param-value>false</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<!-- 动态树组件映射 -->
	<servlet>
		<servlet-name>dynamicTreeProcessor</servlet-name>
		<servlet-class>ie.bsp.modules.tree.servlet.DynamicTreeServlet</servlet-class>
	</servlet>
	<servlet>
		<servlet-name>ImageServlet</servlet-name>
		<servlet-class>ie.bsp.ui.login.ImageServlet</servlet-class>
	</servlet>
	<servlet>
		<servlet-name>TalkServlet</servlet-name>
		<servlet-class>ie.js.talk.MessageServlet</servlet-class>
	</servlet>
	<servlet>
		<servlet-name>MessageServlet</servlet-name>
		<servlet-class>ie.js.talk.MessageServlet</servlet-class>
	</servlet>
	<servlet>
		<servlet-name>CXFServlet</servlet-name>
		<servlet-class>org.apache.cxf.transport.servlet.CXFServlet</servlet-class>
	</servlet>
	<servlet>
		<servlet-name>RandomServlet</servlet-name>
		<servlet-class>cn.com.sinosoft.login.servlet.RandomServlet</servlet-class>
		<init-param>
			<param-name>url</param-name>
			<param-value>login/login.jsp</param-value>
		</init-param>
	</servlet>
	<servlet>
		<servlet-name>AuthenServlet</servlet-name>
		<servlet-class>cn.com.sinosoft.login.servlet.AuthenServlet</servlet-class>
		<init-param>
			<param-name>successUrl</param-name>
			<param-value>index.jsp</param-value>
		</init-param>
		<init-param>
			<param-name>errorUrl</param-name>
			<param-value>login/login.jsp</param-value>
		</init-param>
	</servlet>
	<servlet>
		<servlet-name>pushlet</servlet-name>
		<servlet-class>nl.justobjects.pushlet.servlet.Pushlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet>
		<display-name>MqServlet</display-name>
		<servlet-name>MqServlet</servlet-name>
		<servlet-class>cn.com.sinosoft.mq.MqServlet</servlet-class>
		<load-on-startup>2</load-on-startup>
	</servlet>
	<!-- servlet end -->


	<!-- servlet-mapping start -->
	<servlet-mapping>
		<servlet-name>dwr-invoker</servlet-name>
		<url-pattern>/dwr/*</url-pattern>
	</servlet-mapping>
	<!-- 动态树组件映射 -->
	<servlet-mapping>
		<servlet-name>dynamicTreeProcessor</servlet-name>
		<url-pattern>/dynamicTree/*</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ImageServlet</servlet-name>
		<url-pattern>/ImageServlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>TalkServlet</servlet-name>
		<url-pattern>/talkonline</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>MessageServlet</servlet-name>
		<url-pattern>/messageonline</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>CXFServlet</servlet-name>
		<url-pattern>/ws/*</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>RandomServlet</servlet-name>
		<url-pattern>/random</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>AuthenServlet</servlet-name>
		<url-pattern>/auth</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>pushlet</servlet-name>
		<url-pattern>/pushlet.srv</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>MqServlet</servlet-name>
		<url-pattern>/MqServlet</url-pattern>
	</servlet-mapping>

	<!-- servlet-mapping end -->

	<!-- 设置session失效时间 -->
	<session-config>
		<session-timeout>480</session-timeout>
	</session-config>
	<mime-mapping>
		<extension>xls</extension>
		<mime-type>application/msexcel</mime-type>
	</mime-mapping>
	<welcome-file-list>
		<welcome-file>random</welcome-file>
	</welcome-file-list>
	<error-page>
		<error-code>403</error-code>
		<location>/common/403.jsp</location>
	</error-page>
	<error-page>
		<error-code>404</error-code>
		<location>/common/404.jsp</location>
	</error-page>

	<!-- taglib start -->
	<!--标签的配置 -->
	<jsp-config>
		<taglib>
			<taglib-uri>/ec</taglib-uri>
			<taglib-location>/WEB-INF/tld/ec.tld</taglib-location>
		</taglib>
		<taglib>
			<taglib-uri>/bsp-html</taglib-uri>
			<taglib-location>/WEB-INF/tld/bsp-html.tld</taglib-location>
		</taglib>
		<taglib>
			<taglib-uri>/permission-func</taglib-uri>
			<taglib-location>/WEB-INF/tld/permission-func.tld</taglib-location>
		</taglib>
		<taglib>
			<taglib-uri>/oscache</taglib-uri>
			<taglib-location>/WEB-INF/tld/oscache.tld</taglib-location>
		</taglib>
	</jsp-config>
</web-app>