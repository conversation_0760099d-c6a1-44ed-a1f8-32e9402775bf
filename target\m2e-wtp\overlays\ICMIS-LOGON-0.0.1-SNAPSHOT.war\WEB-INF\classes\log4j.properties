# Sample log4j config, adapt to your own logging needs
# $Id: log4j.properties,v 1.1 2007/12/07 12:57:40 justb Exp $
#
# ***** Set root logger level to WARN and its two appenders to stdout and R.
log4j.rootLogger=warn, stdout
# ***** stdout is set to be a ConsoleAppender.
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
# ***** stdout uses PatternLayout.
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
# ***** Pattern to output the caller's file name and line number.
log4j.appender.stdout.layout.ConversionPattern=%d{yy-MM-dd@HH:mm:ss} %-5p - %m%n

# ***** R is set to be a RollingFileAppender.
# log4j.appender.R=org.apache.log4j.RollingFileAppender
# log4j.appender.R.File=/Users/<USER>/example.log
# ***** Max file size is set to 100KB
# log4j.appender.R.MaxFileSize=100KB
# ***** Keep one backup file
# log4j.appender.R.MaxBackupIndex=1
# ***** R uses PatternLayout.
# log4j.appender.R.layout=org.apache.log4j.PatternLayout
# log4j.appender.R.layout.ConversionPattern=%p %t %c - %m%n
