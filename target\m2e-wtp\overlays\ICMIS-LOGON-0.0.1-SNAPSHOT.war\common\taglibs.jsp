
<%@page import="ie.bsp.core.bean.UserView"%>
<%@page import="ie.bsp.ui.FrameConstant"%>
<%@ taglib uri="http://java.sun.com/jstl/core_rt" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jstl/fmt_rt" prefix="fmt"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="/bsp-html" prefix="bspHtml"%>
<%@ taglib uri="/permission-func" prefix="perm" %>
<%@ page isELIgnored="false" %>
<c:set var="ctx" value="${pageContext.request.contextPath}" />	 
<%@ taglib uri="/oscache" prefix="oscache"%>

<script src="${ctx}/scripts/boot.js" type="text/javascript"></script>
<script src="${ctx}/scripts/core.js" type="text/javascript"></script>
<script src="${ctx}/scripts/util.js" type="text/javascript" charset="utf-8"></script>
<script src="${ctx}/scripts/zoneorg.js" type="text/javascript" charset="utf-8"></script>
<!--<script src="${ctx}/scripts/alert.js" type="text/javascript" charset="utf-8"></script>-->
<link rel="stylesheet" href="${ctx }/css/miniui-diysize.css" type="text/css"></link>
<link rel="stylesheet" href="${ctx }/css/util.css" type="text/css"></link>
<link rel="stylesheet" href="${ctx }/css/tab-1.css" type="text/css"></link>
<link rel="stylesheet" href="${ctx }/css/icons.css" type="text/css"></link>

<%
	UserView taglibs_userview=(UserView)session.getAttribute(FrameConstant.SESSION_USERVIEW);
	if(taglibs_userview==null)
		response.sendRedirect(request.getContextPath()+"/");
%>